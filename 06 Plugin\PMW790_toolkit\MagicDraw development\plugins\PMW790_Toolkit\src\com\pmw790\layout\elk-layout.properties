# ELK Algorithm Registry Configuration
# This file registers ELK layout algorithms for use in the PMW790 toolkit

# Register the layered layout algorithm (main algorithm used)
org.eclipse.elk.layered=org.eclipse.elk.alg.layered.LayeredLayoutProvider
org.eclipse.elk.layered.description=The layer-based method was introduced by <PERSON><PERSON><PERSON>, Tagawa and Toda. Nodes are assigned to layers, which are sometimes called ranks, and then reordered such that the number of edge crossings is minimized. Afterwards, concrete coordinates are computed for the nodes and edge bend points.

# Register force-directed layout algorithm  
org.eclipse.elk.force=org.eclipse.elk.alg.force.ForceLayoutProvider
org.eclipse.elk.force.description=Force-based algorithm that treats edges as springs and nodes as masses. Provides good results for graphs with dense connections.

# Register tree layout algorithm
org.eclipse.elk.mrtree=org.eclipse.elk.alg.mrtree.TreeLayoutProvider
org.eclipse.elk.mrtree.description=A tree layout algorithm that arranges nodes in a tree structure. Good for hierarchical data with clear parent-child relationships.

# Register box layout algorithm for simple arrangements
org.eclipse.elk.box=org.eclipse.elk.alg.common.nodespacing.BoxLayoutProvider
org.eclipse.elk.box.description=Simple box layout that arranges nodes in rectangular patterns. Suitable for simple diagrams.

# Fallback algorithms with qualified names
layered=org.eclipse.elk.alg.layered.LayeredLayoutProvider
force=org.eclipse.elk.alg.force.ForceLayoutProvider
mrtree=org.eclipse.elk.alg.mrtree.TreeLayoutProvider
box=org.eclipse.elk.alg.common.nodespacing.BoxLayoutProvider