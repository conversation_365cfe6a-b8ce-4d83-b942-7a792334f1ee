from core.imports import *
from core.constants import PACKAGE_VIEWS, SYSTEM_NAMING
from utils.md_utils import printer
from utils.dictionary import ModelDictionary as MD
class PartService:
    def __init__(self, project, package_service, creator_service, parts_dict):
        self.project = project
        self.package_service = package_service
        self.creator = creator_service
        self.parts_view = PACKAGE_VIEWS["PARTS"]
        self.parts_package = f.byQualifiedName().find(self.project, self.parts_view)
        self.existing_parts_dict = parts_dict

    def get_system_parts_package(self, system_name):
        """Find existing system parts package"""
        system_parts_name = SYSTEM_NAMING["PARTS"].format(system_name)

        for pkg in self.parts_package.getOwnedMember():
            if isinstance(pkg, Package) and pkg.getName() == system_parts_name:
                return pkg
        return None

    def get_unknown_package(self):
        """Find existing Unknown package"""
        for pkg in self.parts_package.getOwnedMember():
            if isinstance(pkg, Package) and pkg.getName() == "Unknown":
                return pkg
        return None

    def create_part_system_packages(self, system_name):
        """Creates system-specific and unknown packages for parts"""
        # Try to get existing packages first
        system_package = self.get_system_parts_package(system_name)
        unknown_package = self.get_unknown_package()

        # Create if not found
        if not system_package:
            system_parts_name = SYSTEM_NAMING["PARTS"].format(system_name)
            system_package = self.package_service.create_package(system_parts_name, self.parts_package)

        if not unknown_package:
            unknown_package = self.package_service.create_package("Unknown", self.parts_package)

        return system_package, unknown_package

    def create_part_name(self, base_name, system_prefix=None):
        """Generates standardized part name
        Args:
            base_name: Original part name
            system_prefix: Optional system name to prefix
        """
        if system_prefix:
            return "{0}_{1}".format(system_prefix, base_name)
        return base_name

    def create_part_block(self, name, target_package, part_data):
        """Creates or updates part block with properties O(1) average case"""
        material_sbom = name.split("Material_")[-1] if "Material_" in name else None
        in_unknown = target_package.getName() == "Unknown"

        if not material_sbom:
            return None

        # Use cached material location dict - O(1) lookup
        if not hasattr(self, '_material_locations'):
            self._material_locations = {}  # material_sbom -> {package_name: part_name}

            # One-time initialization O(n) but amortized
            for pkg in self.parts_package.getOwnedMember():
                if isinstance(pkg, Package):
                    pkg_name = pkg.getName()
                    for part in pkg.getOwnedElement():
                        if isinstance(part, Element):
                            mat = part.getName().split("Material_")[-1] if "Material_" in part.getName() else None
                            if mat:
                                if mat not in self._material_locations:
                                    self._material_locations[mat] = {}
                                self._material_locations[mat][pkg_name] = part.getName()

        # O(1) lookup if material exists anywhere
        if material_sbom in self._material_locations:
            existing_locations = self._material_locations[material_sbom]

            if in_unknown:
                # If in any system package, don't create in Unknown
                if any(pkg != "Unknown" for pkg in existing_locations):
                    return None
                # If already in Unknown, don't duplicate
                if "Unknown" in existing_locations:
                    return None
            else:
                # If in target system package, don't duplicate
                if target_package.getName() in existing_locations:
                    return None
                # Remove from Unknown if it exists there
                if "Unknown" in existing_locations:
                    unknown_pkg = self.get_unknown_package()
                    part_name = existing_locations["Unknown"]
                    for part in unknown_pkg.getOwnedElement():
                        if part.getName() == part_name:
                            mem.getInstance().removeElement(part)
                            del existing_locations["Unknown"]
                            break

        # Create new part block
        part_block = self.creator.create_IDP_Element(name, 'Part', target_package)
        if part_block:
            self.creator.create_properties(part_block, "Part", part_data)
            # Update cache
            if material_sbom not in self._material_locations:
                self._material_locations[material_sbom] = {}
            self._material_locations[material_sbom][target_package.getName()] = name

        return part_block