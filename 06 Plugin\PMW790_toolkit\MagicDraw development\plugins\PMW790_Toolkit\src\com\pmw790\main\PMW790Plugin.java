package com.pmw790.main;

import com.nomagic.magicdraw.core.Application;
import com.pmw790.functions.ConnectionRegistry;
import com.pmw790.diagram.PowerConnectorManager;
import com.pmw790.schema.BindingSchemaManager;
import com.pmw790.functions.SysMLStereotypes;
import com.pmw790.configurators.BrowserContextConfigurator;
import com.pmw790.configurators.DiagramContextConfigurator;
import com.pmw790.configurators.MainMenuConfigurator;
import com.pmw790.jython.jythonFunctions;
import com.nomagic.magicdraw.plugins.Plugin;
import com.nomagic.magicdraw.actions.ActionsConfiguratorsManager;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.core.project.ProjectEventListenerAdapter;
import com.pmw790.functions.Utilities;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicReference;
import java.io.File;
import java.net.URL;
import org.python.core.PyString;
import org.python.util.PythonInterpreter;
import org.python.core.PySystemState;

public class PMW790Plugin extends Plugin
{
	private final AtomicReference<Project> currentProjectRef = new AtomicReference<>(null);

	private volatile boolean projectSwitchInProgress = false;

	private static final String LOG_PREFIX = "[PMW790Toolkit] ";

	private PythonInterpreter interp;
	private jythonFunctions jythonFunctions;
	private static PMW790Plugin instance;
	private static File pluginDirectory;

    @Override
	public void init()
	{
		instance = this;
		
		initializeJython();

		BrowserContextConfigurator.initialize(jythonFunctions);
		MainMenuConfigurator.initialize(jythonFunctions);
		
		// Initialize ELK classloader
		try {
			com.pmw790.layout.ELKClassLoader.reset();
			com.pmw790.layout.ELKClassLoader.initializeELKClassLoader();
		} catch (Exception e) {
			Utilities.Log(LOG_PREFIX + "ELK initialization failed: " + e.getMessage());
		}

		ActionsConfiguratorsManager.getInstance().addContainmentBrowserContextConfigurator(
				BrowserContextConfigurator.INSTANCE);
		ActionsConfiguratorsManager.getInstance().addDiagramContextConfigurator("Any Diagram",
				DiagramContextConfigurator.INSTANCE);
		ActionsConfiguratorsManager.getInstance().addMainMenuConfigurator(
				MainMenuConfigurator.INSTANCE);


		Application.getInstance().getProjectsManager().addProjectListener(
				new ProjectEventListenerAdapter() {
					@Override
					public void projectOpened(Project project) {
						try {
							projectSwitchInProgress = true;

							String projectName = project != null ? project.getName() : "null";

							Project previousProject = currentProjectRef.getAndSet(project);
							String previousProjectName = previousProject != null ? previousProject.getName() : "null";

							if (previousProject != null && previousProject != project) {
								Utilities.showProjectSwitchNotification(previousProjectName, projectName);
							}
							activateProjectCache(project);
						} catch (Exception e) {
							Utilities.Log(LOG_PREFIX + "Error in projectOpened: " + e.getMessage());
							e.printStackTrace();
							clearAllCaches(project);
							try {
								activateProjectCache(project);
							} catch (Exception ex) {
								Utilities.Log(LOG_PREFIX + "Failed to recover: " + ex.getMessage());
							}
						} finally {
							projectSwitchInProgress = false;
						}
					}

					@Override
					public void projectClosed(Project project) {
						try {
							projectSwitchInProgress = true;
							currentProjectRef.set(null);
							String projectName = project != null ? project.getName() : "null";
							performFullCacheClear(project);
						} catch (Exception e) {
							Utilities.Log(LOG_PREFIX + "Error in projectClosed: " + e.getMessage());
							e.printStackTrace();
							clearAllCaches(project);
						} finally {
							projectSwitchInProgress = false;
						}
					}

					private void clearAllCaches(Project specificProject) {
						Project projectToUse = specificProject != null ?
							specificProject : Application.getInstance().getProject();

						Utilities.clearCaches();
						SysMLStereotypes.clearCaches();
						ConnectionRegistry.getInstance().reset();
						BindingSchemaManager.getInstance().clearCache();
						PowerConnectorManager.clearConnectorCache();

						if (projectToUse != null) {
							Utilities.ModelElements.clearProjectCaches(projectToUse);
						}

						System.gc();
					}

					private void clearAllCaches() {
						clearAllCaches(null);
					}

					private void performFullCacheClear(Project oldProject) {
						if (oldProject != null) {
							clearAllCaches(oldProject);

							try {
								Utilities.ModelElements.clearProjectCaches(oldProject);
							} catch (Exception e) {
								Utilities.Log(LOG_PREFIX + "Error clearing project-specific caches: " + e.getMessage());
							}
						} else {
							clearAllCaches();
						}

						SysMLStereotypes.clearCaches();
						System.gc();
						System.gc();

					}

					private void activateProjectCache(Project project) {
						if (project == null) {
							Utilities.Log(LOG_PREFIX + "Cannot activate cache for null project");
							return;
						}
						String projectName = project.getName();
						clearAllCaches(project);
						try {
							SysMLStereotypes.initialize(project);
							Utilities.findPowerBlocks();
							BindingSchemaManager.getInstance().initialize(project);
							ConnectionRegistry.getInstance().analyzeModelConnections(project);
							Utilities.findAndCacheRoomBlocks(project);
						} catch (Exception e) {
							Utilities.Log(LOG_PREFIX + "Error initializing project: " + e.getMessage());
						}
					}

				});
	}

	private void initializeJython() {
		try {
			URL loc = getClass().getProtectionDomain().getCodeSource().getLocation();
			String path = loc.getPath();
			try {
				path = URLDecoder.decode(path, StandardCharsets.UTF_8.toString());
			} catch (Exception e) {
				Utilities.Log("Error decoding path: " + e.getMessage());
			}

			File jarFile = new File(path);
			pluginDirectory = jarFile.getParentFile();

			File jythonFolder = new File(pluginDirectory, "jython_code");
			String jythonFolderPath = jythonFolder.getAbsolutePath();

			if (!jythonFolder.isDirectory()) {
				Utilities.Log(LOG_PREFIX + "Jython folder not found at: " + jythonFolder.getAbsolutePath());
				return;
			}

			interp = new PythonInterpreter();
			interp.getSystemState().path.insert(0, new PyString(jythonFolderPath));
			jythonFunctions = new jythonFunctions(interp, jythonFolderPath);
		} catch (Exception e) {
			Utilities.Log(LOG_PREFIX + "Failed to initialize Jython: " + e.getMessage());
			e.printStackTrace();
		}
	}

	@Override
	public boolean close()
	{
		ActionsConfiguratorsManager.getInstance().removeContainmentBrowserContextConfigurator(
				BrowserContextConfigurator.INSTANCE);
		ActionsConfiguratorsManager.getInstance().removeDiagramContextConfigurator("Any Diagram",
				DiagramContextConfigurator.INSTANCE);
		ActionsConfiguratorsManager.getInstance().removeMainMenuConfigurator(
				MainMenuConfigurator.INSTANCE);

		if (interp != null) {
			try {
				interp.close();
			} catch (Exception e) {
				Utilities.Log(LOG_PREFIX + "Error closing Jython interpreter: " + e.getMessage());
			}
		}
		
		instance = null;
		return true;
	}

	@Override
	public boolean isSupported()
	{
		return true;
	}

	public static Project getCurrentProject() {
		if (instance == null) {
			return Application.getInstance().getProject();
		}
		Project current = instance.currentProjectRef.get();
		return current != null ? current : Application.getInstance().getProject();
	}

	public static File getPluginDirectory() {
		return pluginDirectory;
	}
}