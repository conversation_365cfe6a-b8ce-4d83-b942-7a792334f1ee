from core.imports import *
from core.constants import PACKAGE_VIEWS, SYSTEM_NAMING, REQUIRED_RESOURCES
from utils.md_utils import printer

class PackageService():
    """Service for managing high-level package structure and resources"""
    def __init__(self, project):
        """
        Initialize package service
        Args:
            project: MagicDraw project instance
        """
        self.project = project
        self.element_factory = project.getElementsFactory()

    def create_package(self, category, package):
        """Creates a package via ElementsFactory"""
        new_package = self.element_factory.createPackageInstance()
        new_package.setVisibility(VisibilityKindEnum.getByName("public"))
        new_package.setOwner(package)
        new_package.setName(category)
        return new_package

    def find_package_by_name(self,top_level_package, name):
        """
        Recursively finds a package by name and returns its qualified name

        Args:
            top_level_package: Package to start search from
            name: Name of package to find

        Returns:
            str: Qualified name of found package or None
        """
        package_qn = None
        for packages_ in top_level_package.getOwnedMember():
            if isinstance(packages_, Package):
                if packages_.getName() == name:
                    package_qn = packages_.getQualifiedName()
                    return package_qn
                else:
                    package_qn_sub = self.find_package_by_name(packages_, name)
                    if package_qn_sub:
                        return package_qn_sub
        return package_qn

    def find_or_build_idp_packages(self, base_model):
        """
        Finds or creates the core IDP package structure
        Args:
            base_model: Base MagicDraw model to create packages in

        Returns:
            Tuple of core packages (asset, part, connection, location, system)
        """
        packages = []
        try:
            for name in PACKAGE_VIEWS.values():
                # First try to find existing package
                package_qn = self.find_package_by_name(base_model, name)
                if package_qn:
                    package = f.byQualifiedName().find(self.project, package_qn)
                else:
                    package = self.create_package(name, base_model)
                packages.append(package)

            return tuple(packages)

        except Exception as e:
            printer("Error creating packages: {}".format(str(e)))
            return None

    def find_resources(self, project_models):
        """
        Find resource packages
        Args:
            project_models: Collection of project models to search
    
        Returns:
            tuple: Resources in order (idp_resources, material_package, bcl_package, taxonomy_package)
        """
        resources = {}

        try:
            # Find each resource package
            for models in project_models:
                for resource_name in REQUIRED_RESOURCES.values():
                    if resource_name not in resources:
                        package_qn = self.find_package_by_name(models, resource_name)
                        if package_qn:
                            package = f.byQualifiedName().find(self.project, package_qn)
                            resources[resource_name] = package

            # Verify all required resources found
            missing = [name for name in REQUIRED_RESOURCES.values()
                       if name not in resources]
            if missing:
                printer("Missing required resources: {}".format(missing))
                return None

            return (
                resources[REQUIRED_RESOURCES["IDP_RESOURCES"]],
                resources[REQUIRED_RESOURCES["PARTS_CATALOG"]],
                resources[REQUIRED_RESOURCES["BCL"]],
                resources[REQUIRED_RESOURCES["TAXONOMY"]]
            )
    
        except Exception as e:
            printer("Error finding resources: {}".format(str(e)))
        return None

    def find_system_element(self, view_package, system_name, element_type="package"):
        """
        Find existing system element in different views

        Args:
            view_package: Package object to search in
            system_name: Name of system to find
            element_type: "package" or "block"

        Returns:
            str: Qualified name if found or None
        """
        # Get correct view type from package name
        view_type = None
        for key, value in PACKAGE_VIEWS.items():
            if value == view_package.getName():
                view_type = key
                break

        if not view_type:
            printer("Unknown view type for package: {0}".format(view_package.getName()))
            return None

        search_name = SYSTEM_NAMING[view_type].format(system_name)

        if element_type == "package":
            return self.find_package_by_name(view_package, search_name)
        else:
            for item in view_package.getOwnedMember():
                if (not isinstance(item, Package) and
                        sh.hasStereotype(item, "Block") and
                        item.getName() == search_name):
                    return item.getQualifiedName()
        return None