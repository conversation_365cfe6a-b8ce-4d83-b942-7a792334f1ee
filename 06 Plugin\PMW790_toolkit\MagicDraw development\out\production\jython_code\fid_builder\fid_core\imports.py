"""
Central location for all MagicDraw and common imports.
Import this module using: from core.imports import *
"""

# MagicDraw Core APIs
from com.nomagic.magicdraw.core import Application
from com.nomagic.magicdraw.openapi.uml import (
    ModelElementsManager as mem,
    SessionManager as sm
)
from com.nomagic.text.html import HtmlTextUtils

# MagicDraw Element Types
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import (
    Element,
    Package,
    Property,
    Class,
    AggregationKindEnum,
    VisibilityKindEnum
)

# MagicDraw Helpers & Utils
from com.nomagic.magicdraw.uml import Finder as f
from com.nomagic.uml2.ext.jmi.helpers import (
    StereotypesHelper as sh,
    <PERSON><PERSON>elper as mh
)

# Profile & Stereotype Support
from com.nomagic.magicdraw.sysml.util import SysMLProfile
from com.nomagic.uml2.ext.magicdraw.mdprofiles import Stereotype

# Python Standard Library
import json
import os, sys
from functools import reduce
from itertools import combinations

__all__ = [
    'Application', 'mem', 'sm', 'HtmlTextUtils',
    'Element', 'Package', 'Property', 'Class',
    'AggregationKindEnum', 'VisibilityKindEnum',
    'f', 'sh', 'mh',
    'SysMLProfile', 'Stereotype',
    'json', 'os', 'sys', 'reduce', 'combinations'
]