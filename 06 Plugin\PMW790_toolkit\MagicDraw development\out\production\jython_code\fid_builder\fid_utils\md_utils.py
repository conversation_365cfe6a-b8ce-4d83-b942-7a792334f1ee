from fid_core.imports import *
from com.nomagic.magicdraw.openapi.uml import PresentationElementsManager, SessionManager

def printer(e):
    """Helper function that prints to the MagicDraw GUI Log.

        Parameters
        ----------
        e : AnyType
            Element that will be printed
    """
    printer = Application.getInstance().getGUILog()
    printer.log(str(e))

def safe_session_start(session_name):
    """Start a session if one is not already active.
    
    Parameters
    ----------
    session_name : str
        Name for the session
        
    Returns
    -------
    bool
        True if session was started, False if already active
    """
    session_mgr = SessionManager.getInstance()
    if not session_mgr.isSessionCreated():
        session_mgr.createSession(session_name)
        return True
    return False

def safe_session_end():
    """End a session if one is currently active.
    
    Returns
    -------
    bool
        True if session was ended, False if none was active
    """
    session_mgr = SessionManager.getInstance()
    if session_mgr.isSessionCreated():
        session_mgr.closeSession()
        return True
    return False

def isBlock(ele):
    """Check if element is a Block.
    
    Parameters
    ----------
    ele : Element
        Element to check
        
    Returns
    -------
    bool
        True if element is a Block
    """
    if not isinstance(ele, Class):
        return False
    
    a_st = sh.getStereotypes(ele)
    for a_st_st in a_st:
        if a_st_st.getName() == "Block":
            return True
    return False

def isPartProperty(element):
    """Check if element is a Part Property.
    
    Parameters
    ----------
    element : Element
        Element to check
        
    Returns
    -------
    bool
        True if element is a Part Property
    """
    if isinstance(element, Property):
        return sh.hasStereotype(element, "PartProperty")
    return False