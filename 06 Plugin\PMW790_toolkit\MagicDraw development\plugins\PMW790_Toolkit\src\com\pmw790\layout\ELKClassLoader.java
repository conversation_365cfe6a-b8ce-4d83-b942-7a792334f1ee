package com.pmw790.layout;

import com.pmw790.functions.Utilities;
import com.pmw790.main.PMW790Plugin;
import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;
public class ELKClassLoader {
    
    private static URLClassLoader elkClassLoader = null;
    private static boolean elkClassLoaderInitialized = false;
    private static final Object initLock = new Object();
    
    public static synchronized void initializeELKClassLoader() {
        if (elkClassLoaderInitialized) {
            return;
        }
        
        synchronized (initLock) {
            if (elkClassLoaderInitialized) {
                return;
            }
            
            // Reset any existing classloader first to avoid conflicts
            if (elkClassLoader != null) {
                try {
                    elkClassLoader.close();
                } catch (Exception e) {
                    Utilities.Log("Warning: Error closing existing ELK classloader: " + e.getMessage());
                }
                elkClassLoader = null;
            }
            
            elkClassLoaderInitialized = true;
            
            try {
                // Get the plugin directory from PMW790Plugin
                File pluginDir = PMW790Plugin.getPluginDirectory();
                if (pluginDir == null) {
                    Utilities.Log("Plugin directory not available from PMW790Plugin");
                    return;
                }
                
                File libDir = new File(pluginDir, "lib");
                
                if (!libDir.exists() || !libDir.isDirectory()) {
                    Utilities.Log("ELK lib directory not found at: " + libDir.getAbsolutePath());
                    return;
                }
                
                // Find ELK JAR files
                List<URL> elkJarUrls = new ArrayList<>();
                File[] jarFiles = libDir.listFiles((dir, name) -> 
                    name.toLowerCase().contains("elk") && name.endsWith(".jar"));
                
                if (jarFiles == null || jarFiles.length == 0) {
                    Utilities.Log("No ELK JAR files found in lib directory: " + libDir.getAbsolutePath());
                    return;
                }

                // Create custom classloader
                for (File jarFile : jarFiles) {
                    URL jarUrl = jarFile.toURI().toURL();
                    elkJarUrls.add(jarUrl);
                }

                URL[] urlArray = elkJarUrls.toArray(new URL[0]);
                ClassLoader parentClassLoader = Thread.currentThread().getContextClassLoader();
                if (parentClassLoader == null) {
                    parentClassLoader = ELKClassLoader.class.getClassLoader();
                }

                elkClassLoader = new URLClassLoader(urlArray, parentClassLoader);

            } catch (Exception e) {
                Utilities.Log("Error initializing ELK ClassLoader: " + e.getMessage());
                e.printStackTrace();
                elkClassLoader = null;
            }
        }
    }

    /**
     * Get the ELK classloader, initializing it if necessary
     */
    private static boolean loggedReturn = false;
    
    public static ClassLoader getELKClassLoader() {
        if (!elkClassLoaderInitialized) {
            Utilities.Log("Initialized ELK Class Loader in getmethod");
            initializeELKClassLoader();
        }

        if (elkClassLoader != null) {
            if (!loggedReturn) {
                loggedReturn = true;
            }
            return elkClassLoader;
        } else {
            Utilities.Log("ELKClassLoader: WARNING - Custom classloader is null, falling back to default classloader");
            return ELKClassLoader.class.getClassLoader();
        }
    }

    /**
     * Reset the classloader - useful for debugging or reinitialization
     */
    public static synchronized void reset() {
        synchronized (initLock) {
            elkClassLoaderInitialized = false;
            loggedReturn = false;
            if (elkClassLoader != null) {
                try {
                    elkClassLoader.close();
                } catch (Exception e) {
                    Utilities.Log("Error closing ELK classloader: " + e.getMessage());
                }
            }
            elkClassLoader = null;
            
            // Force garbage collection to help clear cached classes
            System.gc();
            System.gc();
        }
    }
}