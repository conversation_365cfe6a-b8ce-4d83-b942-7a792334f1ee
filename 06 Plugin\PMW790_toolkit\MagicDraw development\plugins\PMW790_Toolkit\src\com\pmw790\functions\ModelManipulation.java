package com.pmw790.functions;

import java.awt.event.ActionEvent;
import java.io.IOException;

import com.nomagic.magicdraw.actions.MDAction;
import com.nomagic.magicdraw.core.Application;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.openapi.uml.SessionManager;
import com.nomagic.magicdraw.properties.Property;
import com.nomagic.magicdraw.properties.PropertyID;
import com.nomagic.magicdraw.properties.PropertyManager;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.uml2.ext.jmi.helpers.ElementImageHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Image;

public class ModelManipulation {
	public static void changeElementImage(PresentationElement pe) throws IOException {
		SessionManager.getInstance().createSession("Update Image");
		Image image = Application.getInstance().getProject().getElementsFactory().createImageInstance();
		image.setContent(ToolkitUtilities.hexifyImage("/images/frog logo.png"));
		image.setLocation("/images/frog logo.png");
		image.setFormat("PNG");
		
		try {
			if (("Part Property").equals(pe.getElement().getHumanType())){
				ElementImageHelper.setCustomImageInformation(pe.getElement(), image);
				
				Property vis = pe.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE);
				Property vis_2 = pe.getProperty(PropertyID.SUPPRESS_STRUCTURE);
				
				if (!"STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE".equals(vis.getValue()) && !"True".equals(vis_2.getValue())){
					Property new_vis = vis.clone();
					new_vis.setValue("STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE");
					Property new_vis_2 = vis_2.clone();
					new_vis_2.setValue("True");
					PropertyManager pm = new PropertyManager();
					pm.addProperty(new_vis);
					pm.addProperty(new_vis_2);
					PresentationElementsManager.getInstance().setPresentationElementProperties(pe, pm);
				}
			}
			SessionManager.getInstance().closeSession();
		} catch (ReadOnlyElementException e) {
			SessionManager.getInstance().cancelSession();
		}
	}
	
	public static class UpdateImage extends MDAction {
		private PresentationElement pe;
		
		public UpdateImage(PresentationElement pe) {
			super("IBD_IMAGE_UPDATE","Update Image", null, null);
			this.pe = pe;
	}
		public void actionPerformed(ActionEvent e) {
			try {
				ModelManipulation.changeElementImage(pe);
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		}
	}
}
