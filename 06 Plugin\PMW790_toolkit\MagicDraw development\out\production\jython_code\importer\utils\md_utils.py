import os
from com.nomagic.magicdraw.core import Application
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Package, VisibilityKindEnum
from java.util import HashMap

def printer(e):
    """Helper function that prints to the MagicDraw GUI Log.
    Parameters
    ----------
    e : AnyType
        Element that will be printed
    """
    printer = Application.getInstance().getGUILog()
    printer.log(str(e))

def createLitInt(project):
    """Creates Literal Integers for the new term's attributes"""
    litInt = project.getElementsFactory().createLiteralIntegerInstance()
    litInt.setVisibility(VisibilityKindEnum.getByName("public"))
    litInt.setValue(1)
    return litInt

def createLitUnlimNat(project):
    """Creates Literal Unlimited Naturals for the new term's attributes"""
    litUnlim = project.getElementsFactory().createLiteralUnlimitedNaturalInstance()
    litUnlim.setVisibility(VisibilityKindEnum.getByName("public"))
    litUnlim.setValue(1)
    return litUnlim

def load_json_from_folder(folder_path):
    """
    Scans a folder for JSON files and returns a dictionary of file info.

    Args:
        folder_path (str): Path to folder containing JSON files

    Returns:
        dict: Dictionary with structure {filename: {'file_path': full_path}}
    """
    jsons = {}
    for filename in os.listdir(folder_path):
        if filename.endswith('.json'):
            file_path = os.path.join(folder_path, filename)
            base_name = os.path.basename(file_path)
            file_bare_name = os.path.splitext(base_name)[0]
            jsons[file_bare_name] = {'file_path': file_path}
    return jsons

def flatten_dict(d, parent_key='', sep='__'):
    """
    Flattens a nested dictionary with custom separator.

    Args:
        d: Dictionary to flatten
        parent_key: Key of parent dictionary (used in recursion)
        sep: Separator for nested keys

    Returns:
        HashMap: Flattened dictionary with concatenated keys
    """
    items = HashMap()
    for k, v in d.items():
        new_key = parent_key + sep + k if parent_key else k
        if isinstance(v, dict):
            items.putAll(flatten_dict(v, new_key, sep=sep))
        else:
            items.put(new_key, v)
    return items

def build_hierarchy_roots(is_new_asset_set, existing_assets_dict):

    new_asset_to_highest_ancestor = {}
    for asset_id in is_new_asset_set:
        highest_ancestor = ancestry_tracer.find_highest_new_ancestor(
            asset_id, is_new_asset_set, existing_assets_dict
        )
        new_asset_to_highest_ancestor[asset_id] = highest_ancestor

    ancestry_tracer.set_highest_ancestor_cache(new_asset_to_highest_ancestor)
    
    # Root assets are only those that are their own highest ancestor
    true_root_assets = {asset_id for asset_id, highest in new_asset_to_highest_ancestor.items() 
                       if asset_id == highest}
    
    return true_root_assets

class AncestryTracer:
    """
    Consolidated ancestry tracing utility that handles multiple data sources 
    and stopping conditions with efficient caching.
    """
    
    def __init__(self):
        self._cache = {}  # Global cache for all ancestry traces
        self._highest_ancestor_cache = {}  # Cache for highest new ancestor mappings
    
    def trace_ancestry(self, element_id, data_sources, include_self=True, cache_key_prefix=""):
        """
        Universal ancestry tracing function that traverses to the root.
        
        Args:
            element_id (str): ID of the element to trace
            data_sources (list): List of dictionaries to search for parent relationships
            include_self (bool): Whether to include the starting element in the result
            cache_key_prefix (str): Prefix for cache key to avoid collisions
        
        Returns:
            list: Ancestry chain from element to root
        """
        cache_key = "{0}:{1}".format(cache_key_prefix, element_id)
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        ancestry = []
        current_id = element_id
        
        while current_id:
            # Add current element to ancestry
            if include_self or current_id != element_id:
                ancestry.append(current_id)
            
            # Get element data from any available data source
            element_data = None
            for data_source in data_sources:
                if current_id in data_source:
                    element_data = data_source[current_id]
                    break
            
            # If no data found, stop tracing
            if not element_data:
                break
            
            # Get parent ID
            parent_id = element_data.get('parent_composite_element')
            if not parent_id:
                break
                
            current_id = parent_id
        
        # Cache and return result
        self._cache[cache_key] = ancestry
        return ancestry
    
    def get_highest_ancestor(self, asset_id):
        """Get highest ancestor from cache."""
        return self._highest_ancestor_cache.get(asset_id)
    
    def find_highest_new_ancestor(self, asset_id, is_new_asset_set, existing_assets_dict):
        """
        Find the highest ancestor that is marked as 'new' in the hierarchy.
        """
        full_ancestry = self.trace_ancestry(
            asset_id,
            [existing_assets_dict],
            include_self=True,
            cache_key_prefix="new_ancestor"
        )
        
        # Find the highest 'new' ancestor
        highest_new_ancestor = asset_id
        for ancestor_id in full_ancestry:
            if ancestor_id in is_new_asset_set:
                highest_new_ancestor = ancestor_id
        
        return highest_new_ancestor
    
    def trace_full_ancestry(self, element_id, assets_dict, locations_dict=None):
        """
        Full ancestry trace - replacement for trace_ancestry in connection_new.py.
        """
        data_sources = [assets_dict]
        if locations_dict:
            data_sources.append(locations_dict)
        
        return self.trace_ancestry(
            element_id,
            data_sources,
            include_self=True,
            cache_key_prefix="full_ancestry"
        )
    
    def set_highest_ancestor_cache(self, cache_dict):
        """Update the highest ancestor cache from build_hierarchy_roots results."""
        self._highest_ancestor_cache.update(cache_dict)
    
    def clear_cache(self):
        """Clear all cached ancestry traces."""
        self._cache.clear()
        self._highest_ancestor_cache.clear()

# Global instance for easy access
ancestry_tracer = AncestryTracer()