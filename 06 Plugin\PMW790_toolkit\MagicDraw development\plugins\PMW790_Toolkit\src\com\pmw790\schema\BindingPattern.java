package com.pmw790.schema;

/**
 * Represents a single binding connection pattern between source property and target parameter
 * This is used to define how properties should be bound with constraint parameters
 */
public class BindingPattern {

    // Core attributes for property-to-constraint binding
    private final String sourceProperty;      // Name of the source property (e.g., "power_factor")
    private final String targetConstraint;    // Name of the target constraint (e.g., "Power_Capacity")
    private final String targetParameter;     // Name of the target parameter (e.g., "PF")

    // Attributes for constraint-to-constraint binding
    private final boolean isConstraintToConstraint;  // Flag for constraint-to-constraint bindings
    private final String sourceConstraint;           // Source constraint name (used for constraint-to-constraint)
    private final String sourceParameter;            // Source parameter name (used for constraint-to-constraint)

    // Attributes for property bindings (property-to-property and constraint-to-property)
    private final boolean hasTargetProperty;         // Flag for bindings with target properties
    private final String targetProperty;             // Target property name (for property-based targets)

    /**
     * Constructor for property-to-constraint binding pattern
     *
     * @param sourceProperty Name of the source property
     * @param targetConstraint Name of the target constraint
     * @param targetParameter Name of the target parameter
     */
    public BindingPattern(String sourceProperty, String targetConstraint, String targetParameter) {
        this.sourceProperty = sourceProperty;
        this.targetConstraint = targetConstraint;
        this.targetParameter = targetParameter;
        this.isConstraintToConstraint = false;
        this.hasTargetProperty = false;

        // Initialize other fields to avoid null values
        this.sourceConstraint = null;
        this.sourceParameter = null;
        this.targetProperty = null;
    }

    // Property-to-property constructor has been removed

    /**
     * Constructor for constraint-to-constraint binding pattern
     *
     * @param sourceConstraint Name of the source constraint
     * @param sourceParameter Name of the source parameter
     * @param targetConstraint Name of the target constraint
     * @param targetParameter Name of the target parameter
     */
    public BindingPattern(String sourceConstraint, String sourceParameter,
                          String targetConstraint, String targetParameter) {
        this.sourceConstraint = sourceConstraint;
        this.sourceParameter = sourceParameter;
        this.targetConstraint = targetConstraint;
        this.targetParameter = targetParameter;
        this.isConstraintToConstraint = true;
        this.hasTargetProperty = false;

        // Initialize other fields to avoid null values
        this.sourceProperty = null;
        this.targetProperty = null;
    }

    /**
     * Factory method for creating a constraint-to-property binding pattern
     *
     * @param sourceConstraint Name of the source constraint
     * @param sourceParameter Name of the source parameter
     * @param targetProperty Name of the target property
     * @return A new BindingPattern instance
     */
    public static BindingPattern createConstraintToPropertyPattern(
            String sourceConstraint, String sourceParameter, String targetProperty) {

        return new BindingPattern(
                null,           // sourceProperty
                sourceConstraint,
                sourceParameter,
                null,           // targetConstraint
                null,           // targetParameter
                targetProperty,
                true,           // isConstraintToConstraint
                true            // hasTargetProperty
        );
    }

    /**
     * Factory method for normalizing and creating a binding pattern from path strings
     * This centralizes the normalization logic in one place
     *
     * @param sourcePath The source path (property or constraint.parameter)
     * @param targetPath The target path (property or constraint.parameter)
     * @return A new normalized BindingPattern instance
     * @throws IllegalArgumentException if the paths don't represent a valid binding pattern
     */
    public static BindingPattern createFromPaths(String sourcePath, String targetPath) {
        // Validate inputs
        if (sourcePath == null || targetPath == null) {
            throw new IllegalArgumentException("Source or target path is null");
        }

        // Case 1: Both are constraints (constraint-to-constraint)
        if (sourcePath.contains(".") && targetPath.contains(".")) {
            String[] sourceParts = sourcePath.split("\\." , 2);
            String[] targetParts = targetPath.split("\\." , 2);

            if (sourceParts.length == 2 && targetParts.length == 2) {
                return new BindingPattern(
                    sourceParts[0], sourceParts[1], targetParts[0], targetParts[1]);
            }
        }
        // Case 2: Source is property, target is constraint
        else if (!sourcePath.contains(".") && targetPath.contains(".")) {
            String[] targetParts = targetPath.split("\\." , 2);

            if (targetParts.length == 2) {
                // Convert to canonical form (constraint-to-property)
                return createConstraintToPropertyPattern(
                    targetParts[0], targetParts[1], sourcePath);
            }
        }
        // Case 3: Source is constraint, target is property (already canonical)
        else if (sourcePath.contains(".") && !targetPath.contains(".")) {
            String[] sourceParts = sourcePath.split("\\." , 2);

            if (sourceParts.length == 2) {
                return createConstraintToPropertyPattern(
                    sourceParts[0], sourceParts[1], targetPath);
            }
        }

        throw new IllegalArgumentException("Invalid binding path format: " +
            sourcePath + " -> " + targetPath);
    }

    /**
     * Private constructor for factory methods with all parameters
     */
    private BindingPattern(
            String sourceProperty,
            String sourceConstraint,
            String sourceParameter,
            String targetConstraint,
            String targetParameter,
            String targetProperty,
            boolean isConstraintToConstraint,
            boolean hasTargetProperty) {

        this.sourceProperty = sourceProperty;
        this.sourceConstraint = sourceConstraint;
        this.sourceParameter = sourceParameter;
        this.targetConstraint = targetConstraint;
        this.targetParameter = targetParameter;
        this.targetProperty = targetProperty;
        this.isConstraintToConstraint = isConstraintToConstraint;
        this.hasTargetProperty = hasTargetProperty;
    }

    /**
     * Checks if this is a constraint-to-constraint binding
     *
     * @return true if this is a constraint-to-constraint binding
     */
    public boolean isConstraintToConstraint() {
        return isConstraintToConstraint;
    }

    /**
     * Checks if this binding has a target property (rather than target constraint)
     *
     * @return true if this binding targets a property
     */
    public boolean hasTargetProperty() {
        return hasTargetProperty;
    }

    /**
     * Gets the source property name
     *
     * @return The source property name
     */
    public String getSourceProperty() {
        return sourceProperty;
    }

    /**
     * Gets the target constraint name
     *
     * @return The target constraint name
     */
    public String getTargetConstraint() {
        return targetConstraint;
    }

    /**
     * Gets the target parameter name
     *
     * @return The target parameter name
     */
    public String getTargetParameter() {
        return targetParameter;
    }

    /**
     * Gets the target property name
     *
     * @return The target property name
     */
    public String getTargetProperty() {
        return targetProperty;
    }

    /**
     * Gets the source constraint name (for constraint-to-constraint binding)
     *
     * @return The source constraint name
     */
    public String getSourceConstraint() {
        return sourceConstraint;
    }

    /**
     * Gets the source parameter name (for constraint-to-constraint binding)
     *
     * @return The source parameter name
     */
    public String getSourceParameter() {
        return sourceParameter;
    }

    @Override
    public String toString() {
        if (isConstraintToConstraint) {
            if (hasTargetProperty) {
                return String.format("Constraint-to-property binding: %s.%s -> %s",
                        sourceConstraint, sourceParameter, targetProperty);
            } else {
                return String.format("Constraint-to-constraint binding: %s.%s -> %s.%s",
                        sourceConstraint, sourceParameter, targetConstraint, targetParameter);
            }
        } else {
            return String.format("Property-to-constraint binding: %s -> %s.%s",
                    sourceProperty, targetConstraint, targetParameter);
        }
    }
}