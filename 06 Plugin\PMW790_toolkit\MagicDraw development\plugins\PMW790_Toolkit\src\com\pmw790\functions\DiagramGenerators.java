package com.pmw790.functions;

import com.pmw790.functions.ToolkitUtilities;
import com.pmw790.functions.Finders;
import com.nomagic.magicdraw.actions.*;
import com.nomagic.magicdraw.core.Application;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.ModelElementsManager;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.openapi.uml.SessionManager;
import com.nomagic.magicdraw.properties.PropertyID;
import com.nomagic.magicdraw.properties.PropertyManager;
import com.nomagic.magicdraw.sysml.util.SysMLConstants;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.Connector;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdports.Port;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.magicdraw.uml.symbols.shapes.ClassifierView;
import com.nomagic.magicdraw.uml.symbols.shapes.DiagramFrameView;
import java.awt.event.ActionEvent;
import java.util.*;

public class DiagramGenerators {
	private static final String TIMESTAMP_FORMAT = "yyyyMMdd_HHmmss";

	// ========================================
	// CORE UTILITY METHODS
	// ========================================

	private static DiagramPresentationElement findOrCreateDiagram(Project project, Class ownerElement,
			String diagramType, String namePrefix) {
		String elementName = ownerElement.getName();
		DiagramPresentationElement existingDiagram = null;

		Collection<DiagramPresentationElement> diagrams = project.getDiagrams();
		for (DiagramPresentationElement diagram : diagrams) {
			com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram diagramElement =
					(com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram) diagram.getElement();

			if (diagramElement != null &&
				diagramElement.getOwner() == ownerElement &&
				diagram.getDiagramType().getType().equals(diagramType)) {
				
				String diagramName = diagramElement.getName();
				if (diagramName != null && diagramName.contains("_" + namePrefix)) {
					existingDiagram = diagram;
					break;
				}
			}
		}

		if (existingDiagram != null) {
			try {
				ModelElementsManager.getInstance().removeElement(existingDiagram.getElement());
			} catch (Exception e) {
				ToolkitUtilities.Log("Error deleting existing diagram: " + e.getMessage());
			}
		}

		try {
			String timestamp = new java.text.SimpleDateFormat(TIMESTAMP_FORMAT)
					.format(new java.util.Date());
			String diagramName = String.format("%s_%s %s", elementName, namePrefix, timestamp);

			com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Diagram newDiagram =
					ModelElementsManager.getInstance().createDiagram(diagramType, ownerElement);
			newDiagram.setName(diagramName);

			return project.getDiagram(newDiagram);
		} catch (Exception e) {
			ToolkitUtilities.Log("Error creating " + namePrefix + " diagram for " + elementName + ": " + e.getMessage());
			return null;
		}
	}

	public static void ibdCreator(Element element, int layerdepth, boolean showPorts, boolean showClasses, String diagramPrefix) {
		SessionManager.getInstance().createSession("Create IBD Diagram");
		Class nc = (Class) element;
		try {
			Project project = Application.getInstance().getProject();
			DiagramPresentationElement nIbd = findOrCreateDiagram(project, nc,
					SysMLConstants.SYSML_INTERNAL_BLOCK_DIAGRAM, diagramPrefix);

			if (nIbd == null) {
				ToolkitUtilities.Log("Error: Could not create IBD diagram for: " + nc.getName());
				SessionManager.getInstance().cancelSession();
				return;
			}
			List<String> tracker = new ArrayList<>();
			DiagramGenerators.addPartPropertiesToIBD(nc, nIbd, layerdepth, tracker, showPorts, showClasses, true);

			SessionManager.getInstance().closeSession();
			nIbd.layout(true);
			nIbd.open();
		}
		catch (ReadOnlyElementException e) {
			SessionManager.getInstance().cancelSession();
		}
	}

	public static void addPartPropertiesToIBD(Class element, PresentationElement diagram, int level, List<String> tracker, Boolean showPorts, boolean showClasses, Boolean topLevel) throws ReadOnlyElementException {
		if (topLevel) {
			for (Port port : element.getOwnedPort()) {
				//if (!(tracker.contains(port))){
				DiagramPresentationElement dfe = (DiagramPresentationElement) diagram;
				DiagramFrameView df = dfe.getDiagramFrame();
				PresentationElementsManager.getInstance().createShapeElement(((Element) port), df, true);
				//	tracker.add(port);
				//}
			}
		}

		for (Property part : element.getOwnedAttribute()) {
			if (DiagramGenerators.isPartProperty(part)) {
				if (level >=1) {
					PresentationElement outer = PresentationElementsManager.getInstance().createShapeElement(part, diagram, true);
					/*
					ShapeElement shape = (ShapeElement) outer;
					outer.getProperty(PropertyID.SUPPRESS_STRUCTURE)
					shape.getPropertyVal
					outer.getDiagramPresentationElement().getProperty(PropertyID.use_im);
					Image image = Application.getInstance().getProject().getElementsFactory().createImageInstance();
					image.setLocation(null);
					outer.getProperty(PropertyID.stereotype_dis);
					outer.getProperty(StyleConstants.IconElementName)
					PropertyManager pm = new PropertyManager();
					ChoiceProperty property = (ChoiceProperty) outer.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE);
					ChoiceProperty cProp = property.clone();
					cProp.setValue();
					*/
					if (!showClasses) {
						Map<String, Set<String>> classifiers = ToolkitUtilities.buildClassifierLists();
						Set<String> con = classifiers.get("conns");
						List<String> elementClassifiers = ToolkitUtilities.findGeneralizationClasses(part.getType());
						if (ToolkitUtilities.isConnectorStrings(elementClassifiers, con)) {
							com.nomagic.magicdraw.properties.Property property = outer.getProperty(PropertyID.SHOW_OBJECT_CLASS);
							com.nomagic.magicdraw.properties.Property cProp = property.clone();
							cProp.setValue(ClassifierView.DO_NOT_DISPLAY);
							PropertyManager pm = new PropertyManager();
							pm.addProperty(cProp);
							PresentationElementsManager.getInstance().setPresentationElementProperties(outer, pm);
						}
					}

					if (part.getType() instanceof Class) {
						Class partType = (Class) part.getType();
						if (showPorts) {
							if (!partType.getOwnedPort().isEmpty()) {
								for (Port port : partType.getOwnedPort()) {
									//String portName = part.getName()+"::"+port.getName();
									//if (!tracker.contains(portName)) {
									PresentationElementsManager.getInstance().createShapeElement(port, outer, true);
									//	tracker.add(portName);
									//}
								}
							}
						}
						if (level > 1) {
							DiagramGenerators.addPartPropertiesToIBD(partType, outer, level - 1, tracker, showPorts, showClasses, false);
						}
					}
				}
			}
		}
	}

	public static boolean isPartProperty(Property property) {
		if (property instanceof Property) {
			return StereotypesHelper.hasStereotypeOrDerived(property, "PartProperty");
		}
		return false;
	}

	// ========================================
	// UI CLASSES
	// ======================================

	public static class IBD_All extends MDAction {
		private Element selectedElement;
		
		public IBD_All(Element selectedElement) {
			super("IBD_1_ACTION","IBD - All Layers", null, null);
			this.selectedElement = selectedElement;
	}
		public void actionPerformed(ActionEvent e) {
			DiagramGenerators.ibdCreator(selectedElement, 150, true, true, "IBD_All");
		}
	}

	public static class IBD_L1 extends MDAction {
		private Element selectedElement;

		public IBD_L1(Element selectedElement) {
			super("IBD_L1_ACTION","IBD - One Layer", null, null);
			this.selectedElement = selectedElement;
		}
		public void actionPerformed(ActionEvent e) {
			DiagramGenerators.ibdCreator(selectedElement, 1, true, true, "IBD_L1");
		}
	}

	public static class IBD_L2 extends MDAction {
		private Element selectedElement;

		public IBD_L2(Element selectedElement) {
			super("IBD_L2_ACTION","IBD - Two Layers", null, null);
			this.selectedElement = selectedElement;
		}
		public void actionPerformed(ActionEvent e) {
			DiagramGenerators.ibdCreator(selectedElement, 2, true, true, "IBD_L2");
		}
	}

}

