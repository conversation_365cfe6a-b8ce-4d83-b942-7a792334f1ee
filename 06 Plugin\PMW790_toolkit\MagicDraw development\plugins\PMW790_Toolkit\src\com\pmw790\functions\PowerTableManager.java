package com.pmw790.functions;

import com.nomagic.magicdraw.core.Application;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import javax.swing.table.JTableHeader;
import java.awt.*;
import java.awt.event.*;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Arrays;

public class PowerTableManager {
    private static JDialog currentDialog = null;
    private static boolean isTableActive = false;

    public static void closeTable() {
        if (isTableActive && currentDialog != null && currentDialog.isDisplayable()) {
            currentDialog.dispose();
            isTableActive = false;
            currentDialog = null;
        }
    }
    
    /**
     * Calculates and displays the total power consumption for a selected block
     * @param selectedElement The selected cabinet or room element
     */
    public static void calculateTotalPowerConsumption(Element selectedElement) {
        if (selectedElement == null) {
            JOptionPane.showMessageDialog(
                Application.getInstance().getMainFrame(),
                "No element selected.",
                "Power Consumption Calculator",
                JOptionPane.WARNING_MESSAGE
            );
            return;
        }

        try {
            String elementName = "";
            if (selectedElement instanceof NamedElement) {
                elementName = ((NamedElement) selectedElement).getName();
            }

            // Check if element is a valid block
            if (!Utilities.ModelElements.isBlock(selectedElement)) {
                JOptionPane.showMessageDialog(
                    Application.getInstance().getMainFrame(),
                    "Selected element '" + elementName + "' is not a valid block.",
                    "Power Consumption Calculator",
                    JOptionPane.WARNING_MESSAGE
                );
                return;
            }

            List<PowerConsumerInfo> powerConsumers = new ArrayList<>();
            double totalPowerConsumption = 0.0;

            // Get power consumers from the selected context
            if (selectedElement instanceof Class) {
                Class blockElement = (Class) selectedElement;
                
                // Check if it's a Cabinet or Room
                String powerType = null;
                if (Utilities.ModelElements.isRoom(selectedElement)) {
                    powerType = "Room";
                } else {
                    // Check if it's a cabinet by examining its classifier
                    Utilities.ClassifierInfo classifierInfo = Utilities.ModelElements.findBaseClassifierInfo(blockElement);
                    if (classifierInfo != null) {
                        String classifier = classifierInfo.getClassifierName();
                        if (Utilities.TYPE_CABINET.equals(Utilities.getPowerType(classifier))) {
                            powerType = "Cabinet";
                        }
                    }
                }

                if (powerType == null) {
                    JOptionPane.showMessageDialog(
                        Application.getInstance().getMainFrame(),
                        "Selected element '" + elementName + "' is not a valid Location or Cabinet block.",
                        "Power Consumption Calculator",
                        JOptionPane.WARNING_MESSAGE
                    );
                    return;
                }

                // Find all power consumers using context-based approach
                Map<String, Property> powerConsumerProperties = new HashMap<>();
                
                if ("Room".equals(powerType)) {
                    // For rooms, get both non-cabinet and cabinet properties
                    String roomName = elementName;
                    Map<String, Property> roomNonCabinetProps = Utilities.getRoomNonCabinetProperties(roomName);
                    Map<String, Property> roomCabinetProps = Utilities.getRoomCabinetProperties(roomName);
                    
                    // Add room-level power consumers (non-cabinet properties)
                    if (roomNonCabinetProps != null) {
                        powerConsumerProperties.putAll(roomNonCabinetProps);
                    }
                    
                    // Add cabinet-level power consumers within the room
                    if (roomCabinetProps != null) {
                        for (Map.Entry<String, Property> cabinetEntry : roomCabinetProps.entrySet()) {
                            String cabinetName = cabinetEntry.getKey();
                            Map<String, Property> cabinetProperties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);
                            if (cabinetProperties != null) {
                                // Filter for power consumers only
                                ConnectionRegistry registry = ConnectionRegistry.getInstance();
                                for (Map.Entry<String, Property> propEntry : cabinetProperties.entrySet()) {
                                    String propName = propEntry.getKey();
                                    String elementType = registry.getElementType(propName);
                                    if (Utilities.TYPE_POWER_CONSUMER.equals(elementType)) {
                                        powerConsumerProperties.put(propName, propEntry.getValue());
                                    }
                                }
                            }
                        }
                    }
                } else if ("Cabinet".equals(powerType)) {
                    // For cabinets, get properties from cabinet cache
                    String cabinetName = elementName;
                    Map<String, Property> cabinetProperties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);
                    if (cabinetProperties != null) {
                        // Filter for power consumers only
                        ConnectionRegistry registry = ConnectionRegistry.getInstance();
                        for (Map.Entry<String, Property> entry : cabinetProperties.entrySet()) {
                            String propName = entry.getKey();
                            String elementType = registry.getElementType(propName);
                            if (Utilities.TYPE_POWER_CONSUMER.equals(elementType)) {
                                powerConsumerProperties.put(propName, entry.getValue());
                            }
                        }
                    }
                }
                
                // Process all found power consumer properties
                for (Map.Entry<String, Property> entry : powerConsumerProperties.entrySet()) {
                    String propertyName = entry.getKey();
                    Property property = entry.getValue();
                    
                    if (property.getType() instanceof Class) {
                        Class propertyType = (Class) property.getType();
                        
                        // Get the classifier info for this property
                        Utilities.ClassifierInfo propClassifierInfo = Utilities.ModelElements.findBaseClassifierInfo(propertyType);
                        if (propClassifierInfo != null) {
                            String propClassifier = propClassifierInfo.getClassifierName();
                            
                            // Get power consumption value
                            Set<String> propertiesToFind = new HashSet<>();
                            propertiesToFind.add("power_consumption");
                            
                            Map<String, Property> foundProps = Utilities.ModelElements.findPropertiesInHierarchy(propertyType, propertiesToFind);
                            Property powerConsumptionProp = foundProps.get("power_consumption");
                            
                            if (powerConsumptionProp != null) {
                                Object powerValue = Utilities.ModelElements.getPropertyValue(powerConsumptionProp);
                                double powerConsumption = 0.0;
                                
                                if (powerValue instanceof Number) {
                                    powerConsumption = ((Number) powerValue).doubleValue();
                                } else if (powerValue instanceof String) {
                                    String stringValue = (String) powerValue;
                                    if ("None".equals(stringValue)) {
                                        powerConsumption = 0.0;
                                    } else {
                                        try {
                                            powerConsumption = Double.parseDouble(stringValue);
                                        } catch (NumberFormatException e) {
                                            Utilities.Log("Warning: Could not parse power consumption value '" + powerValue + "' for " + propertyName);
                                        }
                                    }
                                }
                                
                                powerConsumers.add(new PowerConsumerInfo(propertyName, propClassifier, powerConsumption));
                                totalPowerConsumption += powerConsumption;
                            }
                        }
                    }
                }
            }

            // Display results
            displayPowerConsumptionResults(elementName, powerConsumers, totalPowerConsumption);

        } catch (Exception e) {
            Utilities.Log("Error calculating power consumption: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(
                Application.getInstance().getMainFrame(),
                "Error calculating power consumption: " + e.getMessage(),
                "Power Consumption Calculator Error",
                JOptionPane.ERROR_MESSAGE
            );
        }
    }

    /**
     * Displays the power consumption calculation results in a table dialog
     */
    private static void displayPowerConsumptionResults(String contextName, List<PowerConsumerInfo> powerConsumers, double totalPowerConsumption) {
        SwingUtilities.invokeLater(() -> {
            try {
                // Create table model
                DefaultTableModel model = new DefaultTableModel() {
                    @Override
                    public boolean isCellEditable(int row, int column) {
                        return false;
                    }
                };

                // Add columns
                model.addColumn("Power Consumer");
                model.addColumn("Type");
                model.addColumn("Power Consumption (W)");

                // Add data
                for (PowerConsumerInfo consumer : powerConsumers) {
                    model.addRow(new Object[]{
                        consumer.getName(),
                        consumer.getType(),
                        consumer.getPowerConsumption()
                    });
                }


                // Create table
                JTable table = new JTable(model);
                table.setFillsViewportHeight(true);
                table.setRowHeight(25);
                table.getTableHeader().setReorderingAllowed(false);
                
                // Add sorting and filtering capabilities
                TableRowSorter<DefaultTableModel> sorter = new TableRowSorter<>(model);
                table.setRowSorter(sorter);
                
                // Custom comparator for Power Consumption column to handle numeric sorting
                sorter.setComparator(2, new java.util.Comparator<Object>() {
                    @Override
                    public int compare(Object o1, Object o2) {
                        if (o1 == null && o2 == null) return 0;
                        if (o1 == null) return -1;
                        if (o2 == null) return 1;
                        
                        // Handle empty strings and "TOTAL" row
                        String s1 = o1.toString().trim();
                        String s2 = o2.toString().trim();
                        
                        if (s1.isEmpty() && s2.isEmpty()) return 0;
                        if (s1.isEmpty()) return -1;
                        if (s2.isEmpty()) return 1;
                        
                        try {
                            Double d1 = Double.parseDouble(s1);
                            Double d2 = Double.parseDouble(s2);
                            return d1.compareTo(d2);
                        } catch (NumberFormatException e) {
                            // Fallback to string comparison for non-numeric values
                            return s1.compareTo(s2);
                        }
                    }
                });
                
                // Set up proper table renderer for selection
                table.setDefaultRenderer(Object.class, new javax.swing.table.DefaultTableCellRenderer() {
                    @Override
                    public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                        Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                        
                        // Use normal font for all rows
                        setFont(getFont().deriveFont(Font.PLAIN));
                        
                        // Handle selection properly
                        if (isSelected) {
                            setBackground(table.getSelectionBackground());
                            setForeground(table.getSelectionForeground());
                        } else {
                            setBackground(table.getBackground());
                            setForeground(table.getForeground());
                        }
                        
                        return c;
                    }
                });

                // Set up Excel-style header filters
                setupExcelStyleHeaderFilters(table, sorter, powerConsumers);
                
                // Create scroll pane
                JScrollPane scrollPane = new JScrollPane(table);

                // Create dialog
                JFrame mainFrame = Application.getInstance().getMainFrame();
                JDialog dialog = new JDialog(mainFrame, "Power Consumption - " + contextName, false);

                // Create info panel with total
                JPanel infoPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
                JLabel infoLabel = new JLabel("Total Power Consumption: " + String.format("%.2f W", totalPowerConsumption));
                infoLabel.setFont(infoLabel.getFont().deriveFont(Font.BOLD, 14f));
                infoPanel.add(infoLabel);

                // Layout dialog
                dialog.setLayout(new BorderLayout());
                dialog.add(infoPanel, BorderLayout.NORTH);
                dialog.add(scrollPane, BorderLayout.CENTER);

                // Size and position - compact size with Excel-style header filters
                dialog.setSize(600, 400);
                dialog.setLocationRelativeTo(mainFrame);

                // Show appropriate message if no power consumers found
                if (powerConsumers.isEmpty()) {
                    JLabel noDataLabel = new JLabel("No power consumers found in " + contextName, SwingConstants.CENTER);
                    dialog.add(noDataLabel, BorderLayout.CENTER);
                    dialog.setSize(300, 150);
                }

                dialog.setVisible(true);

            } catch (Exception e) {
                Utilities.Log("Error displaying power consumption results: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * Set up Excel-style header filters with dropdown arrows in column headers
     */
    private static void setupExcelStyleHeaderFilters(JTable table, TableRowSorter<DefaultTableModel> sorter, List<PowerConsumerInfo> powerConsumers) {
        JTableHeader header = table.getTableHeader();
        
        // Store filter state for each column (skip column 0)
        final Map<Integer, Set<String>> columnFilters = new HashMap<>();
        columnFilters.put(1, new HashSet<>()); // Type column  
        columnFilters.put(2, new HashSet<>()); // Power column
        
        // Custom header renderer with dropdown arrow
        header.setDefaultRenderer(new javax.swing.table.DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                
                // Add dropdown arrow to header text (skip column 0)
                String headerText = value.toString();
                if (column > 0) { // Only add dropdown arrow for columns 1 and 2
                    Set<String> filters = columnFilters.get(column);
                    if (filters != null && !filters.isEmpty()) {
                        headerText += " ▼*"; // Arrow with asterisk to show filter is active
                    } else {
                        headerText += " ▼";
                    }
                }
                setText(headerText);
                
                setHorizontalAlignment(SwingConstants.CENTER);
                setBorder(BorderFactory.createRaisedBevelBorder());
                return c;
            }
        });
        
        // Add mouse listener for header clicks
        header.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int column = header.columnAtPoint(e.getPoint());
                if (column > 0) { // Only show filter popup for columns 1 and 2
                    showFilterPopup(e, column, table, sorter, powerConsumers, columnFilters);
                }
            }
        });
    }

    /**
     * Show Excel-style filter popup with checkboxes
     */
    private static void showFilterPopup(MouseEvent e, int column, JTable table, TableRowSorter<DefaultTableModel> sorter, 
                                      List<PowerConsumerInfo> powerConsumers, Map<Integer, Set<String>> columnFilters) {
        
        // Get unique values for this column
        Set<String> uniqueValues = new HashSet<>();
        DefaultTableModel model = (DefaultTableModel) table.getModel();
        
        for (int row = 0; row < model.getRowCount(); row++) {
            Object value = model.getValueAt(row, column);
            if (value != null) {
                if (column == 2) { // Power column - categorize values
                    double power = 0.0;
                    try {
                        power = Double.parseDouble(value.toString());
                    } catch (NumberFormatException ex) {
                        power = 0.0;
                    }
                    uniqueValues.add(power > 0 ? "Has Power (>0W)" : "No Power (0W)");
                } else {
                    uniqueValues.add(value.toString());
                }
            }
        }
        
        // Create popup menu with checkboxes
        JPopupMenu popup = new JPopupMenu();
        popup.setLayout(new BorderLayout());
        
        // Create checkbox panel
        JPanel checkboxPanel = new JPanel();
        checkboxPanel.setLayout(new BoxLayout(checkboxPanel, BoxLayout.Y_AXIS));
        
        // Select All / Deselect All controls
        JPanel controlPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JButton selectAllBtn = new JButton("Select All");
        JButton deselectAllBtn = new JButton("Deselect All");
        JButton clearFilterBtn = new JButton("Clear Filter");
        
        Set<String> currentFilters = columnFilters.get(column);
        Map<String, JCheckBox> checkBoxes = new HashMap<>();
        
        // Create checkboxes for each unique value
        String[] sortedValues = uniqueValues.toArray(new String[0]);
        Arrays.sort(sortedValues);
        
        for (String value : sortedValues) {
            JCheckBox checkbox = new JCheckBox(value);
            checkbox.setSelected(currentFilters.isEmpty() || currentFilters.contains(value));
            checkBoxes.put(value, checkbox);
            checkboxPanel.add(checkbox);
        }
        
        // Button actions
        selectAllBtn.addActionListener(evt -> {
            for (JCheckBox cb : checkBoxes.values()) {
                cb.setSelected(true);
            }
        });
        
        deselectAllBtn.addActionListener(evt -> {
            for (JCheckBox cb : checkBoxes.values()) {
                cb.setSelected(false);
            }
        });
        
        clearFilterBtn.addActionListener(evt -> {
            columnFilters.get(column).clear();
            applyExcelStyleFilter(sorter, columnFilters, powerConsumers);
            table.getTableHeader().repaint();
            popup.setVisible(false);
        });
        
        controlPanel.add(selectAllBtn);
        controlPanel.add(deselectAllBtn);
        controlPanel.add(clearFilterBtn);
        
        // OK and Cancel buttons
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton okBtn = new JButton("OK");
        JButton cancelBtn = new JButton("Cancel");
        
        okBtn.addActionListener(evt -> {
            Set<String> selectedValues = new HashSet<>();
            for (Map.Entry<String, JCheckBox> entry : checkBoxes.entrySet()) {
                if (entry.getValue().isSelected()) {
                    selectedValues.add(entry.getKey());
                }
            }
            columnFilters.put(column, selectedValues);
            applyExcelStyleFilter(sorter, columnFilters, powerConsumers);
            table.getTableHeader().repaint();
            popup.setVisible(false);
        });
        
        cancelBtn.addActionListener(evt -> popup.setVisible(false));
        
        buttonPanel.add(okBtn);
        buttonPanel.add(cancelBtn);
        
        // Add scroll pane for checkboxes
        JScrollPane scrollPane = new JScrollPane(checkboxPanel);
        scrollPane.setPreferredSize(new Dimension(200, 150));
        
        popup.add(controlPanel, BorderLayout.NORTH);
        popup.add(scrollPane, BorderLayout.CENTER);
        popup.add(buttonPanel, BorderLayout.SOUTH);
        
        // Show popup
        popup.show(table.getTableHeader(), e.getX(), e.getY());
    }

    /**
     * Apply Excel-style multi-column filter
     */
    private static void applyExcelStyleFilter(TableRowSorter<DefaultTableModel> sorter, Map<Integer, Set<String>> columnFilters, List<PowerConsumerInfo> powerConsumers) {
        sorter.setRowFilter(new javax.swing.RowFilter<DefaultTableModel, Object>() {
            @Override
            public boolean include(javax.swing.RowFilter.Entry<? extends DefaultTableModel, ? extends Object> entry) {
                // Check each column filter
                for (Map.Entry<Integer, Set<String>> filterEntry : columnFilters.entrySet()) {
                    int column = filterEntry.getKey();
                    Set<String> allowedValues = filterEntry.getValue();
                    
                    if (!allowedValues.isEmpty()) {
                        Object cellValue = entry.getValue(column);
                        String valueToCheck;
                        
                        if (column == 2) { // Power column
                            double power = 0.0;
                            try {
                                power = Double.parseDouble(cellValue.toString());
                            } catch (NumberFormatException e) {
                                power = 0.0;
                            }
                            valueToCheck = power > 0 ? "Has Power (>0W)" : "No Power (0W)";
                        } else {
                            valueToCheck = cellValue.toString();
                        }
                        
                        if (!allowedValues.contains(valueToCheck)) {
                            return false;
                        }
                    }
                }
                return true;
            }
        });
    }


    /**
     * Inner class to hold power consumer information
     */
    private static class PowerConsumerInfo {
        private String name;
        private String type;
        private double powerConsumption;

        public PowerConsumerInfo(String name, String type, double powerConsumption) {
            this.name = name;
            this.type = type;
            this.powerConsumption = powerConsumption;
        }

        public String getName() { return name; }
        public String getType() { return type; }
        public double getPowerConsumption() { return powerConsumption; }
    }
}