package com.pmw790.configurators;

import com.nomagic.actions.ActionsManager;
import com.nomagic.magicdraw.actions.BrowserContextAMConfigurator;
import com.nomagic.magicdraw.ui.browser.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.actions.ActionsCategory;
import com.pmw790.functions.DiagramGenerators;
import com.pmw790.jython.jythonFunctions;
import com.pmw790.functions.Finders;
import com.pmw790.functions.Utilities;
import com.pmw790.main.PMW790Plugin;
import com.nomagic.magicdraw.actions.MDAction;
import com.nomagic.magicdraw.core.Project;
import java.awt.event.ActionEvent;

public class BrowserContextConfigurator implements BrowserContextAMConfigurator {
	public static final BrowserContextConfigurator INSTANCE = new BrowserContextConfigurator();
	private static jythonFunctions jythonFunctions;

	public static void initialize(jythonFunctions jf) {
		jythonFunctions = jf;
	}

	@Override
	public void configure(ActionsManager manager, Tree browserTree) {
		Project currentProject = PMW790Plugin.getCurrentProject();
		if (currentProject == null) {
			return;
		}
		
		// Use the project-specific containment tree
		Tree tree = currentProject.getBrowser().getContainmentTree();
		Object selectedNode = tree.getSelectedNode();
		if (!(selectedNode instanceof Node)) {
			return;
		}

		Node node = (Node) selectedNode;
		Object userObject = node.getUserObject();
		if (!(userObject instanceof Element)) {
			return;
		}

		Element element = (Element) userObject;

		// Check if element is a Block
		boolean isBlock = Utilities.ModelElements.isBlock(element);
		if (!isBlock) {
			return;
		}

		// Create the main category
		ActionsCategory mainCategory = new ActionsCategory("PMW790_TOOLKIT", "PMW790 Toolkit");
		mainCategory.setNested(true);

		// Create the power tools category as an action
		ActionsCategory powerToolsCategory = new ActionsCategory("POWER_TOOLS_CATEGORY", "Power Tools");
		powerToolsCategory.setNested(true);
		PowerFolderAction PowerActions = new PowerFolderAction(element);
		PowerConsumptionAction powerConsumptionAction = new PowerConsumptionAction(element);
		powerToolsCategory.addAction(PowerActions);
		powerToolsCategory.addAction(powerConsumptionAction);
		mainCategory.addAction(powerToolsCategory);

		// Create the diagram tools category as an action
		ActionsCategory diagramsCategory = new ActionsCategory("DIAGRAMS_CATEGORY","Diagrams");
		diagramsCategory.setNested(true);

		ActionsCategory experimentalCategory = new ActionsCategory("EXPERIMENTAL_CATEGORY","Experimental");
		experimentalCategory.setNested(true);

		DiagramGenerators.IBD_All ibdall = new DiagramGenerators.IBD_All(element);
		DiagramGenerators.IBD_L1 ibdl1 = new DiagramGenerators.IBD_L1(element);
		DiagramGenerators.IBD_L2 ibdl2 = new DiagramGenerators.IBD_L2(element);

		diagramsCategory.addAction(ibdall);
		diagramsCategory.addAction(ibdl1);
		diagramsCategory.addAction(ibdl2);
		if (jythonFunctions != null) {
			experimentalCategory.addAction(new jythonFunctions.fidl2Action(jythonFunctions, element, 10));
		}
		mainCategory.addAction(diagramsCategory);
		mainCategory.addAction(experimentalCategory);

		// Add the main category to the context menu
		manager.addCategory(mainCategory);
	}

	@Override
	public int getPriority() {
		return 10;
	}
}