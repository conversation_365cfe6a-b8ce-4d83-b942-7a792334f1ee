package com.pmw790.configurators;

import com.nomagic.actions.AMConfigurator;
import com.nomagic.actions.ActionsCategory;
import com.nomagic.actions.ActionsManager;
import com.nomagic.actions.NMAction;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.pmw790.jython.jythonFunctions;

public class MainMenuConfigurator implements AMConfigurator {
	public static final MainMenuConfigurator INSTANCE = new MainMenuConfigurator();
	private static jythonFunctions jythonFunctions;

	public static void initialize(jythonFunctions jf) {
		jythonFunctions = jf;
	}
	
	@Override
	public void configure(ActionsManager manager) {
		NMAction action = manager.getActionFor("TOOLS_PLUGINS");
		if (action == null) {
			return;
		}
		ActionsCategory toolkitCategory = new ActionsCategory("TOOLKIT_CATEGORY", "PMW 790 Toolkit");
		toolkitCategory.setNested(true);
		ActionsCategory modelUpdates = new ActionsCategory("UPDATE_MODEL_ELEMENTS", "Model-wide Tools");
		modelUpdates.setNested(true);
        
        Element element = null;

        jythonFunctions.importSystemAction impSys = new jythonFunctions.importSystemAction(jythonFunctions);
        jythonFunctions.exportJsonAction expJson = new jythonFunctions.exportJsonAction(jythonFunctions);
		jythonFunctions.updateElementImages modEleUp = new jythonFunctions.updateElementImages(jythonFunctions);

        toolkitCategory.addAction(impSys);
        toolkitCategory.addAction(expJson);
		toolkitCategory.addAction(modEleUp);

        action.addAction(toolkitCategory);
	}
}