from core.imports import *
from core.constants import REQUIRED_RESOURCES, PACKAGE_VIEWS
from utils.md_utils import printer

class ModelDictionary:
    """Centralized dictionary management for all model types"""
    def __init__(self, project):
        self.project = project
        self.system_blocks_list = []
        self.location_blocks_list = []

    def get_element_ports(self, element):
        """Extract complete port information from element"""
        ports_dict = {}
        for port in element.getOwnedAttribute():
            if not any(st.getName() == "IDP Port" for st in port.getAppliedStereotype()):
                continue

            port_info = {
                'qualified_name': port.getQualifiedName(),
                'tagged_values': {}
            }

            # Get all tagged values and stereotype info
            for tag in port.getTaggedValue():
                tag_name = tag.tagDefinition.name
                tag_value = tag.getValue()[0]
                port_info['tagged_values'][tag_name] = tag_value

            ports_dict[port.getName()] = port_info

        return ports_dict

    def build_taxonomy_dictionary(self, taxonomy_package, bcl_package=None):
        """
        Builds unified dictionary including both IDP Taxonomy and BCL elements.
        
        The inheritance chain is: BCL <- IDP Taxonomy <- Material <- Asset
        
        Args:
            taxonomy_package: Package containing IDP taxonomy blocks
            bcl_package: Package containing BCL blocks (optional)
            
        Returns:
            dict: Dictionary with structure:
                {
                    'BlockName': {
                        'Owned Attributes': [list of attribute names],
                        'Classifier Elements': [list of classifier names]
                    }
                }
        """
        taxonomy_dict = {}
        taxonomy_attributes_header = "Owned Attributes"
        classifier_components_header = "Classifier Elements"
        
        def add_block_to_dict(block_element):
            """Helper function to add a block to the taxonomy dictionary"""
            if not sh.hasStereotype(block_element, "Block"):
                return False
                
            block_name = block_element.getName()
            if block_name in taxonomy_dict:
                return False
                
            attributes = []
            classifiers = []
            
            # Get value properties
            for att in block_element.getOwnedAttribute():
                if att.getHumanType() == "Value Property":
                    attributes.append(att.getName())
            
            # Get classifier base elements through generalizations
            for generalization in block_element.getGeneralization():
                classifier = generalization.getGeneral()
                if classifier:
                    classifiers.append(classifier.getName())
            
            # Add to dictionary
            taxonomy_dict[block_name] = {
                taxonomy_attributes_header: attributes,
                classifier_components_header: classifiers
            }
            
            return True
        
        # Process IDP Taxonomy package to establish IDP blocks
        idp_blocks_to_trace = []
        for item in taxonomy_package.getOwnedMember():
            if add_block_to_dict(item):
                idp_blocks_to_trace.append(item)
        
        # Trace generalization chain from IDP blocks to find BCL parents
        if bcl_package:
            bcl_namespace = bcl_package.getName()
            
            for idp_block in idp_blocks_to_trace:
                # Follow generalization chain
                current_block = idp_block
                visited = set()
                
                while current_block and current_block.getName() not in visited:
                    visited.add(current_block.getName())
                    
                    # Check all generalizations of current block
                    for generalization in current_block.getGeneralization():
                        classifier = generalization.getGeneral()
                        if classifier:
                            classifier_qualified_name = classifier.getQualifiedName()
                            
                            # Check if this classifier is in BCL package hierarchy using qualified name
                            is_bcl_classifier = (classifier_qualified_name and 
                                               classifier_qualified_name.startswith(bcl_namespace))
                            
                            if is_bcl_classifier:
                                # Add BCL block to dictionary if not already present
                                add_block_to_dict(classifier)
                                
                                # Continue tracing from this BCL block
                                current_block = classifier
                                break
                    else:
                        # No more generalizations found
                        break

        return taxonomy_dict

    def build_material_dictionary(self, material_package):
        """
        Builds dictionary of materials from Parts Catalog package.

        Args:
            material_package: Package containing material blocks

        Returns:
            dict: Materials indexed by SBOM ID with structure:
                {
                    'sbom_id': {
                        'sbom': sbom_id,
                        'qualified_name': qname,
                        'ports': ports_dict,
                        'attributes': attributes,
                        'generalization': gen_info
                    }
                }
        """
        material_dict = {}
        for element in material_package.getOwnedMember():
            if not sh.hasStereotype(element, "Block"):
                continue
            # Get basic attributes
            attributes = self.get_element_attributes(element)
            sbom = attributes.get('sbom') or attributes.get('material__sbom')

            if not sbom:
                printer("Material missing SBOM: " + element.getName())
                continue

            material_dict[sbom] = {
                'sbom': sbom,
                'qualified_name': element.getQualifiedName(),
                'ports': self.get_element_ports(element),
                'attributes': attributes,
                'generalization': self.get_generalization_info(element)
            }

        return material_dict

    def build_systems_dictionary(self, systems_package):
        """
        Builds dictionary of system blocks from Systems View package.
        Returns dict: Systems indexed by name with structure:
            {
                'system_name': {
                    'qualified_name': qname,
                    'id': system_id,
                    'attributes': {...}
                }
            }
        """
        systems_dict = {}

        systems_dict = {}
        for element in systems_package.getOwnedMember():
            if not sh.hasStereotype(element, "Block"):
                continue

            name, entry = self.update_system_dictionary_entry(element)
            if name:
                systems_dict[name] = entry

        return systems_dict

    def build_location_dictionary(self, location_package):
        """
        Builds dictionary of locations from Location View package.
        Returns dict: Locations indexed by ID with structure containing
        composite relationships, base classifier, and complete attributes
        """
        location_dict = {}

        for element in location_package.getOwnedMember():
            if not sh.hasStereotype(element, "Block"):
                continue

            relationship_info = self.get_composite_relationships(element)
            composite_rel = relationship_info.get('composite_relationship')
            parent_id = relationship_info.get('parent_composite_element')

            name, entry = self.update_location_dictionary_entry(element, parent_id, composite_rel)
            if name:
                location_dict[name] = entry
        return location_dict

    def build_parts_dictionary(self, parts_package):
        """Builds dictionary of parts from Parts View with consistent structure"""
        parts_dict = {}

        for pkg in parts_package.getOwnedMember():
            if isinstance(pkg, Package):
                parts_dict[pkg.getName()] = {
                    'qualified_name': pkg.getQualifiedName(),
                    'type': 'package'
                }

                # Add parts within package
                for element in pkg.getOwnedMember():
                    if sh.hasStereotype(element, "Block"):
                        parts_dict[element.getName()] = {
                            'qualified_name': element.getQualifiedName(),
                            'owner': pkg.getName(),
                            'type': 'part'
                        }
                        # Add other attributes
                        attributes = self.get_element_attributes(element)
                        parts_dict[element.getName()].update(attributes)

        return parts_dict

    def build_assets_dictionary(self, assets_package):
        """
        Builds dictionary of assets from Assets View package.
        Returns dict: Assets indexed by ID with structure containing
        material classifier, composite relationships, and complete attributes
        """
        assets_dict = {}

        for element in assets_package.getOwnedMember():
            if isinstance(element, Package):
                # Recursively process packages
                for subelement in element.getOwnedMember():
                    if not sh.hasStereotype(subelement, "Block"):
                        continue

                    asset_id, entry = self.update_asset_dictionary_entry(subelement)
                    if asset_id:
                        assets_dict[asset_id] = entry
            elif sh.hasStereotype(element, "Block"):
                # Process top-level assets
                asset_id, entry = self.update_asset_dictionary_entry(element)
                if asset_id:
                    assets_dict[asset_id] = entry

        return assets_dict

    def update_system_dictionary_entry(self, system_block):
        """Update single system entry without rebuilding entire dictionary"""
        if not system_block or not sh.hasStereotype(system_block, "Block"):
            return None, None

        entry = {
            'qualified_name': system_block.getQualifiedName(),
            'attributes': self.get_element_attributes(system_block),
            'generalization': self.get_generalization_info(system_block)
        }

        return system_block.getName(), entry

    def update_location_dictionary_entry(self, location_element, parent_composite_element=None, composited_relationship=None):
        if not location_element or not sh.hasStereotype(location_element, "Block"):
            return None, None

        # Get base classifier from generalizations
        base_classifier = None
        if location_element.getGeneralization():
            gen = location_element.getGeneralization()[0]
            if gen.getGeneral():
                base_classifier = gen.getGeneral().getName()

        # Get all attributes including ID
        attributes = self.get_element_attributes(location_element)
        location_id = attributes.get('id')

        if location_id:
            entry = {
                'qualified_name': location_element.getQualifiedName(),
                'location_name': location_element.getName(),
                'base_classifier': base_classifier,
                'parent_composite_element': parent_composite_element,
                'composite_relationship': composited_relationship
            }
            entry.update(attributes)
            return location_id, entry

        return None, None

    def update_parts_dictionary_entry(self, package):
        """Update single parts package entry without rebuilding entire dictionary"""
        if not package or not isinstance(package, Package):
            return None, None

        entry = {
            'qualified_name': package.getQualifiedName(),
        }

        return package.getName(), entry

    def update_asset_dictionary_entry(self, asset_element, asset_package_names=None, asset_status=None):
        """Update single asset entry without rebuilding entire dictionary

        Args:
            asset_element: Asset block element to process
            asset_status: Asset status from original data

        Returns:
            tuple: (asset_id, entry_dict) or (None, None) if invalid
        """
        if not asset_element or not sh.hasStereotype(asset_element, "Block"):
            return None, None

        # Get all attributes including ID
        attributes = self.get_element_attributes(asset_element)
        asset_id = attributes.get('id')

        if not asset_id:
            return None, None

        # Get composite relationships
        composite_info = self.get_composite_relationships(asset_element, asset_package_names)

        # Get material info through generalization
        gen_info = self.get_generalization_info(asset_element)

        entry = {
            'qualified_name': asset_element.getQualifiedName(),
            'asset_name': asset_element.getName(),
            'material_classifier': gen_info.get('classifier'),
            'parent_composite_element': composite_info.get('parent_composite_element'),
            'parent_element_type': composite_info.get('owning_asset_type'),
            'composite_relationship': composite_info.get('composite_relationship'),
            'owning_package': asset_element.getOwningPackage().getName(),
            'asset_status': asset_status
        }
        entry.update(attributes)

        return asset_id, entry

    def get_element_attributes(self, element):
        """Extract basic attributes from an element"""
        attributes = {}
        for att in element.getOwnedAttribute():
            if any(st_.getName() == "IDP Port" for st_ in att.getAppliedStereotype()):
                continue

            try:
                attribute_value = att.getDefaultValue().getValue()
            except:
                try:
                    attribute_value = att.getDefaultValue().getInstance().getName()
                except:
                    attribute_value = "-"
            attributes[att.getName()] = attribute_value
        return attributes

    def get_composite_relationships(self, element, asset_package_names=None):
        """Helper to extract composite relationship information"""
        result = {
            'composite_relationship': None,
            'parent_composite_element': None,
            'owning_asset_type': None,
            'parent_part': None,
            'part_relationship': None,
            'parent_system': None,
            'parent_system_composite_relationship': None
        }

        # Get all association relationships
        association_rels = [rel for rel in element.get_relationshipOfRelatedElement()
                            if rel.getHumanType() == "Association"]

        for rel in association_rels:
            rel_elements = rel.getMemberEnd()
            part_e = None
            whole_e = None

            # Find composite and non-composite ends
            for m_e in rel_elements:
                if str(m_e.getAggregation()) == "composite":
                    part_e = m_e.getType().getName()
                if str(m_e.getAggregation()) == "none":
                    whole_e = m_e.getType().getName()

            # If this element is the part element
            if part_e and element.getName() == part_e and whole_e:
                # Get related elements
                for related_e in rel.getRelatedElement():
                    if related_e.getName() == whole_e:
                        rel_elem = f.byQualifiedName().find(self.project, related_e.getQualifiedName())
                        owner_pkg_name = rel_elem.getOwningPackage().getName()

                        # Handle different package types
                        if owner_pkg_name in (asset_package_names or []):
                            for att in rel_elem.getOwnedAttribute():
                                if att.getName() == "id":
                                    result['parent_composite_element'] = att.getDefaultValue().getValue()
                                    result['composite_relationship'] = rel
                                    result['owning_asset_type'] = 'Asset'
                        elif owner_pkg_name == PACKAGE_VIEWS["LOCATION"]:
                            for att in rel_elem.getOwnedAttribute():
                                if att.getName() == "id":
                                    result['parent_composite_element'] = att.getDefaultValue().getValue()
                                    result['composite_relationship'] = rel
                                    result['owning_asset_type'] = 'Location'
                        # elif owner_pkg_name == "Parts View":
                        #     result['parent_part'] = str(rel_elem.getName())
                        #     result['part_relationship'] = rel
                        elif owner_pkg_name == PACKAGE_VIEWS["SYSTEMS"]:
                            result['parent_system'] = rel_elem.getName()
                            result['parent_system_composite_relationship'] = rel

        return result

    def get_generalization_info(self, element):
        """Get generalization relationship info"""
        info = {}

        generalizations = [rel for rel in element.get_relationshipOfRelatedElement()
                           if rel.getHumanType() == "Generalization"]

        if generalizations:
            rel = generalizations[0]
            info['relationship'] = rel
            target = rel.getTarget()[0] if rel.getTarget() else None
            if target:
                if target.getOwner().getName() == REQUIRED_RESOURCES["TAXONOMY"]:
                    info['classifier'] = target.getName()

        return info

    def process_classifiers(self, block_element, taxonomy_dict, start_classifier, data):
        """
        Recursively processes classifiers and their properties for a block element

        Args:
            block_element: Target block element to process
            taxonomy_dict: Dictionary containing taxonomy information
            start_classifier: Initial classifier to start processing from
            data: Data to apply to the properties
        """
        processed = set()
        classifiers_to_process = [start_classifier]

        while classifiers_to_process:
            current = classifiers_to_process.pop(0)
            if current not in processed:
                if current not in taxonomy_dict:
                    continue

                processed.add(current)

                next_classifiers = taxonomy_dict[current]["Classifier Elements"]
                classifiers_to_process.extend([c for c in next_classifiers if c not in processed])

    def build_part_prop_dict(self, package, asset_names_list):
        """Creates dictionary of part property elements recursively"""
        part_props_dict = {}

        def process_element(element):
            if sh.hasStereotype(element, "Block"):
                # Get ID from element
                element_id = next((att.getDefaultValue().getValue()
                                   for att in element.getOwnedAttribute()
                                   if att.getName() == "id"), element.getName())

                if element_id:
                    # Process part properties
                    part_dict = {}
                    for att in element.getOwnedAttribute():
                        if any(st_.getName() == "PartProperty" for st_ in att.getAppliedStereotype()):
                            try:
                                # Get target element's name and find its ID
                                type_name = att.getType().getName()
                                if type_name in asset_names_list:
                                    target_id = asset_names_list[type_name]
                                    part_dict[target_id] = att
                            except:
                                continue

                    if part_dict:
                        part_props_dict[element_id] = part_dict

            elif isinstance(element, Package):
                for subelement in element.getOwnedMember():
                    process_element(subelement)

        for element in package.getOwnedMember():
            process_element(element)

        return part_props_dict

    def build_site_part_props(self, site_block):
        part_props = {}
        if site_block is None:
            return part_props
        for part in site_block.getPart():
            if any(st_.getName() == "PartProperty" for st_ in part.getAppliedStereotype()):
                part_type = part.getType()
                # Get base classifier from generalizations
                base_classifier = None
                if part_type.getGeneralization():
                    gen = part_type.getGeneralization()[0]
                    if gen.getGeneral():
                        base_classifier = gen.getGeneral().getName()
                if base_classifier == "System":
                    part_name = part_type.getName()
                    part_props[part_name] = part

        return part_props