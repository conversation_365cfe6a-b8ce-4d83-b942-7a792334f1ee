package com.pmw790.configurators;

import com.nomagic.actions.ActionsManager;
import com.nomagic.magicdraw.actions.DiagramContextAMConfigurator;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.actions.ActionsCategory;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.pmw790.functions.DiagramGenerators;
import com.pmw790.functions.Utilities;
import com.nomagic.magicdraw.actions.MDAction;
import java.awt.event.ActionEvent;

public class DiagramContextConfigurator implements DiagramContextAMConfigurator {
	public static final DiagramContextConfigurator INSTANCE = new DiagramContextConfigurator();
	
	@Override
	public void configure(ActionsManager manager, DiagramPresentationElement diagram, PresentationElement[] selected, PresentationElement requester) {
		
		if (selected == null || selected.length == 0) {
			return;
		}
		PresentationElement pe = selected[0];
		if (pe == null) {
			return;
		}
		Element element = pe.getElement();
		if (element == null) {
			return;
		}

        ActionsCategory toolkitCategory = new ActionsCategory("TOOLKIT_CATEGORY", "PMW 790 Toolkit");
        toolkitCategory.setNested(true);

        ActionsCategory diagramsCategory = new ActionsCategory("DIAGRAMS_CATEGORY","Diagrams");
        diagramsCategory.setNested(true);

        // Add diagram generator actions
        if (Utilities.ModelElements.isBlock(element)) {
            diagramsCategory.addAction(new MDAction("IBD_ALL_DIAGRAM", "Create IBD All", null, null) {
                @Override
                public void actionPerformed(ActionEvent e) {
                    new DiagramGenerators.IBD_All(element);
                }
            });
        }

        toolkitCategory.addAction(diagramsCategory);

        manager.addCategory(toolkitCategory);
	}
	
	@Override
	public int getPriority() {
		return 10;
	}
}