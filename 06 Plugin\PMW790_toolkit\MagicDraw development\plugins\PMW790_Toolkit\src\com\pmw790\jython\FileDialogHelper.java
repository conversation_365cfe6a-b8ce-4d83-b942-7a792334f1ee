package com.pmw790.jython;

import javax.swing.JFileChooser;
import javax.swing.JOptionPane;
import java.awt.Component;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Properties;

/**
 * Utility class for standardizing file dialog operations
 */
public final class FileDialogHelper {

    private static File lastOpenedDirectory = null;
    private static final String PREFERENCES_FILE = "pmw790_preferences.properties";
    private static final String LAST_DIRECTORY_KEY = "last.opened.directory";
    static {
        loadLastDirectory();
    }
    
    /**
     * Shows a directory chooser dialog
     * @param parent Parent component for the dialog
     * @param title Title for the dialog
     * @return Selected directory or null if cancelled
     */
    public static File chooseDirectory(Component parent, String title) {
        JFileChooser chooser = new JFileChooser();
        chooser.setDialogTitle(title);
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        
        // Set the current directory to the last opened directory if available
        if (lastOpenedDirectory != null && lastOpenedDirectory.exists()) {
            chooser.setCurrentDirectory(lastOpenedDirectory);
        }
        
        int result = chooser.showOpenDialog(parent);
        if (result == JFileChooser.APPROVE_OPTION) {
            File selectedFile = chooser.getSelectedFile();
            // Save the selected directory as the last opened directory
            lastOpenedDirectory = selectedFile;
            saveLastDirectory();
            return selectedFile;
        }
        return null;
    }
    
    /**
     * Prompts user for a filename with validation and timestamp handling
     * @param parent Parent component for the dialog
     * @param promptMessage Message to show in the input dialog
     * @param title Title for the input dialog
     * @param defaultPrefix Default prefix if no name provided
     * @return Filename without extension, or null if cancelled
     */
    public static String promptForFilename(Component parent, String promptMessage, String title, String defaultPrefix) {
        String userFileName = JOptionPane.showInputDialog(parent, promptMessage, title, JOptionPane.QUESTION_MESSAGE);
        
        if (userFileName == null) {
            return null;
        }
        
        if (userFileName.trim().isEmpty()) {
            if (defaultPrefix != null) {
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(JythonConstants.TIMESTAMP_FORMAT));
                return defaultPrefix + timestamp;
            } else {
                JOptionPane.showMessageDialog(parent,
                        "Filename cannot be empty. Operation cancelled.",
                        "Invalid Filename",
                        JOptionPane.WARNING_MESSAGE);
                return null;
            }
        }
        
        userFileName = userFileName.trim();
        
        // Remove .json extension if present
        if (userFileName.toLowerCase().endsWith(JythonConstants.JSON_EXTENSION)) {
            userFileName = userFileName.substring(0, userFileName.length() - JythonConstants.JSON_EXTENSION.length());
        }
        
        return userFileName;
    }
    
    /**
     * Prompts user for export filename with standard validation
     * @param parent Parent component for the dialog
     * @return Filename without extension, or null if cancelled
     */
    public static String promptForExportFilename(Component parent) {
        return promptForFilename(parent, 
                "Enter filename for model export:", 
                "Model Export Filename", 
                null);
    }
    
    /**
     * Prompts user for download filename with automatic timestamp fallback
     * @param parent Parent component for the dialog
     * @return Filename without extension, or null if cancelled
     */
    public static String promptForDownloadFilename(Component parent) {
        return promptForFilename(parent,
                "Enter filename:",
                "Filename Input",
                JythonConstants.DEFAULT_DOWNLOAD_PREFIX);
    }
    
    /**
     * Checks if a file exists and prompts for overwrite confirmation
     * @param parent Parent component for the dialog
     * @param file The file to check
     * @return true if should proceed (file doesn't exist or user confirmed overwrite), false otherwise
     */
    public static boolean confirmOverwrite(Component parent, File file) {
        if (!file.exists()) {
            return true;
        }
        
        int overwrite = JOptionPane.showConfirmDialog(parent,
                "File '" + file.getName() + "' already exists. Overwrite?",
                "File Exists",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);
        
        if (overwrite != JOptionPane.YES_OPTION) {
            JOptionPane.showMessageDialog(parent,
                    "Operation cancelled.",
                    "Operation Cancelled",
                    JOptionPane.INFORMATION_MESSAGE);
            return false;
        }
        
        return true;
    }
    
    /**
     * Creates a timestamped filename with the given prefix
     * @param prefix Prefix for the filename
     * @return Timestamped filename without extension
     */
    public static String createTimestampedFilename(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(JythonConstants.TIMESTAMP_FORMAT));
        return prefix + timestamp;
    }
    
    /**
     * Creates a catalog filename with timestamp
     * @return Catalog filename without extension
     */
    public static String createCatalogFilename() {
        return createTimestampedFilename(JythonConstants.CATALOG_FILE_PREFIX);
    }
    
    /**
     * Creates a temporary export filename with timestamp
     * @return Temporary export filename without extension
     */
    public static String createTempExportFilename() {
        return createTimestampedFilename(JythonConstants.TEMP_EXPORT_PREFIX);
    }
    
    /**
     * Adds JSON extension to filename if not already present
     * @param filename The filename to process
     * @return Filename with .json extension
     */
    public static String ensureJsonExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return filename;
        }
        
        if (!filename.toLowerCase().endsWith(JythonConstants.JSON_EXTENSION)) {
            return filename + JythonConstants.JSON_EXTENSION;
        }
        
        return filename;
    }
    
    /**
     * Loads the last directory from persistent storage
     */
    private static void loadLastDirectory() {
        try {
            File prefsFile = getPreferencesFile();
            if (prefsFile.exists()) {
                Properties props = new Properties();
                try (FileInputStream fis = new FileInputStream(prefsFile)) {
                    props.load(fis);
                    String lastDirPath = props.getProperty(LAST_DIRECTORY_KEY);
                    if (lastDirPath != null && !lastDirPath.trim().isEmpty()) {
                        File lastDir = new File(lastDirPath);
                        if (lastDir.exists() && lastDir.isDirectory()) {
                            lastOpenedDirectory = lastDir;
                        }
                    }
                }
            }
        } catch (IOException e) {
            // Silently ignore - preferences will just not be loaded
        }
    }
    
    /**
     * Saves the last directory to persistent storage
     */
    private static void saveLastDirectory() {
        if (lastOpenedDirectory == null) {
            return;
        }
        
        try {
            File prefsFile = getPreferencesFile();
            Properties props = new Properties();

            if (prefsFile.exists()) {
                try (FileInputStream fis = new FileInputStream(prefsFile)) {
                    props.load(fis);
                }
            }
            props.setProperty(LAST_DIRECTORY_KEY, lastOpenedDirectory.getAbsolutePath());
            try (FileOutputStream fos = new FileOutputStream(prefsFile)) {
                props.store(fos, "PMW790 Plugin Preferences");
            }
        } catch (IOException e) {
            // Silently ignore - preferences will just not be saved
        }
    }
    
    /**
     * Gets the preferences file location in the user's home directory
     */
    private static File getPreferencesFile() {
        String userHome = System.getProperty("user.home");
        return new File(userHome, PREFERENCES_FILE);
    }
    
    // Private constructor to prevent instantiation
    private FileDialogHelper() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}