package com.pmw790.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.pmw790.functions.ConnectionRegistry;
import com.pmw790.functions.Utilities;

import java.util.*;

/**
 * Context class holding information needed for cabinet power diagram creation.
 * Extends PowerDiagramContext to inherit shared functionality.
 */
public class CabinetDiagramContext extends PowerDiagramContext {
    private final Class cabinetBlock;
    private final List<String> providers;
    private final Map<String, Property> partPropertyMap; // Maps property NAME to Property element
    private final Map<String, List<String>> connectionAnalysisCache; // Provider Name -> List of Consumer Names
    private final Map<String, Map<String, Map<String, Property>>> constraintPortsCache; // Provider Name -> (Constraint Type -> (Port Name -> Port Property))
    private final String hostLocation; // Cabinet's host location (room)
    private final boolean isRoomContext; // Flag indicating if this context is part of a room diagram
    private final String roomName; // Room name if this is part of a room diagram

    /**
     * Creates a new context for cabinet diagram operations
     * @param project The MagicDraw project
     * @param cabinetBlock The cabinet block element
     * @param registry The connection registry
     * @param isRoomContext Whether this cabinet is part of a room diagram
     * @param roomName The room name if this is part of a room diagram (null for standalone)
     * @param precachedProperties Pre-cached properties for this cabinet (null to load from block)
     */
    public CabinetDiagramContext(Project project, Class cabinetBlock, ConnectionRegistry registry,
                                boolean isRoomContext, String roomName, Map<String, Property> precachedProperties) {
        // Call parent constructor with cabinet name as context name
        super(project, registry, (cabinetBlock != null) ? cabinetBlock.getName() : null);

        this.cabinetBlock = cabinetBlock;

        // 1. Validate Cabinet Name
        if (this.contextName == null) {
            throw new IllegalArgumentException("This is not a recognized Cabinet block in the model.");
        }

        // 2. Providers
        Map<String, List<String>> cabinetProvidersMap = registry.getCabinetToPowerProviders();
        this.providers = cabinetProvidersMap.getOrDefault(this.contextName, Collections.emptyList());

        // 3. Part Properties (Map by Name for easier lookup)
        if (precachedProperties != null && !precachedProperties.isEmpty()) {
            this.partPropertyMap = new HashMap<>(precachedProperties);
        } else {
            // Otherwise collect properties from the cabinet block
            this.partPropertyMap = new HashMap<>();
            if (cabinetBlock != null) {
                for (Property property : cabinetBlock.getOwnedAttribute()) {
                    // Store all owned attributes (parts and constraint properties) by their name
                    partPropertyMap.put(property.getName(), property);
                }
            }
        }

        // 4. Initialize Connection Cache
        this.connectionAnalysisCache = new HashMap<>();

        // 5. Initialize Constraint Ports Cache (cabinet-specific)
        this.constraintPortsCache = new HashMap<>();

        // 6. Get the cabinet's host location
        String hostLocation = null;
        // First try to get host_location from pre-cached properties
        Property hostLocationProp = partPropertyMap.get("host_location");
        if (hostLocationProp != null && hostLocationProp.getType() != null) {
            hostLocation = hostLocationProp.getType().getName();
        } else {
            // Fall back to searching through all properties
            if (cabinetBlock != null) {
                for (Property property : cabinetBlock.getOwnedAttribute()) {
                    if ("host_location".equals(property.getName()) && property.getType() != null) {
                        hostLocation = property.getType().getName();
                        break;
                    }
                }
            }
        }
        this.hostLocation = hostLocation;
        this.isRoomContext = isRoomContext;
        this.roomName = roomName;

        // 7. Preload external_load properties for all providers in this cabinet
        preloadExternalLoadProperties();
    }

    // Implementation of abstract methods from PowerDiagramContext

    @Override
    public Class getContextBlock() {
        return cabinetBlock;
    }

    @Override
    public List<String> getProviders() {
        return Collections.unmodifiableList(providers);
    }

    @Override
    public Property getPartProperty(String propertyName) {
        return partPropertyMap.get(propertyName);
    }

    @Override
    public String getContextType() {
        return "CABINET";
    }

    @Override
    public boolean isRoomContext() {
        return isRoomContext;
    }

    /**
     * @return The cabinet block class
     */
    public Class getCabinetBlock() {
        return cabinetBlock;
    }

    /**
     * @return The name of the cabinet
     */
    public String getCabinetName() {
        return contextName;
    }

    /**
     * @return The host location (room) of the cabinet, or null if not found
     */
    public String getHostLocation() {
        return hostLocation;
    }



    /**
     * @return The room name if this is part of a room diagram, or null otherwise
     */
    public String getRoomName() {
        return roomName;
    }

    /**
     * Gets the host location for a cabinet
     * @param cabinetName The cabinet name
     * @return The host location name or null if not found
     */
    public String getCabinetHostLocation(String cabinetName) {
        if (cabinetName == null) {
            return null;
        }

        // If it's the current cabinet, return from instance variable
        if (cabinetName.equals(this.contextName)) {
            return hostLocation;
        }

        // Otherwise, get directly from Utilities.CabinetProperties
        return Utilities.CabinetProperties.getCabinetHostLocationFromCache(cabinetName);
    }

    /**
     * Gets all cabinet host locations
     * @return Map of cabinet names to their host locations
     */
    public Map<String, String> getCabinetHostLocations() {
        // Create a map of all cabinet host locations
        Map<String, String> result = new HashMap<>();

        // Add the current cabinet's host location
        if (contextName != null && hostLocation != null) {
            result.put(contextName, hostLocation);
        }

        // Get all cabinets from the registry
        Set<String> allCabinets = registry.getCabinets();

        // For each cabinet, get its host location
        for (String cabinet : allCabinets) {
            if (!cabinet.equals(contextName)) { // Skip the current cabinet as it's already added
                String location = Utilities.CabinetProperties.getCabinetHostLocationFromCache(cabinet);
                if (location != null) {
                    result.put(cabinet, location);
                }
            }
        }

        return result;
    }

    /**
     * @return List of power provider names in this cabinet that have consumers
     */
    public List<String> getProvidersWithConsumers() {
        List<String> result = new ArrayList<>();
        for (String providerName : providers) {
            List<String> consumers = getConsumersForProvider(providerName);
            if (consumers != null && !consumers.isEmpty()) {
                result.add(providerName);
            }
        }
        return Collections.unmodifiableList(result);
    }

    /**
     * Gets the top-level power providers in this cabinet.
     * Top providers are those that either:
     * 1. Have connections to room-level power sources, OR
     * 2. Have no internal power sources within the cabinet
     *
     * @return List of top power provider names in this cabinet
     */
    public List<String> getTopProviders() {
        if (contextName == null) {
            return Collections.emptyList();
        }

        Set<String> topProviders = registry.getCabinetTopProviders().getOrDefault(contextName, Collections.emptySet());
        return new ArrayList<>(topProviders);
    }

    /**
     * Gets the child power providers in this cabinet.
     * Child providers are those that receive power from other providers within the same cabinet.
     *
     * @return List of child power provider names in this cabinet
     */
    public List<String> getChildProviders() {
        List<String> topProviders = getTopProviders();
        List<String> result = new ArrayList<>();

        for (String provider : providers) {
            if (!topProviders.contains(provider)) {
                result.add(provider);
            }
        }

        return Collections.unmodifiableList(result);
    }

    /**
     * Gets all child providers that are supplied by a given top provider.
     *
     * @param topProvider The top provider name
     * @return List of child provider names supplied by this top provider
     */
    public List<String> getChildProvidersForTop(String topProvider) {
        if (topProvider == null) {
            return Collections.emptyList();
        }

        // Get direct power targets of this top provider
        Set<String> powerTargets = registry.getPowerTargets(topProvider);
        if (powerTargets.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> result = new ArrayList<>();

        // Filter targets to only include providers in this cabinet (excluding consumers)
        for (String target : powerTargets) {
            if (providers.contains(target)) {
                result.add(target);
            }
        }

        return Collections.unmodifiableList(result);
    }

    /**
     * Checks if a provider is a top-level provider in this cabinet.
     *
     * @param providerName The provider name to check
     * @return true if the provider is a top provider, false otherwise
     */
    public boolean isTopProvider(String providerName) {
        return getTopProviders().contains(providerName);
    }

    /**
     * Gets all properties for a cabinet
     * @param cabinetName The cabinet name
     * @return Map of property names to Property objects, or null if not found
     */
    public Map<String, Property> getCabinetProperties(String cabinetName) {
        if (cabinetName == null) {
            return null;
        }

        // If it's the current cabinet, return from instance variable
        if (cabinetName.equals(this.contextName)) {
            return partPropertyMap;
        }

        // Check if we're in a room context and the properties are in the registry's cache
        // Try to get from Utilities cache first
        Map<String, Property> cachedProperties = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinetName);
        if (cachedProperties != null && !cachedProperties.isEmpty()) {
            return cachedProperties;
        }

        return null;
    }

    /**
     * Gets all property values for a cabinet
     * @param cabinetName The cabinet name
     * @return Map of property names to property values, or null if not found
     */
    public Map<String, Object> getCabinetPropertyValues(String cabinetName) {
        if (cabinetName == null) {
            return null;
        }

        // If it's the current cabinet, we need to convert the partPropertyMap to values
        if (cabinetName.equals(this.contextName)) {
            Map<String, Object> values = new HashMap<>();
            for (Map.Entry<String, Property> entry : partPropertyMap.entrySet()) {
                Object value = Utilities.ModelElements.getPropertyValue(entry.getValue());
                values.put(entry.getKey(), value);
            }
            return values;
        }

        // Otherwise, get the properties and convert to values
        Map<String, Property> properties = getCabinetProperties(cabinetName);
        if (properties != null) {
            Map<String, Object> values = new HashMap<>();
            for (Map.Entry<String, Property> entry : properties.entrySet()) {
                Object value = Utilities.ModelElements.getPropertyValue(entry.getValue());
                values.put(entry.getKey(), value);
            }
            return values;
        }

        return null;
    }

    /**
     * Gets the PropertyCollection for a cabinet
     * @param cabinetName The cabinet name
     * @return PropertyCollection containing both property objects and values, or null if not found
     */
    public Utilities.PropertyCollection getCabinetPropertyCollection(String cabinetName) {
        if (cabinetName == null) {
            return null;
        }

        // If it's the current cabinet, we need to create a PropertyCollection
        if (cabinetName.equals(this.contextName)) {
            Map<String, Object> values = getCabinetPropertyValues(cabinetName);
            return new Utilities.PropertyCollection(values, partPropertyMap);
        }

        // Otherwise, create a new PropertyCollection
        Map<String, Property> properties = getCabinetProperties(cabinetName);
        Map<String, Object> values = getCabinetPropertyValues(cabinetName);

        if (properties != null && values != null) {
            return new Utilities.PropertyCollection(values, properties);
        }

        return null;
    }

    /**
     * Gets all consumers for a given provider using optimized ConnectionRegistry method
     * @param providerName The provider name
     * @return List of consumer names
     */
    public List<String> getConsumersForProvider(String providerName) {
        // First check if we already have this provider's consumers cached
        if (connectionAnalysisCache.containsKey(providerName)) {
            return connectionAnalysisCache.get(providerName);
        }

        List<String> consumers = registry.getConsumersForProvider(providerName);

        // Cache the result for future use
        List<String> immutableResult = Collections.unmodifiableList(consumers);
        connectionAnalysisCache.put(providerName, immutableResult);
        return immutableResult;
    }

    /**
     * Caches an external_load property for a specific provider
     * @param providerName The provider name
     * @param property The external_load property
     */
    public void cacheExternalLoadProperty(String providerName, Property property) {
        if (providerName != null && property != null) {
            externalLoadPropertyCache.put(providerName, property);
        }
    }

    /**
     * Pre-loads all external_load properties for all providers in this cabinet
     * This is called during initialization to avoid repeated lookups later
     */
    private void preloadExternalLoadProperties() {
        if (cabinetBlock == null) return;

        for (String providerName : providers) {
            String propName = "external_load_" + providerName;

            // First check if the property is already in the partPropertyMap
            Property property = partPropertyMap.get(propName);

            // If not found in partPropertyMap, fall back to searching in the cabinet block
            if (property == null) {
                property = Utilities.ModelElements.findPropertyByName(cabinetBlock, propName);
            }

            if (property != null) {
                cacheExternalLoadProperty(providerName, property);
            }
        }
    }

    // Note: External load, constraint properties, and value properties methods are now inherited from PowerDiagramContext

    /**
     * Caches value properties for a specific element (compatibility method)
     * @param elementName The element name (provider or consumer name)
     * @param valueProperties Map of property name to Property object
     */
    public void cacheValueProperties(String elementName, Map<String, Property> valueProperties) {
        if (elementName != null && valueProperties != null && !valueProperties.isEmpty()) {
            for (Map.Entry<String, Property> entry : valueProperties.entrySet()) {
                cacheValueProperty(elementName, entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * Caches constraint ports for a specific provider and constraint type
     * @param providerName The provider name
     * @param constraintType The type of constraint (e.g., "Power_Total")
     * @param ports Map of port name to Port property
     */
    public void cacheConstraintPorts(String providerName, String constraintType, Map<String, Property> ports) {
        if (providerName != null && constraintType != null && ports != null && !ports.isEmpty()) {
            // Get or create the map for this provider
            Map<String, Map<String, Property>> providerConstraintPorts = constraintPortsCache.computeIfAbsent(
                providerName, k -> new HashMap<>());

            // Store the ports map for this constraint type
            providerConstraintPorts.put(constraintType, new HashMap<>(ports));
        }
    }

    /**
     * Gets all constraint ports for a specific provider and constraint type
     * @param providerName The provider name
     * @param constraintType The type of constraint (e.g., "Power_Total")
     * @return Map of port name to Port property, or empty map if none found
     */
    public Map<String, Property> getConstraintPorts(String providerName, String constraintType) {
        if (providerName == null || constraintType == null) {
            return new HashMap<>();
        }

        // Get the map for this provider
        Map<String, Map<String, Property>> providerConstraintPorts = constraintPortsCache.get(providerName);
        if (providerConstraintPorts == null) {
            return new HashMap<>();
        }

        // Get the ports for this constraint type
        return providerConstraintPorts.getOrDefault(constraintType, new HashMap<>());
    }
}
