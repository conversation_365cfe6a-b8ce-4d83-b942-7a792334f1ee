from core.imports import *
from core.constants import PAC<PERSON>GE_VIEWS, SYSTEM_NAMING
from utils.md_utils import printer

class OldConnectionService:
    def __init__(self, project, package_service, creator_service, assets_dict):
        self.project = project
        self.package_service = package_service
        self.creator = creator_service
        self.existing_assets_dict = assets_dict
        self.connections_view = PACKAGE_VIEWS["CONNECTION"]
        self.connections_package = f.byQualifiedName().find(self.project, self.connections_view)

    def create_connections_package(self, system_name):
        """Create (or recreate) system-specific connections package"""
        package_name = SYSTEM_NAMING["CONNECTION"].format(system_name)
        existing_package = f.byQualifiedName().find(self.project, "{0}::{1}".format(self.connections_view, package_name))

        if existing_package:
            mem.getInstance().removeElement(existing_package)

        return self.package_service.create_package(package_name, self.connections_package)

    def create_connection(self, connection_data, connection_package):
        """Create single connection with all related elements"""
        # Create connection context block
        connection_name = connection_data['source']['name'] + '_' + connection_data['source_port']['name'] + '_' + \
                          connection_data['target']['name'] + '_' + connection_data['target_port']['name']
        context_block = self.creator.create_IDP_Element(connection_name, None, connection_package)

        # Get ports
        source_port = self._get_port(connection_data['source']['id'], connection_data['source_port']['name'])
        target_port = self._get_port(connection_data['target']['id'], connection_data['target_port']['name'])

        # Early validation of ports
        if source_port is None or target_port is None:
            if source_port is None:
                printer("ERROR: Source port {0} not found for asset {1}. Please update the new port in Material catalog".format(
                    connection_data['source_port']['name'],
                    connection_data['source']['name']))
            if target_port is None:
                printer("ERROR: Target port {0} not found for asset {1}. Please update the new port in Material catalog".format(
                    connection_data['target_port']['name'],
                    connection_data['target']['name']))
            return None

        # Create relationships and add parts
        self._add_source_elements(context_block, connection_data, connection_package)
        self._add_target_elements(context_block, connection_data, connection_package)
        self._add_cable_elements(context_block, connection_data, connection_package)

        # Create port connections based on connection type
        self._create_port_connections(context_block, connection_data, source_port, target_port)

        return context_block

    def _get_port(self, asset_id, port_name):
        """Get port from asset by ID and port name"""
        port_qn = "{0}::{1}".format(
            self.existing_assets_dict[asset_id]['qualified_name'],
            port_name
        )
        port = f.byQualifiedName().find(self.project, port_qn)
        if not port:
            printer("ERROR: Port {0} not found for asset {1}".format(port_name, asset_id))
        return port

    def _add_source_elements(self, context_block, connection_data, package):
        # Add source asset
        source_element = f.byQualifiedName().find(
            self.project,
            self.existing_assets_dict[connection_data['source']['id']]['qualified_name']
        )
        if source_element:
            source_id_prefix = connection_data['source']['id'].split('-')[0]
            source_name = "{0}_{1}".format(connection_data['source']['name'], source_id_prefix)
            self.creator.createCompositeRelationship(context_block, source_element,
                                                     source_name, package)

        # Add all source connectors in order
        if connection_data['source_connectors']:
            for connector_data in connection_data['source_connectors']:
                connector = f.byQualifiedName().find(
                    self.project,
                    self.existing_assets_dict[connector_data['id']]['qualified_name']
                )
                if connector:
                    self.creator.createCompositeRelationship(context_block, connector,
                                                             connector_data['name'], package)

    def _add_target_elements(self, context_block, connection_data, package):
        # Similar to _add_source_elements but for target side
        target_element = f.byQualifiedName().find(
            self.project,
            self.existing_assets_dict[connection_data['target']['id']]['qualified_name']
        )
        if target_element:
            target_id_prefix = connection_data['target']['id'].split('-')[0]
            target_name = "{0}_{1}".format(connection_data['target']['name'], target_id_prefix)
            self.creator.createCompositeRelationship(context_block, target_element,
                                                     target_name, package)

        # Add all target connectors in order
        if connection_data['target_connectors']:
            for connector_data in connection_data['target_connectors']:
                connector = f.byQualifiedName().find(
                    self.project,
                    self.existing_assets_dict[connector_data['id']]['qualified_name']
                )
                if connector:
                    self.creator.createCompositeRelationship(context_block, connector,
                                                             connector_data['name'], package)

    def _add_cable_elements(self, context_block, connection_data, package):
        if connection_data['cable'] and connection_data['cable']['id']:
            cable = f.byQualifiedName().find(
                self.project,
                self.existing_assets_dict[connection_data['cable']['id']]['qualified_name']
            )
            if cable:
                self.creator.createCompositeRelationship(context_block, cable, cable.getName(), package)

    def _create_port_connections(self, context_block, connection_data, source_port, target_port):
        """Create connections between ports based on connection configuration"""
        owned_parts = context_block.getPart()

        # Cable exists but no connectors
        if connection_data['cable'] and not connection_data['source_connectors'] and not connection_data['target_connectors']:
            #Accounting for scenario where connection is to the same asset
            if len(owned_parts) <= 2:
                # Connection: From Asset Port to Cable
                self.creator.createConnection(context_block, owned_parts[0], owned_parts[1], source_port, None)
                # Connection: Cable to Target Asset Port
                self.creator.createConnection(context_block, owned_parts[1], owned_parts[0], None, target_port)
            else:
                # Connection: From Asset Port to Cable
                self.creator.createConnection(context_block, owned_parts[0], owned_parts[2], source_port, None)
                # Connection: Cable to Target Asset Port
                self.creator.createConnection(context_block, owned_parts[2], owned_parts[1], None, target_port)

        # Direct connection - no cable or connectors
        elif not connection_data['cable'] and not connection_data['source_connectors'] and not connection_data['target_connectors']:
            # Connection: Port to Port
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[1], source_port, target_port)

        # Cable with source connector only
        elif connection_data['cable'] and connection_data['source_connectors'] and not connection_data['target_connectors']:
            # Connection: From Asset to first Source Connector
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[1], source_port, None)
            # Connect connectors in series
            for idx in range(1, len(connection_data['source_connectors'])):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx+1], None, None)

            last_connector_idx = len(connection_data['source_connectors'])
            # Connect last connector to cable
            self.creator.createConnection(context_block, owned_parts[last_connector_idx], owned_parts[-1], None, None)
            # Connect cable to target
            self.creator.createConnection(context_block, owned_parts[-1], owned_parts[-2], None, target_port)

        # Cable with target connector only
        elif connection_data['cable'] and not connection_data['source_connectors'] and connection_data['target_connectors']:
            # Connection: Connect source to cable
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[-1], source_port, None)
            # Connect cable to first target connector
            self.creator.createConnection(context_block, owned_parts[-1], owned_parts[2], None, None)
            # Connect connectors in series
            for idx in range(2, len(connection_data['target_connectors'])+1):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx+1], None, None)
            last_connector_idx = len(connection_data['target_connectors']) + 1
            # Connect last connector to target device
            self.creator.createConnection(context_block, owned_parts[last_connector_idx], owned_parts[1], None, target_port)

        # No cable, source connectors only
        elif not connection_data['cable'] and connection_data['source_connectors'] and not connection_data['target_connectors']:
            # Connect source to first connector
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[1], source_port, None)
            # Connect connectors in series
            for idx in range(2, len(owned_parts)-1):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx-1], None, None)
            # Connect last connector to target
            self.creator.createConnection(context_block, owned_parts[-2], owned_parts[-1], None, target_port)

        # No cable, target connector only
        elif not connection_data['cable'] and not connection_data['source_connectors'] and connection_data['target_connectors']:
            # Connect source to first target connector
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[2], source_port, None)
            # Connect target connectors in series
            for idx in range(3, 2 + len(connection_data['target_connectors'])):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx-1], None, None)
            # Connect last connector to target
            self.creator.createConnection(context_block, owned_parts[-1], owned_parts[1], None, target_port)

        # Cable with both connectors
        elif connection_data['cable'] and connection_data['source_connectors'] and connection_data['target_connectors']:
            source_conn_start = 1  # Start after source
            target_end = source_conn_start + len(connection_data['source_connectors'])
            target_conn_start = target_end + 1

            # Connect source to first source connector
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[source_conn_start], source_port, None)
            # Connect source connectors in series
            for idx in range(source_conn_start, target_end-1):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx+1], None, None)
            # Connect last source connector to cable
            self.creator.createConnection(context_block, owned_parts[target_end-1], owned_parts[-1], None, None)
            # Connect cable to first target connector
            self.creator.createConnection(context_block, owned_parts[-1], owned_parts[target_conn_start], None, None)
            # Connect target connectors in series
            for idx in range(target_conn_start, len(owned_parts)-2):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx+1], None, None)
            # Connect last target connector to target
            self.creator.createConnection(context_block, owned_parts[len(owned_parts)-2], owned_parts[target_end], None, target_port)

        # No cable but both connectors
        elif not connection_data['cable'] and connection_data['source_connectors'] and connection_data['target_connectors']:
            source_conn_start = 1
            source_conn_end = source_conn_start + len(connection_data['source_connectors'])
            target_conn_start = source_conn_end+1
            target_conn_end = target_conn_start + len(connection_data['target_connectors'])

            # Connect source to first source connector
            self.creator.createConnection(context_block, owned_parts[0], owned_parts[source_conn_start], source_port, None)
            # Connect source connectors in series
            for idx in range(source_conn_start, source_conn_end-1):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx+1], None, None)
            # Connect last source connector to first target connector
            self.creator.createConnection(context_block, owned_parts[source_conn_end-1], owned_parts[target_conn_start], None, None)
            # Connect target connectors in series
            for idx in range(target_conn_start, target_conn_end-1):
                self.creator.createConnection(context_block, owned_parts[idx], owned_parts[idx+1], None, None)
            # Connect last target connector to target
            self.creator.createConnection(context_block, owned_parts[-1], owned_parts[-(len(connection_data['target_connectors'])+1)], None, target_port)
