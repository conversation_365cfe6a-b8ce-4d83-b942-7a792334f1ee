from core.imports import *
from core.constants import PACKAGE_VIEWS
from utils.md_utils import printer

class SystemService:
    """
    Manages system blocks in Systems View
    """
    def __init__(self, project, package_service, creator_service, systems_dict):
        self.project = project
        self.package_service = package_service
        self.creator = creator_service
        self.systems_view = PACKAGE_VIEWS["SYSTEMS"]
        self.systems_package = f.byQualifiedName().find(self.project, self.systems_view)
        self.existing_systems_dict = systems_dict

    def get_system_block(self, system_name):
        # Check dictionary first
        if system_name in self.existing_systems_dict:
            return f.byQualifiedName().find(self.project,
                                            self.existing_systems_dict[system_name]['qualified_name'])
        return None

    def create_system_block(self, system_name, system_data):
        """Create/update system block with properties"""
        system_block = self.get_system_block(system_name)

        # If no existing block, create new one with if not already present
        if not system_block and self.systems_package:
            system_block = self.creator.create_IDP_Element(
                system_name,
                "System",
                self.systems_package
            )

        if system_block:
            # Extract just the property data dict from the system data
            for system_name, properties in system_data.items():
                if isinstance(properties, dict):
                    data = {k: v for k, v in properties.items()}
                    self.creator.process_classifiers(system_block, "System", data)

        return system_block

    def set_property(self, system_block, prop_name, value):
        """Set property on system block"""
        try:
            self.creator.createPropGeneric(
                system_block,
                prop_name,
                value,
                "System"
            )
        except Exception as e:
            printer("Error setting property {0}: {1}".format(prop_name, str(e)))
