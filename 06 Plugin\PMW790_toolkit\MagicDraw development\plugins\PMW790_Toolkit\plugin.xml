<?xml version="1.0" encoding="UTF-8"?>
<plugin
        id="pmw790_toolkit"
        name="PMW790 Toolkit"
        version="1.0"
        provider-name="PMW790"
        class="com.pmw790.main.PMW790Plugin"
        ownClassloader="true">

    <requires>
        <api version="1.2"/>
    </requires>

    <runtime>
        <library name="PMW790_toolkit.jar"/>
        <!-- ELK Layout Dependencies (optional - graceful fallback if missing) -->
        <library name="lib/org.eclipse.elk.core_0.9.0.jar"/>
        <library name="lib/org.eclipse.elk.core.service_0.9.0.jar"/>
        <library name="lib/org.eclipse.elk.graph_0.9.0.jar"/>
        <library name="lib/org.eclipse.elk.graph.text_0.9.0.jar"/>
        <library name="lib/org.eclipse.elk.alg.common_0.9.0.jar"/>
        <library name="lib/org.eclipse.elk.alg.layered_0.9.0.jar"/>
        <library name="lib/org.eclipse.elk.alg.force_0.9.0.jar"/>
    </runtime>
</plugin>