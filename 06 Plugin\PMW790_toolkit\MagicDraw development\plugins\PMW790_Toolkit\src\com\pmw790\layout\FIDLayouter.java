package com.pmw790.layout;

import java.lang.reflect.Method;
import java.util.Collection;
import static com.pmw790.functions.Utilities.Log;

/**
 * Specialized ELK Layered layout engine for FID (Functional Interface Diagrams)
 * Uses only the ELK Layered algorithm for hierarchical component diagrams
 * with clean presentation and minimal edge crossings
 */
public class FIDLayouter {

    // ELK classes loaded via reflection to avoid compile-time dependencies
    private static Class<?> coreOptionsClass;
    private static Class<?> layeredOptionsClass;
    private static Class<?> directionEnum;
    private static Class<?> edgeRoutingEnum;
    private static Class<?> hierarchyHandlingEnum;
    private static Class<?> crossingMinimizationEnum;
    private static Class<?> layoutEngineClass;
    private static Class<?> progressMonitorClass;
    private static Class<?> portConstraintsEnum;
    private static Class<?> nodeLayeringEnum;
    private static boolean initialized = false;
    private static final Object initLock = new Object();
    
    // ELK Layered algorithm constants
    private static final String ALGORITHM_ID = "org.eclipse.elk.alg.layered";
    private static final String PROVIDER_CLASS = "org.eclipse.elk.alg.layered.LayeredLayoutProvider";
    
    /**
     * Lazy initialization of ELK classes
     */
    private static void initializeELKClasses() {
        if (initialized) return;
        
        synchronized (initLock) {
            if (initialized) return;
            
            try {
                // Use custom ELK classloader with fallback to standard loading
                ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
                
                Log("FIDLayouter: Loading ELK classes...");
                coreOptionsClass = elkClassLoader.loadClass("org.eclipse.elk.core.options.CoreOptions");
                Log("FIDLayouter: Loaded CoreOptions");
                
                // Try different class names for LayeredOptions depending on ELK version
                try {
                    layeredOptionsClass = elkClassLoader.loadClass("org.eclipse.elk.layered.options.LayeredOptions");
                    Log("FIDLayouter: Loaded LayeredOptions (new package)");
                } catch (ClassNotFoundException e1) {
                    try {
                        // Try old package structure
                        layeredOptionsClass = elkClassLoader.loadClass("org.eclipse.elk.alg.layered.options.LayeredOptions");
                        Log("FIDLayouter: Loaded LayeredOptions (alg package)");
                    } catch (ClassNotFoundException e2) {
                        Log("FIDLayouter: LayeredOptions not found in either package: " + e1.getMessage());
                        throw e2;
                    }
                }
                
                directionEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.Direction");
                edgeRoutingEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.EdgeRouting");
                hierarchyHandlingEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.HierarchyHandling");
                // Try different packages for layered algorithm classes
                try {
                    crossingMinimizationEnum = elkClassLoader.loadClass("org.eclipse.elk.layered.options.CrossingMinimizationStrategy");
                } catch (ClassNotFoundException e) {
                    crossingMinimizationEnum = elkClassLoader.loadClass("org.eclipse.elk.alg.layered.options.CrossingMinimizationStrategy");
                }
                
                portConstraintsEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.PortConstraints");
                
                try {
                    nodeLayeringEnum = elkClassLoader.loadClass("org.eclipse.elk.layered.options.LayeringStrategy");
                } catch (ClassNotFoundException e) {
                    nodeLayeringEnum = elkClassLoader.loadClass("org.eclipse.elk.alg.layered.options.LayeringStrategy");
                }
                layoutEngineClass = elkClassLoader.loadClass("org.eclipse.elk.core.RecursiveGraphLayoutEngine");
                progressMonitorClass = elkClassLoader.loadClass("org.eclipse.elk.core.util.BasicProgressMonitor");
                
                // Initialize algorithm registry
                initializeAlgorithmRegistry(elkClassLoader);
                
                // Initialize ELK reflection system for property cloning
                initializeElkReflect(elkClassLoader);
                
                initialized = true;
                Log("FIDLayouter: All ELK classes loaded successfully via classloader");
            } catch (ClassNotFoundException e) {
                Log("FIDLayouter: ELK class not found - " + e.getMessage());
                e.printStackTrace();
            } catch (Exception e) {
                Log("FIDLayouter: Error loading ELK classes - " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * Apply ELK Layered layout algorithm to a FID graph
     * 
     * @param elkGraph The ELK graph to layout
     * @return The laid-out ELK graph
     */
    public Object layoutFID(Object elkGraph) throws Exception {
        initializeELKClasses();

        if (coreOptionsClass == null || layeredOptionsClass == null) {
            throw new RuntimeException("ELK classes not available for FID layout");
        }

        Log("FIDLayouter: Using ELK Layered algorithm");
        
        // Use direct algorithm invocation
        return layoutFIDDirect(elkGraph);
    }

    /**
     * Apply ELK Layered layout using direct algorithm invocation
     */
    private Object layoutFIDDirect(Object elkGraph) throws Exception {
        ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();

        try {
            // Configure the graph with layered layout options
            configureFIDLayout(elkGraph);

            Log("FIDLayouter: Using direct algorithm provider: " + PROVIDER_CLASS);

            // Load and instantiate the layered algorithm provider
            Class<?> algorithmProviderClass = elkClassLoader.loadClass(PROVIDER_CLASS);
            Object algorithmProvider = algorithmProviderClass.newInstance();

            // Create progress monitor
            Class<?> progressMonitorClass = elkClassLoader.loadClass("org.eclipse.elk.core.util.BasicProgressMonitor");
            Object progressMonitor = progressMonitorClass.newInstance();

            // Call the layout method directly on the algorithm provider
            Method layoutMethod = algorithmProviderClass.getMethod("layout",
                elkClassLoader.loadClass("org.eclipse.elk.graph.ElkNode"),
                elkClassLoader.loadClass("org.eclipse.elk.core.util.IElkProgressMonitor"));

            Log("FIDLayouter: Invoking ELK Layered layout method...");
            layoutMethod.invoke(algorithmProvider, elkGraph, progressMonitor);

            Log("ELK Layered layout completed successfully");
            return elkGraph;

        } catch (java.lang.reflect.InvocationTargetException e) {
            Throwable cause = e.getCause();
            Log("FIDLayouter: Layered layout InvocationTargetException - Root cause: " + (cause != null ? cause.getClass().getName() + ": " + cause.getMessage() : "Unknown"));
            if (cause != null) {
                cause.printStackTrace();
            }
            throw new RuntimeException("ELK Layered layout failed: " + (cause != null ? cause.getMessage() : e.getMessage()), cause != null ? cause : e);
        } catch (Exception e) {
            Log("FIDLayouter: Layered layout failed: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("ELK Layered layout failed: " + e.getMessage(), e);
        }
    }

    
    /**
     * Configure ELK Layered layout for FID diagrams
     * Focuses on clean presentation, minimal edge crossings, and hierarchical organization
     */
    private void configureFIDLayout(Object elkGraph) throws Exception {
        Log("FIDLayouter: Configuring ELK Layered layout for FID");
        
        try {
            // Set ELK Layered algorithm
            setProperty(elkGraph, "ALGORITHM", ALGORITHM_ID);
            
            // Configure hierarchy handling for nested components (parts within parts)
            setProperty(elkGraph, "HIERARCHY_HANDLING", getEnumValue(hierarchyHandlingEnum, "INCLUDE_CHILDREN"));
            
            // Set layout direction - RIGHT works well for FID flow
            setProperty(elkGraph, "DIRECTION", getEnumValue(directionEnum, "RIGHT"));
            
            // Enable orthogonal edge routing for clean connector lines in FID
            setProperty(elkGraph, "EDGE_ROUTING", getEnumValue(edgeRoutingEnum, "ORTHOGONAL"));
            
            // Minimize edge crossings - critical for FID readability
            setProperty(elkGraph, "CROSSING_MINIMIZATION_STRATEGY", 
                       getEnumValue(crossingMinimizationEnum, "LAYER_SWEEP"));
            
            // Configure node layering for logical FID arrangement
            setProperty(elkGraph, "LAYERING_STRATEGY", 
                       getEnumValue(nodeLayeringEnum, "NETWORK_SIMPLEX"));
            
            // Set spacing parameters optimized for FID components
            setProperty(elkGraph, "SPACING_NODE_NODE", 80.0);        // Space between parts
            setProperty(elkGraph, "SPACING_EDGE_NODE", 25.0);        // Space between connectors and parts
            setProperty(elkGraph, "SPACING_EDGE_EDGE", 20.0);        // Space between parallel connectors
            setProperty(elkGraph, "SPACING_COMPONENT_COMPONENT", 40.0); // Space between component groups
            
            // Configure ports for proper connector attachment in FID
            setProperty(elkGraph, "PORT_CONSTRAINTS", getEnumValue(portConstraintsEnum, "FIXED_ORDER"));
            
            // Additional FID-specific optimizations
            setProperty(elkGraph, "SEPARATE_CONNECTED_COMPONENTS", false); // Keep components together
            setProperty(elkGraph, "INTERACTIVE_REFERENCE_POINT", "CENTER"); // Center-based layout
            
            Log("ELK Layered layout configured successfully for FID diagram");
            
        } catch (Exception e) {
            Log("FIDLayouter: Warning - Could not set some layout properties: " + e.getMessage());
            // Continue anyway - algorithm may work with defaults
        }
    }

    /**
     * Configure ELK Layered layout for FID with cabinet grouping integration
     * Preserves cabinet-based grouping while optimizing overall layout
     */
    public Object layoutFIDWithCabinetGrouping(Object elkGraph) throws Exception {
        initializeELKClasses();
        
        if (coreOptionsClass == null) {
            throw new RuntimeException("ELK classes not available for FID cabinet layout");
        }

        // Base FID configuration with layered algorithm for cabinet grouping
        configureFIDLayout(elkGraph);
        
        // Additional settings for cabinet-based grouping
        setProperty(elkGraph, "SPACING_NODE_NODE", 100.0);       // More space for cabinet groups
        setProperty(elkGraph, "SPACING_COMPONENT_COMPONENT", 60.0); // Space between cabinet groups
        
        // Prefer keeping related elements together (within cabinets)
        setProperty(elkGraph, "CONSIDER_MODEL_ORDER_STRATEGY", "PREFER_EDGES");
        
        // Create layout engine and apply layout
        Object layoutEngine = layoutEngineClass.newInstance();
        Object progressMonitor = progressMonitorClass.newInstance();
        
        ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
        Method layoutMethod = layoutEngineClass.getMethod("layout", 
            elkClassLoader.loadClass("org.eclipse.elk.graph.ElkNode"),
            elkClassLoader.loadClass("org.eclipse.elk.core.util.IElkProgressMonitor"));
            
        layoutMethod.invoke(layoutEngine, elkGraph, progressMonitor);
        
        Log("ELK Layered layout with cabinet grouping completed successfully");
        return elkGraph;
    }

    /**
     * Set a property on an ELK graph using reflection
     */
    private void setProperty(Object elkGraph, String propertyName, Object value) throws Exception {
        // Get the property field from CoreOptions or LayeredOptions
        Object property = null;
        
        try {
            property = coreOptionsClass.getField(propertyName).get(null);
        } catch (NoSuchFieldException e) {
            try {
                property = layeredOptionsClass.getField(propertyName).get(null);
            } catch (NoSuchFieldException e2) {
                Log("Warning: ELK property not found: " + propertyName);
                return;
            }
        }
        
        // Set the property on the graph using ELK classloader
        ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
        Class<?> iPropertyClass = elkClassLoader.loadClass("org.eclipse.elk.graph.properties.IProperty");
        Method setPropertyMethod = elkGraph.getClass().getMethod("setProperty", 
            iPropertyClass, Object.class);
        setPropertyMethod.invoke(elkGraph, property, value);
    }
    

    /**
     * Get an enum value by name using reflection
     */
    private Object getEnumValue(Class<?> enumClass, String valueName) throws Exception {
        if (enumClass == null) {
            return null;
        }
        return Enum.valueOf((Class<Enum>) enumClass, valueName);
    }



    private static void initializeElkReflect(ClassLoader elkClassLoader) {
    try {
        Log("FIDLayouter: Initializing ELK reflection system...");
        
        // Load ElkReflect utility class
        Class<?> elkReflectClass = elkClassLoader.loadClass("org.eclipse.elk.core.util.ElkReflect");
        
        // Register common ELK property types with ElkReflect
        Method registerMethod = elkReflectClass.getMethod("register", Class.class);
        
        // Register basic Java types that ELK properties use
        registerMethod.invoke(null, Double.class);
        registerMethod.invoke(null, Integer.class);
        registerMethod.invoke(null, Boolean.class);
        registerMethod.invoke(null, String.class);
        
        // Register ELK-specific types
        try {
            Class<?> paddingClass = elkClassLoader.loadClass("org.eclipse.elk.core.math.ElkPadding");
            registerMethod.invoke(null, paddingClass);
            Log("FIDLayouter: Registered ElkPadding type");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: ElkPadding class not found, continuing...");
        }
        
        try {
            Class<?> marginsClass = elkClassLoader.loadClass("org.eclipse.elk.core.math.ElkMargin");
            registerMethod.invoke(null, marginsClass);
            Log("FIDLayouter: Registered ElkMargin type");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: ElkMargin class not found, continuing...");
        }
        
        // Register additional ELK math types that may be used in properties
        try {
            Class<?> insetsClass = elkClassLoader.loadClass("org.eclipse.elk.core.math.ElkInsets");
            registerMethod.invoke(null, insetsClass);
            Log("FIDLayouter: Registered ElkInsets type");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: ElkInsets class not found, continuing...");
        }
        
        try {
            Class<?> rectangleClass = elkClassLoader.loadClass("org.eclipse.elk.core.math.ElkRectangle");
            registerMethod.invoke(null, rectangleClass);
            Log("FIDLayouter: Registered ElkRectangle type");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: ElkRectangle class not found, continuing...");
        }
        
        try {
            Class<?> dimensionClass = elkClassLoader.loadClass("org.eclipse.elk.core.math.KVectorChain");
            registerMethod.invoke(null, dimensionClass);
            Log("FIDLayouter: Registered KVectorChain type");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: KVectorChain class not found, continuing...");
        }
        
        // Register enum types used by ELK
        if (directionEnum != null) registerMethod.invoke(null, directionEnum);
        if (edgeRoutingEnum != null) registerMethod.invoke(null, edgeRoutingEnum);
        if (hierarchyHandlingEnum != null) registerMethod.invoke(null, hierarchyHandlingEnum);
        if (crossingMinimizationEnum != null) registerMethod.invoke(null, crossingMinimizationEnum);
        if (portConstraintsEnum != null) registerMethod.invoke(null, portConstraintsEnum);
        if (nodeLayeringEnum != null) registerMethod.invoke(null, nodeLayeringEnum);
        
        // Register additional enum types that might be used
        try {
            Class<?> nodeLabelPlacementEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.NodeLabelPlacement");
            registerMethod.invoke(null, nodeLabelPlacementEnum);
            Log("FIDLayouter: Registered NodeLabelPlacement enum");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: NodeLabelPlacement enum not found, continuing...");
        }
        
        try {
            Class<?> edgeLabelPlacementEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.EdgeLabelPlacement");
            registerMethod.invoke(null, edgeLabelPlacementEnum);
            Log("FIDLayouter: Registered EdgeLabelPlacement enum");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: EdgeLabelPlacement enum not found, continuing...");
        }
        
        try {
            Class<?> alignmentEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.Alignment");
            registerMethod.invoke(null, alignmentEnum);
            Log("FIDLayouter: Registered Alignment enum");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: Alignment enum not found, continuing...");
        }
        
        // Register any additional ELK data types that may be referenced by properties
        try {
            Class<?> sizeConstraintEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.SizeConstraint");
            registerMethod.invoke(null, sizeConstraintEnum);
            Log("FIDLayouter: Registered SizeConstraint enum");
        } catch (ClassNotFoundException e) {
            Log("FIDLayouter: SizeConstraint enum not found, continuing...");
        }
        
        Log("FIDLayouter: ELK reflection system initialized successfully");
        
    } catch (ClassNotFoundException e) {
        Log("FIDLayouter: ElkReflect class not found - property cloning may fail: " + e.getMessage());
        // Continue without ElkReflect initialization - some features may not work
    } catch (Exception e) {
        Log("FIDLayouter: Warning - Could not initialize ELK reflection system: " + e.getMessage());
        e.printStackTrace();
        // Continue anyway - basic layout may still work
    }
}

    private static void initializeAlgorithmRegistry(ClassLoader elkClassLoader) {
        try {
            Log("FIDLayouter: Initializing ELK algorithm registry...");

            // The ELK service layer classes are not available in the current JAR set
            // Skip service layer registration and rely on direct algorithm invocation
            Log("FIDLayouter: Skipping service layer registration - using direct algorithm invocation");
            Log("FIDLayouter: Available algorithms will be called directly via their provider classes");

        } catch (Exception e) {
            Log("FIDLayouter: Warning - Could not initialize algorithm registry: " + e.getMessage());
        }
    }
}