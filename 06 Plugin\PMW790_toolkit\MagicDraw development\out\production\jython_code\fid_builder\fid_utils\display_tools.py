# -*- coding: utf-8 -*-
from fid_utils.md_utils import printer, isBlock, isPartProperty
from fid_services.datatools import sort_by_host
from fid_services.finders import find_and_delete_existing_diagram, get_host_asset, get_host_location, get_tag
from fid_core.imports import *

from com.nomagic.magicdraw.openapi.uml import PresentationElementsManager
from com.nomagic.magicdraw.properties import ChoiceProperty, PropertyID, PropertyManager;
from com.nomagic.magicdraw.uml.symbols.shapes import PartView
from com.nomagic.magicdraw.uml.symbols.shapes import ShapeElement, DiagramPropertiesShape
from java.awt import Rectangle, Color, Point
from com.nomagic.magicdraw.sysml.util import SysMLConstants
from java.text import SimpleDateFormat
from java.util import Date
from com.nomagic.magicdraw.uml.symbols.layout.orderedhier import OrderedHierarchicDiagramLayouter
from com.nomagic.magicdraw.core.options import HierarchicLayouterOptionsGroup
from com.nomagic.magicdraw.uml.symbols import CompartmentManager, CompartmentID;

def create_timestamped_diagram(diagram_type, owner_element, name_prefix):
    """
    Creates a diagram with timestamped name to avoid conflicts
    
    :param diagram_type: The type of diagram to create
    :param owner_element: The element that will own the diagram
    :param name_prefix: The prefix for the diagram name
    :return: The created diagram element
    """
    new_diagram = mem.getInstance().createDiagram(diagram_type, owner_element)
    
    timestamp_format = SimpleDateFormat("yyyyMMdd_HHmmss")
    timestamp = timestamp_format.format(Date())
    owner_name = owner_element.getName()
    diagram_name = owner_name + "_" + name_prefix + " " + timestamp
    
    new_diagram.setName(diagram_name)
    return new_diagram

def hideCompartmentInfo(symbol, tag_to_hide):
    if tag_to_hide is not None:
        CompartmentManager.hideCompartmentElement(symbol, CompartmentID.TAGGED_VALUES_ON_SHAPE, tag_to_hide)

def showCompartmentInfo(symbol, tag_to_show):
    CompartmentManager.showCompartmentElement(symbol, CompartmentID.TAGGED_VALUES_ON_SHAPE, tag_to_show)

def collect_all_pes_with_leaf_check(pe, seen, level):
    result = []
    subtree = []

    def recurse(pe, level):
        if pe in seen:
            return
        seen.add(pe)

        if pe.isVisible() and isinstance(pe, PartView):
            subtree.append((pe, level))

        for child in pe.getPresentationElements():
            next_level = level + 1 if isinstance(pe, PartView) else level
            recurse(child, next_level)

    # Traverse one root tree
    recurse(pe, 0)

    def is_leaf(pv):
        def has_child_partview_recursive(pe):
            for child in pe.getPresentationElements():
                if isinstance(child, PartView) and child.isVisible():
                    return True
                if has_child_partview_recursive(child):
                    return True
            return False

        return not has_child_partview_recursive(pv)
    
    for pe, lvl in subtree:
        result.append({
            "PE": pe,
            "Level": lvl,
            "Max_Depth": is_leaf(pe)
        })

    return result

def remove_classifier_from_presentation_element(presentationElement):
    o_prop = presentationElement.getProperty("SHOW_OBJECT_CLASS")
    c_prop = o_prop.clone()
    c_prop.setValue(False)
    pm = PropertyManager()
    pm.addProperty(c_prop)
    PresentationElementsManager.getInstance().setPresentationElementProperties(presentationElement,pm)

def addToFID(element, diagram, level, showClasses, showPorts, topLevel, connectors, connectedElements):
    if topLevel:
        for port in element.getOwnedPort():
            if port in connectedElements:
                df = diagram.getDiagramFrame()
                port_pe = PresentationElementsManager.getInstance().createShapeElement(port, df, connectors)
                
                # Set port properties - hide stereotypes like connectors
                pm_port = PropertyManager()
                show_stereotype = port_pe.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                if show_stereotype:
                    new_show_stereotype = show_stereotype.clone()
                    new_show_stereotype.setValue("STEREOTYPE_DISPLAY_MODE_DO_NOT_DISPLAY_STEREOTYPES")
                    pm_port.addProperty(new_show_stereotype)
                
                if pm_port.getProperties():
                    PresentationElementsManager.getInstance().setPresentationElementProperties(port_pe, pm_port)

    for part in element.getOwnedAttribute():
        if part not in connectedElements:
            continue
        if isPartProperty(part):
            if level >= 1:
                outer = PresentationElementsManager.getInstance().createShapeElement(part, diagram, connectors)
                if not showClasses:
                    if part.getType().getGeneral():
                        classifierName = part.getType().getGeneral().get(0).getName()
                        ###needs to be updated to support req
                        if classifierName in ["Transceiver","Cable","Connector"]:
                            remove_classifier_from_presentation_element(outer)
                if showPorts:
                    if part.getType().getOwnedPort():
                        for port in part.getType().getOwnedPort():
                                if port in connectedElements:
                                    out = PresentationElementsManager.getInstance().createShapeElement(port, outer, connectors)
                                    
                                    # Set port properties - hide stereotypes like connectors
                                    pm_port = PropertyManager()
                                    show_stereotype = out.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                                    if show_stereotype:
                                        new_show_stereotype = show_stereotype.clone()
                                        new_show_stereotype.setValue("STEREOTYPE_DISPLAY_MODE_DO_NOT_DISPLAY_STEREOTYPES")
                                        pm_port.addProperty(new_show_stereotype)
                                    
                                    if pm_port.getProperties():
                                        PresentationElementsManager.getInstance().setPresentationElementProperties(out, pm_port)
                if level > 1:
                    addToFID(part.getType(), outer, level - 1, showClasses, showPorts, False, connectors, connectedElements)
            else:
                printer("Selected element is not supported, please try another.")

def find_shape_element_for(model_element, diagram_presentation_element):
    """Find the presentation element for a given model element within a diagram.
    
    Parameters
    ----------
    model_element : Element
        The model element to find
    diagram_presentation_element : PresentationElement
        The diagram to search within
        
    Returns
    -------
    PresentationElement or None
        The presentation element if found, None otherwise
    """
    def recurse(pes):
        for pe in pes:
            if pe.getElement() == model_element:
                return pe
            nested_result = recurse(pe.getPresentationElements())
            if nested_result:
                return nested_result
        return None

    return recurse(diagram_presentation_element.getPresentationElements())

def fidCreator_v1(selectedElement, level_var, disp_cons_list, model_parts, show_ports):
    """
    Gather all information required to build an IBD.
    Returns a spec dict containing owner, depth, parts to display, deduped connector endpoints, and flags.
    """
    if not isBlock(selectedElement):
        printer('ERROR: Please select a block element')
        return None

    # Deduplicate and collect connector endpoints for later creation
    connector_data = []
    seen_connectors = set()
    for connector in disp_cons_list:
        if connector in seen_connectors:
            continue
        seen_connectors.add(connector)
        ends = connector.getEnd()
        if len(ends) != 2:
            continue
        source_role = ends[0].getRole()
        target_role = ends[1].getRole()
        connector_data.append((connector, source_role, target_role))

    spec = {
        "owner": selectedElement,
        "depth": level_var,
        "display_parts": model_parts,
        "show_ports": show_ports,
        "connector_data": connector_data,
        "diagram_type": SysMLConstants.SYSML_INTERNAL_BLOCK_DIAGRAM,
        "name_prefix": "FID_L2"
    }
    return spec

def create_ibd_diagram_from_spec(spec):
    """
    Create the IBD diagram and render part/port shapes according to the provided spec.
    Does NOT create connectors and does NOT open the diagram. Returns the diagram presentation element.
    """
    project = Application.getInstance().getProject()
    owner = spec["owner"]
    depth = spec["depth"]
    show_ports = spec["show_ports"]
    display_parts = spec["display_parts"]

    sm.getInstance().createSession(project, 'Create IBD From Spec')
    try:
        # Ensure previous FID_L2 for this owner is removed
        find_and_delete_existing_diagram(project, owner, spec["diagram_type"], spec["name_prefix"])

        # Create new timestamped diagram
        new_diagram = create_timestamped_diagram(spec["diagram_type"], owner, spec["name_prefix"])
        ibd_ = project.getDiagram(new_diagram)

        # Add shapes (parts/ports) only
        addToFID(owner, ibd_, depth, False, show_ports, True, False, display_parts)

        # Style adjustments for leaves and top-levels
        seen = set()
        all_pes = []
        diagram_prop_pe = None
        for pe in ibd_.getPresentationElements():
            if isinstance(pe, PartView) and pe not in seen:
                all_pes.extend(collect_all_pes_with_leaf_check(pe, seen, 0))
            if isinstance(pe, DiagramPropertiesShape):
                diagram_prop_pe = pe

        for sel in all_pes:
            p_e = sel["PE"]
            class_view = p_e.getProperty(PropertyID.SHOW_OBJECT_CLASS)
            if class_view:
                no_cv = class_view.clone()
                no_cv.setValue("False")
                pm = PropertyManager()
                pm.addProperty(no_cv)
            else:
                pm = PropertyManager()

            if sel["Max_Depth"] == True:
                change_el = sel["PE"]
                bounds = change_el.getBounds()
                new_bounds = Rectangle(bounds.x, bounds.y, 200, 100)
                vis_st_disp = change_el.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                vis_struct = change_el.getProperty(PropertyID.SUPPRESS_STRUCTURE)
                if vis_st_disp.getValue() != "STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE":
                    new_vis = vis_st_disp.clone()
                    new_vis.setValue("STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE")
                    new_vis_2 = vis_struct.clone()
                    new_vis_2.setValue("True")
                    pm.addProperty(new_vis)
                    pm.addProperty(new_vis_2)
                    PresentationElementsManager.getInstance().setPresentationElementProperties(change_el, pm)
                    PresentationElementsManager.getInstance().reshapeShapeElement(change_el, new_bounds)
            else:
                same_el = sel["PE"]
                fill_color = same_el.getProperty(PropertyID.FILL_COLOR)
                if fill_color.getValue() != Color(255, 255, 255):
                    fc = fill_color.clone()
                    fc.setValue(Color(255, 255, 255))
                    pm.addProperty(fc)
                if sel["Level"] == 0:
                    border_color = same_el.getProperty(PropertyID.PEN_COLOR)
                    if border_color.getValue() != Color(0, 0, 255):
                        bc = border_color.clone()
                        bc.setValue(Color(0, 0, 255))
                        pm.addProperty(bc)
                if pm.getProperties():
                    PresentationElementsManager.getInstance().setPresentationElementProperties(same_el, pm)

        return ibd_
    finally:
        sm.getInstance().closeSession()

def group_elements_in_diagram(selected_ibd, systemCabinetMap=None):
    """
    Create cabinet-based grouping hierarchy for elements within the provided diagram.
    Returns the cabinet hierarchy for use by ELK layout service.
    """
    part_props = {}
    diagram_size = selected_ibd.getBounds()

    # Build part properties mapping from current diagram shapes
    for pe in selected_ibd.getPresentationElements():
        if pe.getHumanType() == "Part Property":
            part_props[pe] = {
                "name": pe.getElement().getName(),
                "presentation_element": pe,
                "element": pe.getElement(),
                "host_asset": get_host_asset(pe)
            }

    hierarchy = None
    try:
        if systemCabinetMap and len(part_props) > 0:
            validated_props = validate_grouping_with_cache(part_props, systemCabinetMap)
            hierarchy = create_cabinet_hierarchy_grouping(validated_props)
        else:
            basic_part_props = {}
            for pe, prop_data in part_props.items():
                basic_part_props[pe] = {
                    "pe": pe,
                    "name": pe.getName(),
                    "bounds": pe.getBounds(),
                    "x": pe.getBounds().getX(),
                    "y": pe.getBounds().getY(),
                    "width": pe.getBounds().getWidth(),
                    "height": pe.getBounds().getHeight(),
                    "host_asset": prop_data["host_asset"]
                }
            chunked = sort_by_host(basic_part_props)
            # Convert to hierarchy format for consistency
            hierarchy = {"Unknown System": {}}
            for host_asset, elements in chunked.items():
                hierarchy["Unknown System"][host_asset] = {
                    "room": "Unknown Room",
                    "elements": elements
                }
            printer("Created basic hierarchy with {} groups".format(len(hierarchy["Unknown System"])))
    except Exception as e:
        printer("ERROR: Exception in group_elements_in_diagram: {}".format(str(e)))
        import traceback
        printer("ERROR: Traceback: {}".format(traceback.format_exc()))
        # Fallback to basic grouping on error
        basic_part_props = {}
        for pe, prop_data in part_props.items():
            basic_part_props[pe] = {
                "pe": pe,
                "name": pe.getName(),
                "bounds": pe.getBounds(),
                "x": pe.getBounds().getX(),
                "y": pe.getBounds().getY(),
                "width": pe.getBounds().getWidth(),
                "height": pe.getBounds().getHeight(),
                "host_asset": prop_data["host_asset"]
            }
        chunked = sort_by_host(basic_part_props)
        hierarchy = {"Unknown System": {}}
        for host_asset, elements in chunked.items():
            hierarchy["Unknown System"][host_asset] = {
                "room": "Unknown Room",
                "elements": elements
            }
    
    return hierarchy

def create_connectors_after_rearrangement(ibd_, connector_data):
    """Create connectors after diagram rearrangement is complete"""
    if not connector_data:
        return
    
    project = Application.getInstance().getProject()
    sm.getInstance().createSession(project, 'Add Connectors After Rearrangement')
    
    try:
        # Check if connectors already exist in diagram to prevent duplication
        existing_connector_elements = set()
        for pe in ibd_.getPresentationElements():
            if pe.getHumanType() == "Connector":
                existing_connector_elements.add(pe.getElement())
        
        for i, (connector, source_role, target_role) in enumerate(connector_data):
            # Skip if connector presentation already exists in diagram
            if connector in existing_connector_elements:
                continue
                
            source_shape = find_shape_element_for(source_role, ibd_)
            target_shape = find_shape_element_for(target_role, ibd_)

            if source_shape and target_shape:
                try:
                    nc = PresentationElementsManager.getInstance().createPathElement(connector, source_shape, target_shape)
                    
                    tag_prop = get_tag(nc.getElement(), "connectorMetaName")
                    hideCompartmentInfo(nc, tag_prop)
                    
                    # Set connector properties
                    pm_connector = PropertyManager()
                    show_name = nc.getProperty(PropertyID.SHOW_NAME)
                    if show_name:
                        new_show_name = show_name.clone()
                        new_show_name.setValue(True)
                        pm_connector.addProperty(new_show_name)
                    
                    # Hide stereotypes - set to "Do Not Display"
                    show_stereotype = nc.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                    if show_stereotype:
                        new_show_stereotype = show_stereotype.clone()
                        new_show_stereotype.setValue("STEREOTYPE_DISPLAY_MODE_DO_NOT_DISPLAY_STEREOTYPES")
                        pm_connector.addProperty(new_show_stereotype)
                    
                    # Apply properties
                    if pm_connector.getProperties():
                        PresentationElementsManager.getInstance().setPresentationElementProperties(nc, pm_connector)
                        
                except Exception as e:
                    printer("Error creating connector: {}".format(str(e)))
                    
        sm.getInstance().closeSession()
        
    except Exception as e:
        printer("Error in create_connectors_after_rearrangement: {}".format(str(e)))
        sm.getInstance().closeSession()
        
    except Exception as e:
        printer("Error in create_connectors_after_rearrangement: {0}".format(str(e)))
        sm.getInstance().closeSession()

# ====== CABINET-BASED GROUPING FUNCTIONS ======

def get_cached_cabinet_properties(systemCabinetMap):
    """
    Extract all part properties from cached cabinet data in systemCabinetMap.
    Uses two-pass lookup: named systems first, then "Unknown" system.
    Returns tuple: (named_cabinet_properties, unknown_cabinet_properties)
    """
    named_cabinet_properties = {}
    unknown_cabinet_properties = {}
    
    if not systemCabinetMap:
        printer("systemCabinetMap not available, falling back to basic grouping")
        return named_cabinet_properties, unknown_cabinet_properties
    
    try:
        # Import Java utilities for cabinet properties access
        from com.pmw790.functions import Utilities
        
        for system_name, system_cabinets in systemCabinetMap.items():
            for cabinet_name, cabinet_info in system_cabinets.items():
                # Get cached properties for this cabinet
                cached_props = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinet_name)
                
                if cached_props:
                    part_props_in_cabinet = {}
                    for prop_name, prop_obj in cached_props.items():
                        # Filter for only PartProperty stereotypes
                        if prop_obj and isPartProperty(prop_obj):
                            part_props_in_cabinet[prop_name] = {
                                'property': prop_obj,
                                'system': system_name,
                                'cabinet': cabinet_name,
                                'room': cabinet_info.get('room', 'Unknown Room') if cabinet_info.get('room') else 'Unknown Room'
                            }
                    
                    if part_props_in_cabinet:
                        # Separate named systems from "Unknown" system
                        if system_name.lower() == "unknown":
                            unknown_cabinet_properties[cabinet_name] = part_props_in_cabinet
                        else:
                            named_cabinet_properties[cabinet_name] = part_props_in_cabinet

        return named_cabinet_properties, unknown_cabinet_properties
        
    except Exception as e:
        printer("Error accessing cached cabinet properties: {0}".format(str(e)))
        return named_cabinet_properties, unknown_cabinet_properties

def find_element_by_name(element_name, part_props):
    """
    Find a presentation element by its name in the current part properties
    """
    for pe, prop_data in part_props.items():
        if prop_data["name"] == element_name:
            return pe
    return None

def validate_grouping_with_cache(part_props, systemCabinetMap):
    """
    Cross-reference current diagram part properties with cached cabinet data
    to determine accurate cabinet relationships.
    Uses hierarchical host_asset resolution for complete grouping.
    """
    validated_grouping = {}
    named_cabinet_props, unknown_cabinet_props = get_cached_cabinet_properties(systemCabinetMap)
    
    # Combine both named and unknown cabinets for host_asset matching
    all_cabinet_names = set(named_cabinet_props.keys()) | set(unknown_cabinet_props.keys())
    
    # Track elements that need hierarchical resolution
    unresolved_elements = {}
    total_elements = len(part_props)
    
    element_counter = 0
    for pe, prop_data in part_props.items():
        element_counter += 1
        element_name = prop_data["name"]
        current_host_asset = prop_data["host_asset"]
        
        # First pass: Check if host_asset is found directly in "Unknown" system
        found_in_cache = False
        for cabinet_name, cabinet_props in unknown_cabinet_props.items():
            if current_host_asset in cabinet_props:
                cache_info = cabinet_props[current_host_asset]
                validated_grouping[pe] = prop_data.copy()
                validated_grouping[pe].update({
                    "validated_cabinet": cabinet_name,
                    "system": cache_info['system'],
                    "room": cache_info['room'],
                    "source": "cache_unknown"
                })
                found_in_cache = True
                break
        
        # Second pass: Check if host_asset is found directly in named systems
        if not found_in_cache:
            for cabinet_name, cabinet_props in named_cabinet_props.items():
                if current_host_asset in cabinet_props:
                    cache_info = cabinet_props[current_host_asset]
                    validated_grouping[pe] = prop_data.copy()
                    validated_grouping[pe].update({
                        "validated_cabinet": cabinet_name,
                        "system": cache_info['system'],
                        "room": cache_info['room'],
                        "source": "cache"
                    })
                    found_in_cache = True
                    break
        
        # Third pass: Check if current host_asset matches any known cabinet directly
        if not found_in_cache:
            if current_host_asset in all_cabinet_names:
                # Determine if the cabinet is in unknown or named system (prioritize unknown)
                if current_host_asset in unknown_cabinet_props:
                    # Get system info from first property in this cabinet
                    sample_prop = next(iter(unknown_cabinet_props[current_host_asset].values()))
                    validated_grouping[pe] = prop_data.copy()
                    validated_grouping[pe].update({
                        "validated_cabinet": current_host_asset,
                        "system": sample_prop['system'],
                        "room": sample_prop['room'],
                        "source": "host_asset_match_unknown"
                    })
                    found_in_cache = True
                elif current_host_asset in named_cabinet_props:
                    # Get system info from first property in this cabinet
                    sample_prop = next(iter(named_cabinet_props[current_host_asset].values()))
                    validated_grouping[pe] = prop_data.copy()
                    validated_grouping[pe].update({
                        "validated_cabinet": current_host_asset,
                        "system": sample_prop['system'],
                        "room": sample_prop['room'],
                        "source": "host_asset_match"
                    })
                    found_in_cache = True
            else:
                pass
        
        # If still not found, add to unresolved for hierarchical resolution
        if not found_in_cache:
            unresolved_elements[pe] = prop_data
    
    # Handle unresolved elements: group by host_asset
    for pe, prop_data in unresolved_elements.items():
        element_name = prop_data["name"]
        current_host_asset = prop_data["host_asset"]
        
        # Get room information directly from the element's host_location property
        host_location = get_host_location(pe)
        room_name = host_location if host_location != "None" else "Unknown Room"
        
        # Use host_asset as the grouping cabinet
        validated_grouping[pe] = prop_data.copy()
        validated_grouping[pe].update({
            "validated_cabinet": current_host_asset,
            "system": None,
            "room": room_name,
            "source": "host_asset_fallback"
        })
    
    return validated_grouping

def create_cabinet_hierarchy_grouping(validated_props):
    """
    Create hierarchical grouping: System -> Cabinet -> Elements
    """
    hierarchy = {}
    
    for pe, prop_data in validated_props.items():
        system = prop_data.get("system", "Unknown System")
        cabinet = prop_data.get("validated_cabinet", "Unknown Cabinet")
        room = prop_data.get("room", "Unknown Room")
        
        if system not in hierarchy:
            hierarchy[system] = {}
        
        if cabinet not in hierarchy[system]:
            hierarchy[system][cabinet] = {
                "room": room,
                "elements": []
            }
        
        hierarchy[system][cabinet]["elements"].append(prop_data)
    
    return hierarchy

def redraw_connectors(connector_props, diagram):
    """Restore connectors to the diagram"""
    try:
        pem = PresentationElementsManager.getInstance()
        for conn in connector_props:
            # Re-add the connector presentation element back to the diagram
            diagram.addPresentationElement(conn)
    except Exception as e:
        printer("Warning: Could not restore connectors: {0}".format(str(e)))

def create_rectangular_separator(diagram, label, x, y, width, height):
    """Create visual rectangular separator with styled border and label"""
    try:
        # Get project instance and managers
        project = Application.getInstance().getProject()
        pem = PresentationElementsManager.getInstance()
        
        # Create rectangular shape (same approach as fid_utils.py)
        rec_sep = pem.createRectangularShape(diagram, Point(int(x), int(y)))
        pem.reshapeShapeElement(rec_sep, Rectangle(int(x), int(y), int(width), int(height)))
        rec_sep.setName(str(label))
        
        # Apply styling to the rectangular shape
        shape_pm = PropertyManager()
        
        # Gray border color
        border_color_prop = rec_sep.getProperty(PropertyID.PEN_COLOR)
        if border_color_prop:
            new_border_color = border_color_prop.clone()
            new_border_color.setValue(Color(128, 128, 128))
            shape_pm.addProperty(new_border_color)
        
        # Transparent fill color
        fill_color_prop = rec_sep.getProperty(PropertyID.FILL_COLOR)
        if fill_color_prop:
            new_fill_color = fill_color_prop.clone()
            new_fill_color.setValue(Color(255, 255, 255, 0))  # Transparent
            shape_pm.addProperty(new_fill_color)
        
        # Apply styling
        if shape_pm.getProperties():
            pem.setPresentationElementProperties(rec_sep, shape_pm)

        return rec_sep
        
    except Exception as e:
        printer("Error creating visual separator: {0}".format(str(e)))
    
    return None

def create_cabinet_separator(diagram, cabinet_info, x, y, width, height):
    """
    Create a styled rectangular separator for cabinet groupings with enhanced labeling
    """
    system = cabinet_info.get("system", "Unknown System")
    cabinet = cabinet_info.get("cabinet", "Unknown Cabinet")
    room = cabinet_info.get("room", "Unknown Room")

    system_label = "SYSTEM: {}".format("UNKNOWN" if system in [None, "Unknown System"] else system.upper() if system == "Unknown" else system)
    cabinet_label = "OWNING ASSET: {}".format(cabinet if cabinet != "Unknown Cabinet" else "NONE")
    room_label = "ROOM: {}".format(room if room != "Unknown Room" else "NONE")
    
    label = "{} | {} | {}".format(system_label, cabinet_label, room_label)
    
    create_rectangular_separator(diagram, label, x, y, width, height)

# ====== APPLY ELK LAYOUT FUNCTIONS ======

def collect_complete_structured_data(diagram, original_spec=None):
    """
    Collect complete structured data from diagram after all elements (including connectors) have been created.
    """
    parts_data = []
    ports_data = []
    connector_data = []

    # Build mapping of model elements to presentation elements
    pe_mapping = {}
    for pe in diagram.getPresentationElements():
        if pe.getElement() and pe.isVisible():
            pe_mapping[pe.getElement()] = pe

    # Collect parts with their presentation elements
    for pe in diagram.getPresentationElements():
        if pe.getHumanType() == "Part Property" and pe.isVisible():
            element = pe.getElement()
            if element:
                part_info = {
                    "element": element,
                    "presentation_element": pe,
                    "name": element.getName() if element.getName() else "Unnamed",
                    "human_name": element.getHumanName() if hasattr(element, 'getHumanName') else element.getName(),
                    "level": 1,  # Default level, could be enhanced
                    "ports": []
                }
                parts_data.append(part_info)

    # Collect ports with their presentation elements and parent relationships
    # Simple approach: only look at top-level and one level deep (parts and their ports)
    seen_port_presentations = set()

    # Process top-level presentation elements
    printer("DEBUG: Scanning top-level presentation elements for ports...")
    for pe in diagram.getPresentationElements():
        if not pe.isVisible():
            continue

        element = pe.getElement()
        pe_human_type = pe.getHumanType()

        # Debug: Show all top-level elements
        printer("DEBUG: Top-level PE: {} (type: {}, element: {})".format(
            pe_human_type,
            pe.getClass().getSimpleName(),
            element.getClass().getSimpleName() if element else "None"))

        # Check if this top-level element is a port (including Full Port, IDP Port, etc.)
        is_top_level_port = False
        if element:
            # Check by element type
            element_type = element.getClass().getSimpleName()
            if "Port" in element_type:
                is_top_level_port = True

        # Also check by human type for various port types
        if "Port" in pe_human_type:
            is_top_level_port = True

        if is_top_level_port and element and pe not in seen_port_presentations:
            seen_port_presentations.add(pe)
            port_info = {
                "element": element,
                "presentation_element": pe,
                "name": element.getName() if element.getName() else "Unnamed",
                "human_name": element.getHumanName() if hasattr(element, 'getHumanName') else element.getName(),
                "parent_element": None,
                "parent_presentation_element": None,
                "parent_name": "Top-level",
                "level": 0,
                "is_top_level": True
            }
            ports_data.append(port_info)
            printer("DEBUG: Added top-level port: {} (PE type: {}, Element type: {})".format(
                element.getName() if element.getName() else "Unnamed",
                pe_human_type,
                element.getClass().getSimpleName()))

        # If this is a part, check its child presentation elements for ports (and go deeper)
        elif pe_human_type == "Part Property" and hasattr(pe, 'getPresentationElements'):
            def search_for_ports_in_part(part_pe, part_element, level=1):
                for child_pe in part_pe.getPresentationElements():
                    if not child_pe.isVisible():
                        continue

                    child_element = child_pe.getElement()
                    child_human_type = child_pe.getHumanType()

                    printer("DEBUG: Checking child PE: {} (type: {}, element: {})".format(
                        child_human_type,
                        child_pe.getClass().getSimpleName(),
                        child_element.getClass().getSimpleName() if child_element else "None"))

                    # Check if this child is a port
                    is_child_port = False
                    if child_element:
                        child_element_type = child_element.getClass().getSimpleName()
                        if "Port" in child_element_type:
                            is_child_port = True
                    if "Port" in child_human_type:
                        is_child_port = True

                    if is_child_port and child_element and child_pe not in seen_port_presentations:
                        seen_port_presentations.add(child_pe)
                        port_info = {
                            "element": child_element,
                            "presentation_element": child_pe,
                            "name": child_element.getName() if child_element.getName() else "Unnamed",
                            "human_name": child_element.getHumanName() if hasattr(child_element, 'getHumanName') else child_element.getName(),
                            "parent_element": part_element,
                            "parent_presentation_element": part_pe,
                            "parent_name": part_element.getName() if part_element and part_element.getName() else "Unknown",
                            "level": level,
                            "is_top_level": False
                        }
                        ports_data.append(port_info)
                        printer("DEBUG: Added child port: {} (PE type: {}, Parent: {}, Level: {})".format(
                            child_element.getName() if child_element.getName() else "Unnamed",
                            child_human_type,
                            part_element.getName() if part_element and part_element.getName() else "Unknown",
                            level))

                    # Recursively search deeper if this child has its own children
                    elif hasattr(child_pe, 'getPresentationElements') and child_pe.getPresentationElements():
                        search_for_ports_in_part(child_pe, child_element, level + 1)

            search_for_ports_in_part(pe, element)

        # Also check if this is a diagram frame that might have ports
        elif pe_human_type == "Diagram Frame" and hasattr(pe, 'getPresentationElements'):
            printer("DEBUG: Checking diagram frame for ports...")
            for frame_child_pe in pe.getPresentationElements():
                if not frame_child_pe.isVisible():
                    continue

                frame_child_element = frame_child_pe.getElement()
                frame_child_human_type = frame_child_pe.getHumanType()

                printer("DEBUG: Frame child PE: {} (type: {}, element: {})".format(
                    frame_child_human_type,
                    frame_child_pe.getClass().getSimpleName(),
                    frame_child_element.getClass().getSimpleName() if frame_child_element else "None"))

                # Check if this frame child is a port
                is_frame_port = False
                if frame_child_element:
                    frame_element_type = frame_child_element.getClass().getSimpleName()
                    if "Port" in frame_element_type:
                        is_frame_port = True
                if "Port" in frame_child_human_type:
                    is_frame_port = True

                if is_frame_port and frame_child_element and frame_child_pe not in seen_port_presentations:
                    seen_port_presentations.add(frame_child_pe)
                    port_info = {
                        "element": frame_child_element,
                        "presentation_element": frame_child_pe,
                        "name": frame_child_element.getName() if frame_child_element.getName() else "Unnamed",
                        "human_name": frame_child_element.getHumanName() if hasattr(frame_child_element, 'getHumanName') else frame_child_element.getName(),
                        "parent_element": None,
                        "parent_presentation_element": pe,
                        "parent_name": "Diagram Frame",
                        "level": 0,
                        "is_top_level": True
                    }
                    ports_data.append(port_info)
                    printer("DEBUG: Added diagram frame port: {} (PE type: {})".format(
                        frame_child_element.getName() if frame_child_element.getName() else "Unnamed",
                        frame_child_human_type))

    printer("DEBUG: Collected {} unique ports total".format(len(ports_data)))

    # Collect connectors with their presentation elements - NOW CONSISTENT WITH PARTS/PORTS!
    for pe in diagram.getPresentationElements():
        if pe.getHumanType() == "Connector" and pe.isVisible():
            element = pe.getElement()
            if element and hasattr(element, 'getEnd') and len(element.getEnd()) >= 2:
                ends = element.getEnd()
                source_role = ends[0].getRole() if len(ends) > 0 else None
                target_role = ends[1].getRole() if len(ends) > 1 else None

                connector_info = {
                    "element": element,
                    "presentation_element": pe,  # NOW INCLUDED!
                    "name": element.getName() if element.getName() else "Unnamed",
                    "human_name": element.getHumanName() if hasattr(element, 'getHumanName') else element.getName(),
                    "source_role": source_role,
                    "target_role": target_role,
                    "connector_tuple": (element, source_role, target_role)  # Preserve original format for compatibility
                }
                connector_data.append(connector_info)

    # Build complete spec preserving original data where available
    complete_spec = {
        "parts_data": parts_data,
        "ports_data": ports_data,
        "connector_data": connector_data,
        "total_parts": len(parts_data),
        "total_ports": len(ports_data),
        "total_connectors": len(connector_data),
        "diagram": diagram,
        "presentation_element_mapping": pe_mapping
    }

    # Preserve original spec data if available, but override connector_data with new format
    if original_spec:
        for key, value in original_spec.items():
            if key not in complete_spec:
                complete_spec[key] = value

    # IMPORTANT: Override the old connector_data (tuple format) with new connector_data (Map format)
    # This ensures Java receives the new format with presentation elements
    printer("DEBUG: Overriding connector_data - old format replaced with new Map format")

    # Debug: Show format of connectors to confirm they're in Map format
    printer("Collected complete structured data: {} parts, {} ports, {} connectors (all with presentation elements)".format(
        len(parts_data), len(ports_data), len(connector_data)))

    if connector_data:
        first_connector = connector_data[0]
        printer("DEBUG: First connector format: {}".format(type(first_connector).__name__))
        if isinstance(first_connector, dict):
            printer("DEBUG: First connector keys: {}".format(list(first_connector.keys())))
        else:
            printer("DEBUG: First connector is not a dict: {}".format(first_connector))

    return complete_spec

def apply_elk_layout_after_grouping(selected_ibd, cabinet_hierarchy=None, spec=None):
    """Apply ELK layout for improved FID presentation after cabinet grouping is complete"""
    elk_layout_applied = False

    try:
        from com.pmw790.jython import jythonFunctions
        jf = jythonFunctions(None, None)  # Minimal instance for layout access

        if jf.isELKAvailable():
            if cabinet_hierarchy:
                complete_spec = collect_complete_structured_data(selected_ibd, spec)
                # Pass complete structured data to Java
                jf.applyELKFIDLayoutOptimized(selected_ibd, cabinet_hierarchy, complete_spec)
                elk_layout_applied = True
                printer("ELK layout with cabinet grouping and complete structured data applied successfully")
                printer("{}".format("=*="*35))

        else:
            printer("ELK layout not available - using standard MagicDraw layout after cabinet grouping")
    except Exception as elk_ex:
        printer("ERROR: ELK layout failed after cabinet grouping: " + str(elk_ex) + " - falling back to standard layout")
        import traceback
        printer("ERROR: ELK traceback: {}".format(traceback.format_exc()))

    # Fallback to standard layout if ELK not applied
    if not elk_layout_applied:
        try:
            project = Application.getInstance().getProject()
            project.getDiagram(selected_ibd.getDiagram()).layout(True)

            try:
                layouter = OrderedHierarchicDiagramLayouter()
                options = HierarchicLayouterOptionsGroup()
                options.setOrientation("LEFT_TO_RIGHT")
                project.getDiagram(selected_ibd.getDiagram()).layout(True, layouter, options)
            except Exception as ex:
                printer("WARNING: Could not apply OrderedHierarchicDiagramLayouter with left-to-right orientation after grouping: " + str(ex))
                try:
                    project.getDiagram(selected_ibd.getDiagram()).layout(True, OrderedHierarchicDiagramLayouter())
                except Exception as ex2:
                    printer("WARNING: Could not apply default OrderedHierarchicDiagramLayouter after grouping: " + str(ex2))
        except Exception as layout_ex:
            printer("WARNING: Could not apply any layout after cabinet grouping: " + str(layout_ex))
    
    # # Draw cabinet grouping boxes if hierarchy data was provided
    # if elk_layout_applied and cabinet_hierarchy:
    #     draw_cabinet_boxes_after_elk(selected_ibd, cabinet_hierarchy)

# def draw_cabinet_boxes_after_elk(selected_ibd, cabinet_hierarchy):
#     """
#     Draw dashed cabinet grouping boxes around elements after ELK layout is complete.
#     Analyzes final element positions to create properly-sized grouping rectangles.
#     """
#     if not cabinet_hierarchy:
#         printer("No cabinet hierarchy provided for box drawing")
#         return
#
#     printer("Drawing cabinet grouping boxes after ELK layout...")
#
#     # Create session for cabinet box drawing
#     project = Application.getInstance().getProject()
#     sm.getInstance().createSession(project, 'Draw Cabinet Grouping Boxes After ELK')
#
#     try:
#         # Build element position mapping from current diagram state
#         element_positions = {}
#         for pe in selected_ibd.getPresentationElements():
#             if pe.getHumanType() == "Part Property" and pe.isVisible():
#                 element_positions[pe] = {
#                     "bounds": pe.getBounds(),
#                     "x": pe.getBounds().getX(),
#                     "y": pe.getBounds().getY(),
#                     "width": pe.getBounds().getWidth(),
#                     "height": pe.getBounds().getHeight(),
#                     "name": pe.getElement().getName() if pe.getElement() else "Unknown"
#                 }
#
#         # Create cabinet grouping boxes for each system/cabinet combination
#         box_padding = 30  # Padding around grouped elements
#
#         for system_name, system_cabinets in cabinet_hierarchy.items():
#             for cabinet_name, cabinet_data in system_cabinets.items():
#                 elements = cabinet_data.get("elements", [])
#
#                 if len(elements) < 2:  # Skip single elements - no grouping box needed
#                     continue
#
#                 # Find all presentation elements for this cabinet group
#                 group_pes = []
#                 for element_data in elements:
#                     pe = element_data.get("pe") or element_data.get("presentation_element")
#                     if pe and pe in element_positions:
#                         group_pes.append(pe)
#
#                 if len(group_pes) < 2:  # Need at least 2 elements to draw grouping box
#                     continue
#
#                 # Calculate bounding box for all elements in this cabinet group
#                 min_x = min(element_positions[pe]["x"] for pe in group_pes)
#                 min_y = min(element_positions[pe]["y"] for pe in group_pes)
#                 max_x = max(element_positions[pe]["x"] + element_positions[pe]["width"] for pe in group_pes)
#                 max_y = max(element_positions[pe]["y"] + element_positions[pe]["height"] for pe in group_pes)
#
#                 # Add padding around the group
#                 group_x = min_x - box_padding
#                 group_y = min_y - box_padding
#                 group_width = (max_x - min_x) + (2 * box_padding)
#                 group_height = (max_y - min_y) + (2 * box_padding)
#
#                 # Create cabinet information for labeling
#                 cabinet_info = {
#                     "system": system_name,
#                     "cabinet": cabinet_name,
#                     "room": cabinet_data.get("room", "Unknown Room")
#                 }
#
#                 # Create the dashed grouping rectangle
#                 create_dashed_cabinet_separator(selected_ibd, cabinet_info, group_x, group_y, group_width, group_height)
#
#                 printer("Created cabinet box for {}/{} with {} elements".format(
#                     system_name, cabinet_name, len(group_pes)))
#
#         printer("Finished drawing cabinet grouping boxes")
#         sm.getInstance().closeSession()
#
#     except Exception as e:
#         printer("ERROR: Exception in draw_cabinet_boxes_after_elk: {}".format(str(e)))
#         import traceback
#         printer("ERROR: Cabinet box drawing traceback: {}".format(traceback.format_exc()))
#         sm.getInstance().closeSession()
#
# def create_dashed_cabinet_separator(diagram, cabinet_info, x, y, width, height):
#     """
#     Create a dashed rectangular separator for cabinet groupings with enhanced labeling
#     """
#     system = cabinet_info.get("system", "Unknown System")
#     cabinet = cabinet_info.get("cabinet", "Unknown Cabinet")
#     room = cabinet_info.get("room", "Unknown Room")
#
#     system_label = "SYSTEM: {}".format("UNKNOWN" if system in [None, "Unknown System"] else system.upper() if system == "Unknown" else system)
#     cabinet_label = "OWNING ASSET: {}".format(cabinet if cabinet != "Unknown Cabinet" else "NONE")
#     room_label = "ROOM: {}".format(room if room != "Unknown Room" else "NONE")
#
#     label = "{} | {} | {}".format(system_label, cabinet_label, room_label)
#
#     try:
#         # Get project instance and managers
#         project = Application.getInstance().getProject()
#         pem = PresentationElementsManager.getInstance()
#
#         # Create rectangular shape
#         rec_sep = pem.createRectangularShape(diagram, Point(int(x), int(y)))
#         pem.reshapeShapeElement(rec_sep, Rectangle(int(x), int(y), int(width), int(height)))
#         rec_sep.setName(str(label))
#
#         # Apply dashed styling to the rectangular shape
#         shape_pm = PropertyManager()
#
#         # Dashed gray border color
#         border_color_prop = rec_sep.getProperty(PropertyID.PEN_COLOR)
#         if border_color_prop:
#             new_border_color = border_color_prop.clone()
#             new_border_color.setValue(Color(128, 128, 128))
#             shape_pm.addProperty(new_border_color)
#
#         # Dashed line style
#         line_style_prop = rec_sep.getProperty(PropertyID.PEN_STYLE)
#         if line_style_prop:
#             new_line_style = line_style_prop.clone()
#             new_line_style.setValue("DASHED")  # Set to dashed line style
#             shape_pm.addProperty(new_line_style)
#
#         # Transparent fill color
#         fill_color_prop = rec_sep.getProperty(PropertyID.FILL_COLOR)
#         if fill_color_prop:
#             new_fill_color = fill_color_prop.clone()
#             new_fill_color.setValue(Color(255, 255, 255, 0))  # Transparent
#             shape_pm.addProperty(new_fill_color)
#
#         # Apply styling
#         if shape_pm.getProperties():
#             pem.setPresentationElementProperties(rec_sep, shape_pm)
#
#         return rec_sep
#
#     except Exception as e:
#         printer("Error creating dashed cabinet separator: {}".format(str(e)))
#
#     return None