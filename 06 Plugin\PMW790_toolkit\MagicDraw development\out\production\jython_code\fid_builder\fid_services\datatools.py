def combine_el_dicts(dicts):
    combined_dict = {}
    for sub_dict in dicts:
        element_dict = sub_dict['connection_info'].get(('element_dict'),{})
        combined_dict.update(element_dict)

    return combined_dict

def combine_part_dicts(dicts):
    combined_dict = {}
    for sub_dict in dicts:
        part_dict = sub_dict['connection_info'].get(('part_dict'),{})
        combined_dict.update(part_dict)

    return combined_dict

def sort_by_host(part_props):
    """Sort part properties by their host asset."""
    sorted_props = {}
    for prop in part_props:
        host_asset = part_props[prop]["host_asset"]
        if host_asset not in sorted_props:
            sorted_props[host_asset] = []
        sorted_props[host_asset].append(part_props[prop])
    return sorted_props