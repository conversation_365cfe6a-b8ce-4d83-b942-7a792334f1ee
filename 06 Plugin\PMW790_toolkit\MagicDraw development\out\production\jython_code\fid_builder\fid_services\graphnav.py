def traverse_connections(connections, connector_end_dict, classifiers_list, nc_list):
    adjacency = {}

    # Build undirected graph
    for node1, node2 in connections:
        adjacency.setdefault(node1, set()).add(node2)
        adjacency.setdefault(node2, set()).add(node1)

    results = []
    cables = {}
    seen_starts = set()
    visited_edges = set()

    for node in adjacency:
        # Only start traversal if node is not in classifiers list and not visited yet
        if node in seen_starts:
            continue
        if any(classifier in connector_end_dict[node]['classifiers'] for classifier in classifiers_list) or any(nc.lower() in connector_end_dict[node]['full_name'].lower() for nc in nc_list):
            continue

        ends, cable_map = find_all_ends(node, adjacency, connector_end_dict, classifiers_list, visited_edges, nc_list)
        results.extend(ends)
        cables.update(cable_map)
        seen_starts.add(node)

    if not results and not cables:
        return None

    return results, cables


def find_all_ends(start, adjacency, connector_end_dict, classifiers_list, visited_edges, nc_list):
    stack = [(start, start, set([start]), None)]  # (origin, current, visited_path, cable)
    results = []
    cable_map = {}

    while stack:
        origin, current, visited, cable_node = stack.pop()

        for neighbor in adjacency.get(current, []):
            if neighbor in visited:
                continue
            visited_new = visited | {neighbor}

            edge_key = tuple(sorted((origin, neighbor)))
            if edge_key in visited_edges:
                continue

            visited_edges.add(edge_key)

            if "Cable" in connector_end_dict[neighbor]['classifiers']:
                stack.append((origin, neighbor, visited_new, neighbor))
            elif any(classifier in connector_end_dict[neighbor]['classifiers'] for classifier in classifiers_list) or any(nc.lower() in connector_end_dict[neighbor]['full_name'].lower() for nc in nc_list):
                stack.append((origin, neighbor, visited_new, cable_node))
            else:
                if connector_end_dict[origin]['sys_owner'] and connector_end_dict[neighbor]['sys_owner']:
                    if connector_end_dict[origin]['sys_owner'] == connector_end_dict[neighbor]['sys_owner']:
                        continue

                key = (origin, neighbor)
                results.append(key)
                cable_map[key] = cable_node
                #results.append((origin, neighbor, cable_node))

    return results, cable_map