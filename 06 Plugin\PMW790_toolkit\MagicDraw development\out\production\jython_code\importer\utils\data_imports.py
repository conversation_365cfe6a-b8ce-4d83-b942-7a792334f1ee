from core.imports import *
from utils.md_utils import printer, flatten_dict
import re

def import_json(file_path):
    """
    Function to read and parse JSON data into IDP format
    
    Args:
        file_path (str): location of json file

    Returns:
        tuple: (locations, materials, assets, parts, connections, system)
    """
    with open(file_path, 'r') as f:
        data = json.load(f)

    # Initialize empty dictionaries for each entity
    assets = {}
    assets_att = []
    parts = {}
    materials = {}
    locations = {}
    connections = {}
    
    # Get system info
    system_name = data['system'].get('name')
    system_id = data['system'].get('id')
    system_install_date = data['installed_on']
    
    # Create system dictionary with inherited properties and default values
    system = {system_name: {
        'installed_on': system_install_date,
        'systemVersionNomenclature': system_id,
        # Set default values for inherited properties
        'nomenclature': '',
        'longname': data.get('name'),
        'shortName': system_name,
        'programOwnership': '',
        'isVirtual': False,
        'capabilityDescription': ''
    }}

    # Iterate over each asset entry in the original data
    for asset_entry in data['assets']:
        # Add the asset to the assets dictionary
        asset = asset_entry['asset']
        part = asset.pop('part', None)

        if part:
            material = part.pop('material', None)
        else:
            material = asset.pop('material', None)

        if material:
            material_type = material.get('material_type', {}).get('name')

            if material_type in ["Connector", "Transceiver"]:
                asset['name'] = standardize_connector_name(
                    asset['name'],
                    asset['id'],
                    is_transceiver=(material_type == "Transceiver")
                )
            elif material_type == "Cable Component":
                asset['name'] = standardize_connector_name(
                    asset['name'],
                    asset['id'],
                    is_component=True
                )
            elif material_type == "Cable":
                asset['name'] = standardize_cable_name(asset['name'], asset['id'])
            else:
                asset['name'] = "{0}_{1}".format(str(asset['name']), str(asset['id']).split('-')[0])

        if asset['location']:
            location = asset['location']
            location_flat = flatten_dict(location)

            # Define the hierarchy and key mappings
            level_keys = {
                'site': 'room__floor__building__site',
                'building': 'room__floor__building',
                'floor': 'room__floor',
                'room': 'room'
            }
            hierarchy = ['site', 'building', 'floor', 'room']

            for level, prefix in level_keys.items():
                level_id = location_flat.get(prefix + '__id')
                if level_id:
                    name = location_flat.get(prefix + '__name', '')
                    parent_level = hierarchy[hierarchy.index(level) - 1] if level != 'site' else None
                    parent_id = location_flat.get(level_keys.get(parent_level, '') + '__id') if parent_level else None
                    
                    if level == 'room':
                        number = location_flat.get(prefix + '__number', '')
                        name = name if (not number or number == name) else "{0}_{1}".format(name, number)
                    
                    if level == 'floor':
                        name = "{0}_{1}".format(name, location_flat.get(level_keys.get(parent_level, '') + '__name'))

                    locations[level_id] = {
                        'type': level,
                        'name': name,
                        'parent': {'type': parent_level, 'id': parent_id} if parent_id else {}
                    }

        # Add the id of the part and material to the asset for traceability
        if part:
            #Creating Name for Part that can be used for the part block
            part['name'] = 'Part_Find_No_' + str(part['find_no']) + ' ' + 'Material_' + str(material['sbom'] if material else '')
            #Traceability for Asset to Part using the "unique" part name
            asset['part_name'] = part['name']

        if material:
            # asset['material_id'] = material['id']
            asset['material_sbom'] = material['sbom']

        # Add data to respective dictionary
        if asset:
            flat_asset = flatten_dict(asset)
            assets[asset['id']] = flat_asset
            assets[asset['id']]['asset_status'] = asset_entry['asset_status']['name']
        if part:
            parts[part['name']] = part
        if material:
            materials[material['sbom']] = flatten_dict(material)

    for connection_entry in data['connections']:
        connection = connection_entry['connection']

        for connector_type in ['source_connectors', 'target_connectors']:
            if connection.get(connector_type):
                for connector in connection[connector_type]:
                    material_type = connector.get('material_type', {}).get('name')
                    is_transceiver = material_type == "Transceiver"
                    is_component = material_type == "Cable Component"
                    connector['name'] = standardize_connector_name(
                        connector['name'],
                        connector['id'],
                        is_transceiver=is_transceiver,
                        is_component=is_component
                    )

        source_name = "{0}_{1}".format(connection['source']['name'],
                                       connection['source']['id'].split('-')[0])
        target_name = "{0}_{1}".format(connection['target']['name'],
                                   connection['target']['id'].split('-')[0])

        # Extract port information and add it to materials import dictionary
        for port_type in ['source_port', 'target_port']:

            port_info = connection[port_type]

            #removing the material information from the port data
            material_info = port_info['port_group'].pop('material', None)
            port_sbom = material_info['sbom']
            port_info['name'] = str(port_info['name'])
            # Check if the sbom exists in the materials
            if port_sbom in materials:
                # If the 'Ports' key doesn't exist in the asset dictionary for the sbom, create it
                if 'Ports' not in materials[port_sbom]:
                    materials[port_sbom]['ports'] = {}

                # Append the port information to the 'Ports' dictionary if it doesn't exist
                if port_info['name'] not in materials[port_sbom]['ports']:
                    materials[port_sbom]['ports'][port_info['name']] = flatten_dict(port_info)

            connection_key = "{0}_{1}_{2}_{3}".format(
                source_name,
                connection['source_port']['name'],
                target_name,
                connection['target_port']['name']
            )

        connections[connection_key] = connection

    return locations, materials, assets, parts, connections, system

# Standardizes connector/transceiver names by shortening them
def standardize_connector_name(asset_name, asset_id, is_transceiver=False, is_component=False):
    direction = ""
    asset_name_lower = asset_name.lower()
    if re.search(r'\bsource\b', asset_name_lower):
        direction = "Source"
    elif re.search(r'\btarget\b', asset_name_lower):
        direction = "Target"

    if direction:
        uuid_prefix = asset_name.split("-")[0].upper()
        uuid_postfix = str(asset_id).split('-')[0]
        if is_component:
            type_str = "Cable Component"
        else:
            type_str = "Transceiver" if is_transceiver else "Connector"
        return "{0}-{1}-{2}_{3}".format(uuid_prefix, type_str, direction, uuid_postfix)
    else:
        return "{0}_{1}".format(asset_name, str(asset_id))

# Standardizes cable names by shortening them
def standardize_cable_name(asset_name, asset_id):
        uuid_postfix = str(asset_id).split('-')[0]
        return "{0}-Cable_{1}".format(asset_name, uuid_postfix)