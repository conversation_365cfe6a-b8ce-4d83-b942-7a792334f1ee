import sys
import os
import time
import traceback

# Setup paths dynamically for MagicDraw
def setup_paths(base_dir):
    utils_dir = os.path.join(base_dir, 'utils')
    services_dir = os.path.join(base_dir, 'services')
    models_dir = os.path.join(base_dir, 'models')
    core_dir = os.path.join(base_dir, 'core')

    # Add directories to sys.path if not already present
    for directory in [base_dir, utils_dir, services_dir, models_dir, core_dir]:
        if directory not in sys.path:
            sys.path.insert(0, directory)
    return utils_dir, services_dir, models_dir, core_dir

# Call setup_paths and get all directories
utils_dir, services_dir, models_dir, core_dir = setup_paths(base_dir)

from core.imports import *
from models.asset import AssetService
from models.connection_new import ConnectionService
from models.connection_old import OldConnectionService
from models.location import LocationService
from models.part import PartService
from models.system import SystemService
from services.creator import CreatorService
from services.package import PackageService
from utils.data_imports import import_json
from utils.dictionary import ModelDictionary
from utils.md_utils import *


class ProgressHelper:
    """Helper class for managing progress tracking during import operations"""
    
    def __init__(self):
        # Try to get the progress callback from the Java side
        try:
            self.callback = globals().get('progressCallback', None)
        except:
            self.callback = None
        self.current_progress = 0
        
    def is_cancelled(self):
        """Check if the import operation has been cancelled"""
        try:
            return globals().get('cancelled', False)
        except:
            return False
    
    def update_progress(self, percentage, message):
        """Update progress with percentage and message"""
        self.current_progress = percentage
        if self.callback:
            try:
                self.callback.updateProgress(percentage, message)
            except:
                pass
    
    def increment_progress(self, increment, message):
        """Increment current progress by specified amount"""
        self.current_progress = min(100, self.current_progress + increment)
        self.update_progress(self.current_progress, message)
    
    def calculate_system_progress(self, current_system, total_systems, base_progress, progress_range):
        """Calculate progress for processing individual systems"""
        if total_systems == 0:
            return base_progress
        system_progress = (current_system / float(total_systems)) * progress_range
        return int(base_progress + system_progress)


class SessionManager:
    """
    Automatically closes any existing session and creates a new one with the given name.
    """
    def __init__(self, session_name, auto_save=False):
        self.session_name = session_name
        self.project = Application.getInstance().getProject()
        self.auto_save = auto_save

    def __enter__(self):
        # Close any existing sessions
        try:
            sm.getInstance().closeSession()
        except:
            pass
        # Create new session
        sm.getInstance().createSession(self.project, self.session_name)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            # Auto-save if requested
            if self.auto_save:
                self._save_project()
            
            # Only close session if one actually exists
            if sm.getInstance().isSessionCreated():
                sm.getInstance().closeSession()

        except Exception as e:
            printer("SessionManager: Error closing session '{0}': {1}".format(self.session_name, str(e)))
        return False

    def _save_project(self):
        """Perform strategic save operation within current session context"""
        try:
            from com.nomagic.magicdraw.core import Application
            from com.nomagic.magicdraw.core.project import ProjectsManager
            from com.nomagic.magicdraw.core.project import ProjectDescriptorsFactory
            import time
            
            app = Application.getInstance()
            project = app.getProject()
            
            if project and project.isDirty():
                # Wait for any existing save to complete
                self._wait_for_save_completion(project)
                
                projectsManager = app.getProjectsManager()
                projectDescriptor = ProjectDescriptorsFactory.getDescriptorForProject(project)
                projectsManager.saveProject(projectDescriptor, True)
                
                # Wait for this save to complete before returning
                self._wait_for_save_completion(project)
                
        except Exception as e:
            printer("Save error: {0}".format(str(e)))
    
    def _wait_for_save_completion(self, project, max_wait_seconds=30):
        """Wait for project save operations to complete"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < max_wait_seconds:
            try:
                # Check if project is in a saving state
                if hasattr(project, 'isSaving') and project.isSaving():
                    time.sleep(0.1)  # Wait 100ms
                    continue
                    
                # Also check if project is dirty (unsaved changes)
                if not project.isDirty():
                    break  # Save completed
                    
                time.sleep(0.1)
            except:
                break
                
        # Small additional delay for event processing
        time.sleep(0.2)

class MDApplication:
    def __init__(self, json_folder_path):
        self.project = Application.getInstance().getProject()
        self.project_models = self.project.getModels()
        self.base_model = self.project.getPrimaryModel()
        self.json_folder_path = json_folder_path
        
        # Initialize progress tracking
        self.progress = ProgressHelper()

        # Initialize dictionaries
        self.model_dict = ModelDictionary(self.project)
        self.jsons = {}
        self.materials_dict = {}
        self.taxonomy_dict = {}
        self.systems_dict = {}
        self.locations_dict = {}
        # self.parts_dict = {}
        self.assets_dict = {}
        self.consolidated_new_asset_set = set()
        self.systems_data = {}

        self.system_service = None
        self.location_service = None
        # self.part_service = None
        self.asset_service = None
        self.old_connection_service = None
        self.new_connection_service = None
        self.creator = None
        self.package_service = None

    #___________________DEFINING PACKAGES_________________#
    def initialize_project(self):
        """Initialize project structure and resources"""
        self.progress.update_progress(0, "Initializing project structure...")
        
        with SessionManager("Project Initialization"):
            # Find project usage packages and populate dictionary
            self.progress.update_progress(2, "Finding project resources...")
            self.package_service = PackageService(self.project)
            self.idp_resources, self.material_package, self.bcl_package, self.taxonomy_package = self.package_service.find_resources(self.project_models)
            
            self.progress.update_progress(5, "Building taxonomy dictionary...")
            self.taxonomy_dict = self.model_dict.build_taxonomy_dictionary(self.taxonomy_package, self.bcl_package)
            
            self.progress.update_progress(8, "Building materials dictionary...")
            self.materials_dict = self.model_dict.build_material_dictionary(self.material_package)

            # Initialize service classes
            self.progress.update_progress(10, "Initializing service classes...")
            self.creator = CreatorService(self.project, self.taxonomy_dict, self.taxonomy_package, self.package_service, self.materials_dict, self.bcl_package)

            # Create main VIEWS packages (Systems, Assets, Location, Part, Connection)
            self.progress.update_progress(12, "Creating main VIEW packages...")
            (self.asset_package, self.location_package, self.connection_package,self.system_package) =  self.package_service.find_or_build_idp_packages(self.base_model)

            # Build initial dictionaries
            self.progress.update_progress(15, "Building initial model dictionaries...")
            self.systems_dict = self.model_dict.build_systems_dictionary(self.system_package)
            self.locations_dict = self.model_dict.build_location_dictionary(self.location_package)
            # self.parts_dict = self.model_dict.build_parts_dictionary(self.part_package)
            self.assets_dict = self.model_dict.build_assets_dictionary(self.asset_package)

        # Initialize service classes
        self.progress.update_progress(18, "Initializing view service objects...")
        self.system_service = SystemService(self.project, self.package_service, self.creator, self.systems_dict)
        self.location_service = LocationService(self.project, self.package_service, self.creator, self.locations_dict)
        # self.part_service = PartService(self.project, self.package_service, self.creator, self.parts_dict)
        self.asset_service = AssetService(self.project, self.package_service, self.creator, self.assets_dict, self.materials_dict,self.idp_resources)
        self.old_connection_service = OldConnectionService(self.project, self.package_service, self.creator, self.assets_dict)
        self.new_connection_service = ConnectionService(self.project, self.package_service, self.creator, self.assets_dict, self.systems_dict, self.locations_dict, asset_service=self.asset_service)
            
        self.progress.update_progress(20, "Project initialization completed")

    #___________________IMPORT BUSINESS_________________#
    def load_json_files(self):
        """Load and pre-process JSON files"""
        self.progress.update_progress(20, "Scanning JSON files in folder...")
        self.jsons = load_json_from_folder(self.json_folder_path)

        total_files = len(self.jsons)
        self.progress.update_progress(22, "Found {0} JSON file(s) to process".format(total_files))
        
        current_file = 0
        for system_name, file_info in self.jsons.items():
            current_file += 1
            # Calculate progress for JSON loading (20-40% range)
            file_progress = self.progress.calculate_system_progress(current_file, total_files, 22, 18)
            self.progress.update_progress(file_progress, "<html>Loading JSON file:<br>{0}</html>".format(system_name))
            
            try:
                # Import and process JSON
                locations, materials, assets,_, connections, system = import_json(file_info['file_path'])
                self.systems_data[system_name] = {
                    'locations': locations,
                    'materials': materials,
                    'assets': assets,
                    # 'parts': parts,
                    'connections': connections,
                    'system': system
                }

            except Exception as e:
                printer("Error loading {0}: {1}".format(system_name, str(e)))
                continue
                
        self.progress.update_progress(40, "JSON files loaded successfully")

    def process_system_import(self, system_name, system_data, current_system_index, total_systems):
        """
        Process single system JSON import file

        Args:
            system_name (str): Name of the JSON import
            system_data (dict): Complete system import data including locations, materials etc.
            current_system_index (int): Current system being processed (0-based)
            total_systems (int): Total number of systems to process
        """
        # Calculate base progress for this system (40-80% range for all systems)
        system_base_progress = self.progress.calculate_system_progress(
            current_system_index, total_systems, 40, 40
        )
        
        if self.progress.is_cancelled():
            self.progress.update_progress(self.progress.current_progress, "Import cancelled by user")
            return
        
        self.progress.update_progress(system_base_progress, 
                                    "Processing system: {0}".format(system_name))
        
        with SessionManager("Process System Import", auto_save=False):
            try:
                # Get system info from import
                system_info = system_data['system']
                system_name = system_info.keys()[0]

                # Create system block (5% of system progress)
                sub_progress = system_base_progress + 1
                self.progress.update_progress(sub_progress, 
                                            "Creating system block: {0}".format(system_name))
                
                system_block = self.system_service.create_system_block(
                    system_name,
                    system_info
                )
                if system_block:
                    # Add new system to dictionary
                    name, entry = self.model_dict.update_system_dictionary_entry(system_block)
                    if name:
                        self.systems_dict[name] = entry

                # Process locations (30% of system progress)
                sub_progress = system_base_progress + 2
                self.progress.update_progress(sub_progress, 
                                            "Processing locations for: {0}".format(system_name))
                self.process_locations(system_data['locations'], system_block)
                self.asset_service.set_locations_dict(self.locations_dict)
                
                # Process assets (50% of system progress)
                sub_progress = system_base_progress + 3
                self.progress.update_progress(sub_progress, 
                                            "Processing assets for: {0}".format(system_name))
                self.process_assets(system_data['assets'], system_name, current_system_index + 1, total_systems)
                
                # Process connections (15% of system progress)
                sub_progress = system_base_progress + 4
                self.progress.update_progress(sub_progress, 
                                            "Processing connections for: {0}".format(system_name))
                self.process_old_connections(system_data['connections'], system_name)

            except Exception as e:
                printer("Error processing system import {0}: {1}".format(system_name, str(e)))

    def process_locations(self, locations_data, system_block):
        processed_block = []
        try:
            # First pass - create/update all blocks
            for location_id, location_data in locations_data.items():
                if self.progress.is_cancelled():
                    self.progress.update_progress(self.progress.current_progress, "Import cancelled by user")
                    return
                    
                location_block = self.location_service.create_location_block(location_id, location_data)
                processed_block.append(location_block)

                # Update dictionary for standalone blocks (like site)
                if not location_data.get('parent', {}).get('id'):
                    location_id, entry = self.model_dict.update_location_dictionary_entry(location_block, None, None)
                    if location_id:
                        self.locations_dict[location_id] = entry

            # Second pass - process relationships and update those entries
            created_relationships = self.location_service.process_relationships()
            for relationship, parent_name, child_name in created_relationships:
                location_id, entry = self.model_dict.update_location_dictionary_entry(child_name, parent_name, relationship)
                if location_id:
                    self.locations_dict[location_id] = entry

            # Create system relationship with site if needed
            if self.location_service.site_block and system_block:
                existing_relationship = False
                for rel in self.location_service.site_block.get_relationshipOfRelatedElement():
                    if (rel.getHumanType() == "Association" and
                            any(end.getType() == system_block for end in rel.getMemberEnd())):
                        existing_relationship = True
                        break

                if not existing_relationship:
                    self.creator.createCompositeRelationship(
                        self.location_service.site_block,
                        system_block,
                        system_block.getName(),
                        self.location_package
                    )

        except Exception as e:
            printer("Error processing locations: {0}".format(str(e)))

    # def process_parts(self, parts_data, system_name):
    #     """Process parts for a system"""
    #     try:
    #         # Find matching system data for the system_file we're processing
    #         matching_system_key = None
    #         for sys_key, sys_data in self.systems_data.items():
    #             system_info = sys_data['system']
    #             if system_name in system_info:
    #                 matching_system_key = sys_key
    #                 break
    #
    #         if not matching_system_key:
    #             printer("No matching system data found for {0}".format(system_name))
    #             return
    #
    #         assets_data = self.systems_data[matching_system_key]['assets']
    #
    #         # First create packages and update dictionary
    #         system_pkg, unknown_pkg = self.part_service.create_part_system_packages(system_name)
    #         if system_pkg:
    #             name, entry = self.model_dict.update_parts_dictionary_entry(system_pkg)
    #             if name:
    #                 self.parts_dict[name] = entry
    #         if unknown_pkg:
    #             name, entry = self.model_dict.update_parts_dictionary_entry(unknown_pkg)
    #             if name:
    #                 self.parts_dict[name] = entry
    #
    #         ## Then process parts...
    #         for asset_id, asset_data in assets_data.items():
    #             if not asset_data.get('part_name') or not asset_data.get('material_sbom'):
    #                 continue
    #
    #             material_sbom = asset_data['material_sbom']
    #             asset_status = asset_data.get('asset_status')
    #             base_part_name = asset_data['part_name']
    #
    #             # Add system name prefix for system packages
    #             is_new_asset = asset_status in ["new", "moving_to", "moving_from", "remove"]
    #             part_name = system_name + "_" + base_part_name if is_new_asset else base_part_name
    #
    #             # Determine target package based on asset status
    #             target_package = self.part_service.get_system_parts_package(system_name) if is_new_asset \
    #                 else self.part_service.get_unknown_package()
    #
    #             if not target_package:
    #                 system_pkg, unknown_pkg = self.part_service.create_system_packages(system_name)
    #                 target_package = system_pkg if is_new_asset else unknown_pkg
    #
    #             # Find matching part data
    #             part_data = next((data for key, data in parts_data.items()
    #                               if material_sbom in key), None)
    #
    #             if part_data and target_package:
    #                 part_block = self.part_service.create_part_block(
    #                     part_name,
    #                     target_package,
    #                     part_data
    #                 )
    #
    #                 if part_block:
    #                     self.parts_dict[part_name] = {
    #                         'qualified_name': part_block.getQualifiedName(),
    #                         'material_sbom': material_sbom,
    #                         'system': system_name if is_new_asset else 'Unknown'
    #                     }
    #                     self.parts_dict[part_name].update(part_data)
    #
    #     except Exception as e:
    #         printer("Error processing parts for system {0}: {1}".format(system_name, str(e)))

    def process_assets(self, assets_data, system_name, current_system_num, total_systems):
        """Process assets in two passes - first create blocks, then establish relationships"""
        try:
            # First pass - create all asset blocks
            created_blocks = {}
            system_pkg, unknown_pkg = self.asset_service.create_asset_system_packages(system_name)
            asset_package_names = [pkg.getName() for pkg in self.asset_package.getOwnedElement()
                                   if isinstance(pkg, Package)]

            is_new_asset_set = set()
            for asset_id, asset_data in assets_data.items():
                if self.progress.is_cancelled():
                    self.progress.update_progress(self.progress.current_progress, "Import cancelled by user")
                    return
                
                # Cache asset_status in first loop for reuse in third passes
                asset_data['_cached_asset_status'] = asset_data.get('asset_status')
                if asset_data['_cached_asset_status'] in ["new", "moving_to", "moving_from", "remove"]:
                    is_new_asset_set.add(asset_id)
                asset_block = self.asset_service.create_asset_block(
                    asset_id,
                    asset_data,
                    system_name
                )
                if asset_block:
                    created_blocks[asset_id] = asset_block
                    asset_status = asset_data['_cached_asset_status']
                    asset_id, entry = self.model_dict.update_asset_dictionary_entry(
                        asset_block,
                        asset_package_names=asset_package_names,
                        asset_status=asset_status
                    )
                    if asset_id:
                        self.assets_dict[asset_id] = entry

            self.progress.update_progress(self.progress.current_progress, 
                                        "<html>{0}:<br>Asset creation completed - establishing relationships...</html>".format(system_name))

            # Second pass - create relationships
            for asset_id, asset_data in assets_data.items():
                if self.progress.is_cancelled():
                    self.progress.update_progress(self.progress.current_progress, "Import cancelled by user")
                    return
                    
                asset_block = created_blocks.get(asset_id)
                if asset_block:
                    # Create all relationships
                    material_type = self.materials_dict[asset_data.get('material_sbom')]['generalization'].get('classifier') if asset_data.get('material_sbom') else None

                    self.asset_service.create_parent_asset_relationship(
                        asset_block, asset_data.get('parent'), self.asset_package
                    )
                    self.asset_service.create_location_relationship(
                        asset_block, asset_data.get('location__room__id'),
                        self.locations_dict, self.location_package
                    )
                    # self.asset_service.create_part_relationship(
                    #     asset_block, asset_data.get('material_sbom'),
                    #     self.parts_dict, self.part_package,
                    #     system_name, is_new_asset
                    # )

                    # Update dictionary after ALL relationships are created
                    asset_status = asset_data['_cached_asset_status']
                    asset_id, updated_entry = self.model_dict.update_asset_dictionary_entry(
                        asset_block,
                        asset_package_names=asset_package_names,
                        asset_status=asset_status
                    )
                    if asset_id:
                        self.assets_dict[asset_id] = updated_entry
                        self.asset_service.existing_assets_dict[asset_id] = updated_entry

            # Third pass - create system relationships and references
            for asset_id, asset_data in assets_data.items():
                asset_status = asset_data['_cached_asset_status']
                is_new_asset = asset_id in is_new_asset_set
                
                # Create reference property for all assets
                asset_block = created_blocks.get(asset_id)
                if asset_block:
                    material_sbom = asset_data.get('material_sbom')
                    material_type = self.materials_dict[material_sbom]['generalization'].get('classifier') if material_sbom else None
                    if material_type not in ["Cable", "Connector", "Transceiver", "Cable Component"]:
                        self.asset_service.create_asset_references(asset_block, asset_id)

            root_assets = build_hierarchy_roots(is_new_asset_set, self.asset_service.existing_assets_dict)

            self.consolidated_new_asset_set.update(is_new_asset_set)
            
            for asset_id in root_assets:
                asset_data = assets_data[asset_id]
                asset_block = created_blocks.get(asset_id)
                if asset_block:
                    material_sbom = asset_data.get('material_sbom')
                    material_type = self.materials_dict[material_sbom]['generalization'].get('classifier') if material_sbom else None
                    self.asset_service.create_system_relationship(
                        asset_block, asset_id, system_name, self.system_package, True, material_type
                    )

            self.progress.update_progress(self.progress.current_progress,
                                        "<html>System {0} assets completed<br>({1}/{2} systems)</html>".format(system_name, current_system_num, total_systems))

        except Exception as e:
            printer("Error processing assets for system {0}:\nError type: {1}\nError message: {2}\nTraceback:\n{3}".format(
                system_name,
                type(e).__name__,
                str(e),
                traceback.format_exc()
            ))

    def process_old_connections(self, connections_data, system_name):
        """Process connections for a system"""
        try:
            # Create connections package
            connections_package = self.old_connection_service.create_connections_package(system_name)

            connections_list = list(connections_data.items())
            
            for connection_id, connection_data in connections_list:
                if self.progress.is_cancelled():
                    self.progress.update_progress(self.progress.current_progress, "Import cancelled by user")
                    return
                    
                self.old_connection_service.create_connection(
                    connection_data,
                    connections_package
                )
                        
        except Exception as e:
            printer("Critical error processing old connections for system {0}:\nError type: {1}\nMessage: {2}\nTrace:\n{3}".format(
                system_name, type(e).__name__, str(e), traceback.format_exc()))

    #___________________MAIN Connections Handling_________________#
    def process_new_connections(self):
        try:
            self.progress.update_progress(80, "Initializing connection processing...")
            
            # Session 2: Connection Processing
            with SessionManager("Connection Processing", auto_save=True):
                # Set the "SITE" block under Location View package for FID Lv1
                self.progress.update_progress(82, "Setting up site block and port stereotypes...")
                self.new_connection_service.set_site_block(self.location_service.site_block)

                # Get IDP Port stereotype from resources
                port_profile = next((item for item in self.idp_resources.getOwnedMember()
                                     if item.getName() == "Port Stereotype"), None)

                if port_profile:
                    port_stereotype = next((st for st in port_profile.getOwnedMember()
                                            if isinstance(st, Stereotype) and
                                            st.getName() == "IDP Port"), None)

                self.progress.update_progress(85, "Building location elements...")
                self.new_connection_service.build_location_elements()

                # Consolidate all connections from all systems
                self.progress.update_progress(87, "Consolidating connections from all systems...")
                all_connections = {}
                for system_data in self.systems_data.values():
                    all_connections.update(system_data['connections'])

                # Process consolidated connections
                self.progress.update_progress(89, "Processing {0} connections...".format(len(all_connections)))
                decomposed, existing = self.new_connection_service.process_connections(all_connections)

                # Update progress after processing
                self.progress.update_progress(90, "Connection analysis complete - {0} new, {1} existing".format(
                    len(decomposed) if decomposed else 0, len(existing) if existing else 0))

            if existing or decomposed:
                with SessionManager("Update Connections"):
                    # Remove existing connections first
                    if existing:
                        self.progress.update_progress(91, "Removing {0} redundant connections...".format(len(existing)))
                        for del_con in existing:
                            self.remove_connector(del_con)
                    
                    # Create new connections
                    if decomposed:
                        total_new_connections = len(decomposed)
                        self.progress.update_progress(93, "Creating {0} new connections...".format(total_new_connections))

                        for i, new_con in enumerate(decomposed):
                            if self.progress.is_cancelled():
                                self.progress.update_progress(self.progress.current_progress, "Import cancelled by user")
                                return

                            try:
                                if i % 5 == 0 or i == total_new_connections - 1:
                                    conn_progress = 93 + int((i / float(total_new_connections)) * 5)
                                    self.progress.update_progress(conn_progress,
                                                                "Creating connection {0}/{1}".format(i+1, total_new_connections))

                                self.new_connection_service.create_new_connection(new_con)
                            except Exception as e:
                                printer("Error creating new connection:\nError type: {0}\nTrace:\n{1}".format(
                                    type(e).__name__, traceback.format_exc()))

                self.progress.update_progress(98, 
                                            "<html>Connection processing completed<br>Saving current model...</html>")

        except Exception as e:
            printer("Critical error in connection processing:\nError type: {0}\nTrace:\n{1}".format(
                type(e).__name__, traceback.format_exc()))

    def remove_connector(self, del_con):
        """Remove a connector with proper error handling"""
        try:
            del_con_element = self.project.getElementByID(del_con['connector_id'])
            if del_con_element:
                mem.getInstance().removeElement(del_con_element)
        except:
            printer("Failed to delete connector: " + str(del_con['connector_id']))

    #___________________COMPOSITE RELATIONSHIP FOR CONNECTION_________________#
    def build_part_property_dictionaries(self):
        try:
            self.progress.update_progress(75, "Building part property dictionaries...")
            
            asset_names_list = {v['asset_name']: k for k, v in self.assets_dict.items()}
            
            self.progress.update_progress(76, "Building asset part properties...")
            self.as_par_dict = self.model_dict.build_part_prop_dict(self.asset_package, asset_names_list)
            
            self.progress.update_progress(77, "Building location part properties...")
            self.loc_par_dict = self.model_dict.build_part_prop_dict(self.location_package, asset_names_list)
            
            self.progress.update_progress(78, "Building system part properties...")
            self.sys_par_dict = self.model_dict.build_part_prop_dict(self.system_package, asset_names_list)
            self.site_par_dict = self.model_dict.build_site_part_props(self.location_service.site_block)

            # Update the ConnectionService with the new dictionaries
            self.progress.update_progress(79, "Updating connection service with dictionaries...")
            self.new_connection_service.part_prop_dictionaries(
                self.as_par_dict, self.sys_par_dict, self.loc_par_dict, self.site_par_dict
            )

            self.new_connection_service.set_new_asset_set(self.consolidated_new_asset_set)

        except Exception as e:
            printer("Error building part property dictionaries: {0}".format(str(e)))

    def _calculate_system_size(self, system_data):
        try:
            assets = len(system_data.get('assets', {}))
            connections = len(system_data.get('connections', {}))
            return assets + connections
            
        except Exception as e:
            printer("Error calculating system size: {0}".format(str(e)))
            return 0

def main():
    """Main application entry point"""
    try:
        # Clear ancestry cache at start of new import session
        ancestry_tracer.clear_cache()

        app = MDApplication(json_folder_path)

        # Setup initial structure
        app.initialize_project()

        # Load all JSON data
        app.load_json_files()

        # Process systems with adaptive batching based on complexity
        total_systems = len(app.systems_data)
        current_system_index = 0
        
        # Simple batching parameters based on element count
        MAX_BATCH_SIZE = 10000        # Total assets + connections per batch
        MAX_SYSTEMS_PER_BATCH = 5     # Hard limit on systems per batch
        
        system_items = list(app.systems_data.items())
        batch_start = 0
        batch_number = 1
        
        while batch_start < total_systems:
            current_batch = []
            batch_total_size = 0
            systems_in_batch = 0
            
            # Build batch based on simple element count
            for i in range(batch_start, total_systems):
                system_name, system_data = system_items[i]
                
                # Calculate system size (assets + connections)
                system_size = app._calculate_system_size(system_data)
                
                # Check if adding this system would exceed batch limits
                if (batch_total_size + system_size > MAX_BATCH_SIZE and len(current_batch) > 0) or \
                   (systems_in_batch >= MAX_SYSTEMS_PER_BATCH):
                    break
                
                current_batch.append((system_name, system_data))
                batch_total_size += system_size
                systems_in_batch += 1
            
            batch_end = batch_start + len(current_batch)
            
            # Process current batch
            app.progress.update_progress(
                app.progress.calculate_system_progress(batch_start, total_systems, 40, 40),
                "Processing batch {0}: {1} systems ({2} elements total)".format(
                    batch_number, len(current_batch), batch_total_size)
            )
            
            for system_name, system_data in current_batch:
                try:
                    app.process_system_import(system_name, system_data, current_system_index, total_systems)
                    current_system_index += 1
                except Exception as e:
                    printer("Error on system {0}: {1}".format(system_name, str(e)))
                    current_system_index += 1
                    continue
            
            # Save after each batch (except final batch - handled at end)
            if batch_end < total_systems:
                app.progress.update_progress(
                    app.progress.calculate_system_progress(batch_end, total_systems, 40, 40),
                    "Saving batch {0} ({1} systems)... Please wait".format(batch_number, len(current_batch))
                )
                
                try:
                    # Use synchronized save with waiting
                    with SessionManager("Batch Save", auto_save=True):
                        pass  # SessionManager handles the save with synchronization
                    
                    app.progress.update_progress(
                        app.progress.calculate_system_progress(batch_end, total_systems, 40, 40),
                        "Batch {0} saved successfully".format(batch_number)
                    )
                    
                except Exception as e:
                    printer("Batch save error: {0}".format(str(e)))
            
            batch_start = batch_end
            batch_number += 1

        # All systems processed milestone
        app.progress.update_progress(79, "<html>All systems completed and saved<br>Starting connection processing...</html>")
        
        # Build part property dictionaries after processing all systems
        if app.progress.is_cancelled():
            app.progress.update_progress(app.progress.current_progress, "Import cancelled by user")
            return
        app.build_part_property_dictionaries()

        # Process new-style connections
        if app.progress.is_cancelled():
            app.progress.update_progress(app.progress.current_progress, "Import cancelled by user")
            return
        app.process_new_connections()
        
        # Final completion
        app.progress.update_progress(99, "Finalizing import process...")

        # Single manual save at the very end
        try:
            project = Application.getInstance().getProject()
            if project and project.isDirty():
                projectsManager = Application.getInstance().getProjectsManager()
                from com.nomagic.magicdraw.core.project import ProjectDescriptorsFactory
                projectDescriptor = ProjectDescriptorsFactory.getDescriptorForProject(project)
                projectsManager.saveProject(projectDescriptor, True)
        except Exception as e:
            printer("Final save error: {0}".format(str(e)))

        app.progress.update_progress(100, "Import process completed successfully!")
    
    except Exception as e:
        printer("Critical error:\nError type: {0}\nError message: {1}\nTraceback:\n{2}".format(
            type(e).__name__,
            str(e),
            traceback.format_exc()
        ))
    finally:
        pass

if __name__ in ['__builtin__', '__main__']:
    main()
