package com.pmw790.functions;

import com.nomagic.magicdraw.core.Application;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Namespace;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.ElementTaggedValue;
import com.nomagic.magicdraw.actions.*;

import com.pmw790.functions.ToolkitUtilities;
import com.pmw790.functions.ToolkitUtilities.PathResult;

import java.awt.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;

public class Finders {
	  public static List<Map<String, Object>> findConnectors(Element element) {
		  Namespace nameEl = (Namespace) element;
		  List<Map<String, Object>> connectors = new ArrayList<>();
		  for (Element subel : nameEl.getOwnedMember()) {
			  if (subel.getHumanType().equals("Connector")) {
				  Map<String, Object> connectorData = new HashMap<>();
				  connectorData.put("connector_element",subel);
				  connectorData.put("connected_elements",getConnectedElements((Connector) subel));
				  connectors.add(connectorData);
			  }  
		  }
		  return connectors;
	  }
	  
	  public static List<Map<String, Object>> findConnectionInfo(Element element) {
		  Namespace nameEl = (Namespace) element;
		  List<Map<String, Object>> connectorInfo = new ArrayList<>();
		  for (Element subel : nameEl.getOwnedMember()) {
			  if (subel.getHumanType().equals("Connector")) {
				  NamedElement c = (NamedElement) subel;
				  Map<String, Object> connectorData = new HashMap<>();
				  connectorData.put("connector_element",subel);
				  connectorData.put("connector_name", c.getName());
				  connectorData.put("connection_info",getConnectorsInfo((Connector) subel));
				  connectorInfo.add(connectorData);
			  }  
		  }
		  return connectorInfo;
	  }
	  
	  public static Map<String, Object> getConnectorsInfo(Connector connector) {
		  List<Map<String,Object>> connectedElements = new ArrayList<>();
		  Map<String, List<String>> classifierLookupMap = new HashMap<>();
		  Map<String, Object> connections = new HashMap<>();
		  List<String> topLevelElements = new ArrayList<>();
		  List<String> fullNames = new ArrayList<>();
		  for (ConnectorEnd end : connector.getEnd()) {
			  ConnectableElement role = end.getRole();
			  Map<String, Object> elementData = new HashMap<>();
			  String portCheck = "";

			  List<String> portOwnerTypeArray = new ArrayList<>();
			  List<String> portOwnerArray = new ArrayList<>();
			  String portOwner = "";
			  
			  if ("Proxy Port".equals(role.getHumanType())) {
				  elementData.put("port",role.getName());
			  }
			  
			  if (!end.getOwnedElement().isEmpty() && end.getOwnedElement() != null) {
				  Collection<Element> elT = end.getOwnedElement();
				  for (Element endEl : elT) {
					  if (endEl instanceof ElementTaggedValue) {
						  ElementTaggedValue tV = (ElementTaggedValue) endEl;
						  List<Element> actualEnds = tV.getValue();
						  for (Element aE : actualEnds) {
							  String typeName;
							  NamedElement nameE = (NamedElement) aE;
							  Property propE = (Property) aE;

							  List<String> foundClassifiers = ToolkitUtilities.findGeneralizationClasses(propE.getType());
							  try {
								  typeName = propE.getType().getName();	  
							  } catch (Exception e) {
								  typeName = "System Owning Block";
							  }
							  portOwnerTypeArray.add(typeName);
							  portOwnerArray.add(nameE.getName());
							  classifierLookupMap.put(nameE.getName(),foundClassifiers);
						  }
					  }
					  topLevelElements.add(portOwnerArray.get(0));
				  }
			  }
			  
			  if ("Part Property".equals(role.getHumanType())) {
				  portOwnerTypeArray.add(role.getType().getName());
				  portOwnerArray.add(role.getName());
				  classifierLookupMap.put(role.getType().getName(),ToolkitUtilities.findGeneralizationClasses(role.getType()));
			  }
			  
			  portOwner = String.join(".", portOwnerArray);
			  if (!portOwner.isEmpty()) {
				  fullNames.add(portOwner);
				  int lastIndexType = portOwnerTypeArray.size()-1;
				  int lastIndexName = portOwnerArray.size()-1;
				  if (lastIndexType >= 0 && lastIndexName >= 0) {
					  String lastType = portOwnerTypeArray.get(lastIndexType);
					  String lastName = portOwnerArray.get(lastIndexName);
					  if (lastName.equals(lastType)) {
						  elementData.put("name", lastType);
						  elementData.put("classifier", classifierLookupMap.getOrDefault(lastType, new ArrayList<>()));
					  } else {
						  elementData.put("name", lastName);		
						  elementData.put("classifier", classifierLookupMap.getOrDefault(lastType, new ArrayList<>()));
					  }
					  connectedElements.add(elementData);
				  }
			  } else {
				  try {
					  fullNames.add(role.getName());
					  elementData.put("name", role.getName());
					  List<String> foundClassifiers = ToolkitUtilities.findGeneralizationClasses(role.getType());
					  elementData.put("classifier", foundClassifiers);
				  } catch (Exception e) {
					  fullNames.add(role.getName());
					  elementData.put("name", role.getType().getName());
				  }
				  connectedElements.add(elementData);
			  }
		  }
		  connections.put("Connected Elements", connectedElements);
		  connections.put("Top Level Elements", topLevelElements);
		  connections.put("Full Connection Strings", fullNames);
		  return connections;
	  }

	  public static List<Map<String,Object>> getConnectedElements(Connector connector) {
		  List<Map<String,Object>> connectedElements = new ArrayList<>();
		  Map<String, List<String>> classifierLookupMap = new HashMap<>();
		  for (ConnectorEnd end : connector.getEnd()) {
			  ConnectableElement role = end.getRole();
			  Map<String, Object> elementData = new HashMap<>();
			  String portCheck = "";

			  List<String> portOwnerTypeArray = new ArrayList<>();
			  List<String> portOwnerArray = new ArrayList<>();
			  String portOwner = "";
			  
			  if ("Proxy Port".equals(role.getHumanType())) {
				  elementData.put("port",role.getName());
			  }
			  
			  if (!end.getOwnedElement().isEmpty() && end.getOwnedElement() != null) {
				  Collection<Element> elT = end.getOwnedElement();
				  for (Element endEl : elT) {
					  if (endEl instanceof ElementTaggedValue) {
						  ElementTaggedValue tV = (ElementTaggedValue) endEl;
						  List<Element> actualEnds = tV.getValue();
						  for (Element aE : actualEnds) {
							  String typeName;
							  NamedElement nameE = (NamedElement) aE;
							  Property propE = (Property) aE;
							  List<String> foundClassifiers = ToolkitUtilities.findGeneralizationClasses(propE.getType());
							  try {
								  typeName = propE.getType().getName();	  
							  } catch (Exception e) {
								  typeName = "System Owning Block";
							  }
							  portOwnerTypeArray.add(typeName);
							  portOwnerArray.add(nameE.getName());
							  classifierLookupMap.put(nameE.getName(),foundClassifiers);
						  }
					  }
			  }
		  }
		  
		  if ("Part Property".equals(role.getHumanType())) {
			  portOwnerTypeArray.add(role.getType().getName());
			  portOwnerArray.add(role.getName());
			  classifierLookupMap.put(role.getType().getName(),ToolkitUtilities.findGeneralizationClasses(role.getType()));
		  }
		  
		  portOwner = String.join(".", portOwnerArray);
		  if (!portOwner.isEmpty()) {
			  int lastIndexType = portOwnerTypeArray.size()-1;
			  int lastIndexName = portOwnerArray.size()-1;
			  if (lastIndexType >= 0 && lastIndexName >= 0) {
				  String lastType = portOwnerTypeArray.get(lastIndexType);
				  String lastName = portOwnerArray.get(lastIndexName);
				  if (lastName.equals(lastType)) {
					  elementData.put("name", lastType);
					  elementData.put("classifier", classifierLookupMap.getOrDefault(lastType, new ArrayList<>()));
				  } else {
					  elementData.put("name", lastName);		
					  elementData.put("classifier", classifierLookupMap.getOrDefault(lastType, new ArrayList<>()));
				  }
				  connectedElements.add(elementData);
			  }
		  } else {
			  try {
				  elementData.put("name", role.getName());
				  List<String> foundClassifiers = ToolkitUtilities.findGeneralizationClasses(role.getType());
				  elementData.put("classifier", foundClassifiers);
			  } catch (Exception e) {
				  elementData.put("name", role.getType().getName());
			  }
			  connectedElements.add(elementData);
		  }
	  }
	  return connectedElements;
  }
  
  public static Map<String,Set<String>> buildAdjacencyAndFindConnectors(List<Map<String, Object>> connectors,Set<String> connectorClassifiers, Set<String> connectorNodes){
	  Map<String, Set<String>> adjacency = new HashMap<>();
	  List<List<String>> namePairs = ToolkitUtilities.extractPairedNames(connectors);
	  
	  for (Map<String,Object> connector: connectors) {
		  List<Map<String,Object>> connectedElements = (List<Map<String,Object>>) connector.get("connected_elements");
		  if (connectedElements != null && connectedElements.size() == 2) {
			  Map<String,Object> el1 = connectedElements.get(0);
			  Map<String,Object> el2 = connectedElements.get(1);
			  String node1 = (String) el1.get("name");
			  String node2 = (String) el2.get("name");
			  if (ToolkitUtilities.isConnectorMap(el1,connectorClassifiers)) {
				  connectorNodes.add(node1);
			  }
			  if (ToolkitUtilities.isConnectorMap(el2, connectorClassifiers)) {
				  connectorNodes.add(node2);
			  }
			  adjacency.putIfAbsent(node1, new HashSet<>());
			  adjacency.putIfAbsent(node2, new HashSet<>());
			  adjacency.get(node1).add(node2);
			  adjacency.get(node2).add(node1);  
		  }
	  }
	  return adjacency;		  
  }
  
  public static Set<Set<String>> findAllEndpoints(Map<String,Set<String>> adjacency, Set<String> connectorNodes) {
	  Set<String> visited = new HashSet<>();
	  Set<Set<String>> groups = new HashSet<>();
	  
	  for (String node: adjacency.keySet()) {
		  if (!visited.contains(node)) {
			  Set<String> component = new HashSet<>();
			  Queue<String> queue = new LinkedList<>();
			  queue.offer(node);
			  visited.add(node);
			  
			  while (!queue.isEmpty()) {
				  String current = queue.poll();
				  component.add(current);
				  
				  for (String neighbor : adjacency.getOrDefault(current,  Collections.emptySet())){
					  if (!visited.contains(neighbor)) {
						  visited.add(neighbor);
						  queue.offer(neighbor);
					  }
				  }
			  }
			  component.removeAll(connectorNodes);
			  if (component.size() > 1) {
				  groups.add(component);
			  }
		  }
	  }
	  return groups;
  }
  
  public static List<List<String>> findEffectiveConnections(Map<String, Set<String>> adjacency, Set<String> connectorNodes){
	  Set<String> visitedGlobal = new HashSet <>();
	  Set<List<String>> pairs = new HashSet <>();
	  for (String node : adjacency.keySet()) {
		  if (!connectorNodes.contains(node)) {
			  if (!visitedGlobal.contains(node)) {
				  Set<String> visitedLocal = new HashSet<>();
				  PathResult result = ToolkitUtilities.tracePath(node, adjacency, connectorNodes, visitedLocal);
				  visitedGlobal.addAll(visitedLocal);
				  for (String end : result.endPoints) {
					  List<String> pair = ToolkitUtilities.sortPair(node, end);
					  pairs.add(pair);
				  }
			  }
		  }
	  }
	  return new ArrayList<>(pairs);
  }
  
  public static List<List<String>> gatherConnections(Element element, boolean recursive, Set<String> con, Set<String> connectorNodes, List<Map<String,Object>> connectors){		  
	  	  
	  Map<String, Set<String>> adjacency = buildAdjacencyAndFindConnectors(connectors, con, connectorNodes);
	  List<List<String>> effectiveConnections = findEffectiveConnections(adjacency, connectorNodes);
	  
	  if (recursive) {
		  for (Element subel : element.getOwnedElement()) {
			  if ("Part Property".equals(subel.getHumanType())) {
				  Property subPP = (Property) subel;
				  Element trueSub = subPP.getType();
				  
				  if (trueSub != null) {
					  List<List<String>> subConnections = gatherConnections(trueSub, true, con, connectorNodes,connectors);
					  effectiveConnections.addAll(subConnections);
				  }
			  }
		  }
	  }
	  return effectiveConnections;
  }
  
  public static Set<String> extractPairsNoPort(List<Map<String,Object>> connectors){
	  Set<String> pairsNoPort = new HashSet<>();
	  for (Map<String,Object> connector : connectors) {
		  List<Map<String,Object>> connectedElements = (List<Map<String,Object>>) connector.get("connected_elements");
		  if (connectedElements != null && connectedElements.size() == 2) {
			  Map<String,Object> el1 = connectedElements.get(0);
			  Map<String,Object> el2 = connectedElements.get(1);
			  
			  if (el1.get("port") != null || el2.get("port") != null) {
				  continue;
			  }
			  String el1name = (String) el1.get("name");
			  String el2name = (String) el2.get("name");
			  String pairKey = ToolkitUtilities.makePairKey(el1name,el2name);
			  pairsNoPort.add(pairKey);
		  }
	  }
	  return pairsNoPort;
  }
  
  public static Connector findConnectorFromPair(List<Map<String,Object>> connectors, String e1, String e2){
	  Connector conn;
	  for (Map<String,Object> connector : connectors) {
		  List<Map<String,Object>> connectedElements = (List<Map<String,Object>>) connector.get("connected_elements");
		  if (connectedElements != null && connectedElements.size() == 2) {
			  Map<String,Object> el1 = connectedElements.get(0);
			  Map<String,Object> el2 = connectedElements.get(1);
			  
			  if (el1.get("port") != null || el2.get("port") != null) {
				  continue;
			  }
			  String el1name = (String) el1.get("name");
			  String el2name = (String) el2.get("name");
			  String pairKey = ToolkitUtilities.makePairKey(el1name,el2name);
			  String compKey = ToolkitUtilities.makePairKey(e1, e2);
			  
			  if (pairKey.equals(compKey)) {
				Object connOb = connector.get("connector_element");  
				conn = (Connector) connOb;
				return conn;
			  }
		  }
	  }
	  return null;
  }
  
  public static Map<String,List<List<String>>> setComp(List<List<String>> compSet, Set<String> processedSet){
	  Map<String,List<List<String>>> compResults = new HashMap<>();
	  List<List<String>> missingPairs = new ArrayList<>();
	  List<List<String>> existingPairs = new ArrayList<>();
	  
	  for (List<String> pair : compSet) {
		  if (pair.size() != 2) {
			  continue;
		  }
		  String name1 = pair.get(0);
		  String name2 = pair.get(1);
		  
		  String pairKey = ToolkitUtilities.makePairKey(name1, name2);
		  
		  if (!processedSet.contains(pairKey)) {
			  missingPairs.add(pair);
		  } else if (processedSet.contains(pairKey)) {
			  existingPairs.add(pair);
		  }
	  }
	  compResults.put("missingPairs",missingPairs);
	  compResults.put("existingPairs", existingPairs);
	  return compResults;
  }
  
  public static List<Map<String,Object>> findPartProperties(Class element){
	  List<Map<String,Object>> partProps = new ArrayList<>();
	  for (Property subProp : element.getOwnedAttribute()) {
		  Map<String, Object> propData = new HashMap<>();
		  if ("Part Property".equals(subProp.getHumanType())){
			  propData.put("name",subProp.getName());
			  propData.put("partProp",subProp);
			  partProps.add(propData);
		  }
	  }		  
	  return partProps;
  }

  
  public static class LogConnectorsAction extends MDAction {
	  private final Element selectedElement;
	  
	  public LogConnectorsAction(Element selectedElement) {
		  super("LOG_CONNECTORS_ACTION", "Show Connected Elements", null, null);
		  this.selectedElement = selectedElement;
	  }
	  
	  public void actionPerformed(ActionEvent e) {
		  List<Map<String, Object>> connectors = Finders.findConnectors(selectedElement);
		  ToolkitUtilities.Log(connectors);
	  }
  }
};