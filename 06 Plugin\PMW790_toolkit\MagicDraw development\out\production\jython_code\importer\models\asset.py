from core.imports import *
from core.constants import PACKAGE_VIEWS
from utils.md_utils import printer

class AssetService:
    """Manages asset blocks in Assets View"""

    def __init__(self, project, package_service, creator_service, assets_dict, material_dict, idp_resources):
        self.project = project
        self.package_service = package_service
        self.creator = creator_service
        self.materials_dict = material_dict
        self.existing_assets_dict = assets_dict
        self.room_locations = {}
        self.assets_view = PACKAGE_VIEWS["ASSETS"]
        self.assets_package = f.byQualifiedName().find(self.project, self.assets_view)
        self.processed_assets = set()
        self.asset_no_composite = ["Cable", "Connector", "Transceiver", "Cable Connector"]
        self.unknown_package = None
        # First find the Port Stereotype profile
        port_profile = next((item for item in idp_resources.getOwnedMember()
                             if item.getName() == "Port Stereotype"), None)

        if port_profile:
            # Then look inside the profile for the IDP Port stereotype
            self.port_stereotype = next((st for st in port_profile.getOwnedMember()
                                         if isinstance(st, Stereotype) and
                                         st.getName() == "IDP Port"), None)

    def get_asset_block(self, asset_id):
        """Find existing asset block by ID"""
        if asset_id in self.existing_assets_dict:
            return f.byQualifiedName().find(self.project,
                                            self.existing_assets_dict[asset_id]['qualified_name'])
        return None

    def create_asset_system_packages(self, system_name):
        """Creates system-specific and unknown packages for assets"""
        packages = {pkg.getName(): pkg for pkg in self.assets_package.getOwnedElement()
                    if isinstance(pkg, Package)}

        system_package = packages.get(system_name) or \
                         self.package_service.create_package(system_name, self.assets_package)

        if not self.unknown_package:
            self.unknown_package = packages.get("Unknown") or \
                                  self.package_service.create_package("Unknown", self.assets_package)

        return system_package, self.unknown_package

    def get_unknown_package(self):
        """Gets the existing Unknown package from assets"""
        return self.unknown_package

    def create_asset_block(self, asset_id, asset_data, system_name):
        """Creates an asset block with properties"""
        # Check if already processed
        asset_block = self.get_asset_block(asset_id)
        is_new_asset = asset_data.get('asset_status') in ["new", "moving_to", "moving_from", "remove"]
        system_pkg, unknown_pkg = self.create_asset_system_packages(system_name)

        # Validate material exists first
        asset_class = asset_data.get('material_sbom')
        asset_class_name = self.materials_dict[asset_class]['qualified_name']
        if not asset_class or asset_class not in self.materials_dict:
            printer("Error: Invalid material SBOM for asset {0}".format(asset_class_name))
            return None

        if asset_block:
            current_package = asset_block.getOwningPackage()
            # Move from Unknown package to current System Package
            if is_new_asset and current_package == unknown_pkg:
                asset_block.setOwner(system_pkg)
        else:
            # Create new asset in appropriate package
            target_package = system_pkg if is_new_asset else unknown_pkg
            if not asset_block:
                asset_block = self.creator.create_IDP_Element(
                    asset_data['name'],
                    asset_class_name,
                    target_package
                )

        # Get material info and taxonomy classifier
        material_info = self.materials_dict[asset_class]
        material_classifier = material_info.get('generalization', {}).get('classifier')

        # Update properties for both new and existing assets
        if asset_block:
            if asset_block.getName() != asset_data['name']:
                asset_block.setName(asset_data['name'])

            self.creator.process_classifiers(
                asset_block,
                material_classifier,
                asset_data
            )

        # Process ports after block is created/updated
        if asset_block:
            self.process_ports(asset_block, asset_data.get('material_sbom'))

        return asset_block

    def process_ports(self, asset_block, material_sbom):
        """Process ports for an asset block from material IDP Stereotype"""
        if not material_sbom or material_sbom not in self.materials_dict:
            return

        # Get existing and material ports
        existing_ports = {p.getName(): p for p in asset_block.getOwnedPort()}
        material_ports = self.materials_dict[material_sbom].get('ports', {})

        # Remove ports no longer in material template
        for port_name, port in existing_ports.items():
            if port_name not in material_ports:
                mem.getInstance().removeElement(port)

        # Create/update ports
        for port_name, port_info in material_ports.items():
            if port_name not in existing_ports:
                self.create_port(asset_block, port_name, port_info)
            else:
                self.update_port(existing_ports[port_name], port_info)

    def create_port(self, asset_block, port_name, port_info):
        """Create new port with properties from template"""
        try:
            # Create port instance using project's ElementsFactory
            new_port_oo = self.project.getElementsFactory().createPortInstance()
            new_port_oo.setVisibility(VisibilityKindEnum.getByName("public"))
            new_port_oo.setAggregation(AggregationKindEnum.getByName("composite"))
            new_port_oo.setName(port_name)
            new_port_oo.setOwner(asset_block)

            # Add IDP Port stereotype
            sh.addStereotype(new_port_oo, self.port_stereotype)

            # Get template port and add redefinitions
            template_port = f.byQualifiedName().find(self.project, port_info['qualified_name'])
            if template_port:
                # Get actual port element after creation
                for port in asset_block.getOwnedPort():
                    if port.getName() == port_name:
                        port.getRedefinedPort().add(template_port)
                        port.getRedefinedProperty().add(template_port)

                        # Copy tagged values
                        for tag_name, tag_value in port_info['tagged_values'].items():
                            if tag_name != 'qualified_name':
                                sh.setStereotypePropertyValue(port, self.port_stereotype,
                                                              tag_name, tag_value)
                        return port

            return None
        except Exception as e:
            printer("Error creating port {0}: {1}".format(port_name, str(e)))
            return None

    def update_port(self, port, port_info):
        """Update existing port properties"""
        template_port = f.byQualifiedName().find(self.project, port_info['qualified_name'])
        if not template_port:
            return

        # Update tagged values
        for tag in template_port.getTaggedValue():
            current_value = None
            for existing_tag in port.getTaggedValue():
                if existing_tag.tagDefinition.name == tag.tagDefinition.name:
                    current_value = existing_tag.getValue()[0]
                    break

            if current_value != tag.getValue()[0]:
                sh.setStereotypePropertyValue(port, self.port_stereotype,
                                              tag.tagDefinition.name,
                                              tag.getValue()[0])

    def create_system_relationship(self, asset_block, asset_id, system_name, systems_package, is_new_asset, material_type):
        """Create composite relationship with system if appropriate"""
        if not is_new_asset:
            return

        if material_type not in self.asset_no_composite:
            system_block = f.byQualifiedName().find(self.project, PACKAGE_VIEWS["SYSTEMS"] + "::" + system_name)
            if system_block:
                self.creator.createCompositeRelationship(
                    system_block,
                    asset_block,
                    asset_block.getName(),
                    systems_package
                )

    def create_parent_asset_relationship(self, asset_block, parent_id, assets_package):
        """Create composite relationship with parent asset"""
        if not parent_id:
            return

        parent_asset = self.get_asset_block(parent_id)
        if parent_asset:
            existing_relationship = False
            for rel in asset_block.get_relationshipOfRelatedElement():
                if (rel.getHumanType() == "Association" and
                        any(end.getType() == parent_asset for end in rel.getMemberEnd())):
                    existing_relationship = True
                    break

            if not existing_relationship:
                self.creator.createCompositeRelationship(
                    parent_asset,
                    asset_block,
                    asset_block.getName(),
                    assets_package
                )

    def create_location_relationship(self, asset_block, location_id, existing_locations_dict, location_package):
        """Create composite relationship with location"""
        if location_id and location_id in existing_locations_dict:
            location_block = f.byQualifiedName().find(
                self.project,
                existing_locations_dict[location_id]['qualified_name']
            )
            if location_block:
                existing_relationship = False
                for rel in asset_block.get_relationshipOfRelatedElement():
                    if (rel.getHumanType() == "Association" and
                            any(end.getType() == location_block for end in rel.getMemberEnd())):
                        existing_relationship = True
                        break

                if not existing_relationship:
                    self.creator.createCompositeRelationship(
                        location_block,
                        asset_block,
                        asset_block.getName(),
                        location_package
                    )
    def set_locations_dict(self, locations_dict):
        """Creates mapping of room locations only"""
        self.room_locations = {
            loc_id: loc_data for loc_id, loc_data in locations_dict.items()
            if loc_data.get('base_classifier') == 'Room'
        }
        
    def create_part_relationship(self, asset_block, material_sbom, parts_dict, parts_package, system_name, is_new_asset):
        """Create composite relationship with part O(1) lookup"""
        if not asset_block or not material_sbom:
            return

        # Create indices but track used SBOMs
        system_parts = {}
        unknown_parts = {}
        used_sboms = set()

        # First pass - index system parts and track used SBOMs
        for part_name, part_data in parts_dict.items():
            if 'material_sbom' not in part_data:
                continue

            sbom = part_data['material_sbom']
            if "_Part_Find_No_" in part_name and not part_name.startswith("Part_Find_No_"):
                sys_name = part_name.split("_Part_Find")[0]
                if sys_name not in system_parts:
                    system_parts[sys_name] = {}
                system_parts[sys_name][sbom] = part_name
                used_sboms.add(sbom)

        # Second pass - only index unknown parts if SBOM not used in systems
        for part_name, part_data in parts_dict.items():
            if 'material_sbom' not in part_data:
                continue

            sbom = part_data['material_sbom']
            if part_name.startswith("Part_Find_No_") and sbom not in used_sboms:
                unknown_parts[sbom] = part_name

        # O(1) lookup using indices
        matching_part = None
        if is_new_asset:
            if system_name in system_parts and material_sbom in system_parts[system_name]:
                matching_part = system_parts[system_name][material_sbom]
        else:
            if material_sbom in unknown_parts:
                matching_part = unknown_parts[material_sbom]

        if matching_part:
            part_block = f.byQualifiedName().find(self.project, parts_dict[matching_part]['qualified_name'])
            if part_block:
                self.creator.createCompositeRelationship(
                    part_block,
                    asset_block,
                    asset_block.getName(),
                    parts_package
                )

    def create_asset_references(self, asset_block, asset_id):
        """Create reference properties for an asset block"""
        if not asset_block or not asset_id in self.existing_assets_dict:
            return

        parent_id = self.existing_assets_dict[asset_id].get('parent_composite_element')
        if parent_id:
            # Create host asset reference if there's a parent asset
            self.create_host_asset_reference(asset_block,parent_id)
            # Create host location reference
            self.create_host_location_reference(asset_block,parent_id)

    def create_host_asset_reference(self, asset_block, parent_id):
        """Create reference to parent asset"""
        if not parent_id:
            return

        parent_asset = self.get_asset_block(parent_id)
        if parent_asset:
            self.create_reference_if_not_exists(
                asset_block, parent_asset, "host_asset", self.assets_package)

    def create_host_location_reference(self, asset_block, parent_id):
        """Create reference to host location"""
        if not asset_block:
            return

        location_block = self.find_host_location(parent_id)
        if location_block:
            self.create_reference_if_not_exists(
                asset_block, location_block, "host_location", self.assets_package)

    def find_host_location(self, current_id):
        """Recursively finds the host location by traversing up the asset hierarchy"""
        if not current_id:
            return None

        # First check if ID is already a room
        if current_id in self.room_locations:
            return f.byQualifiedName().find(
                self.project,
                self.room_locations[current_id]['qualified_name']
            )

        # If not a room, check if it's an asset and traverse up
        asset_data = self.existing_assets_dict.get(current_id)
        parent_id = asset_data.get('parent_composite_element')
        return self.find_host_location(parent_id)

    def create_reference_if_not_exists(self, source, target, targetName, package):
        """Create reference if it doesn't already exist"""
        existing = next((p for p in source.getOwnedAttribute()
                         if p.getName() == targetName and p.getType() == target), None)
        if not existing:
            self.creator.createReferenceProperty(source, target, targetName, package)