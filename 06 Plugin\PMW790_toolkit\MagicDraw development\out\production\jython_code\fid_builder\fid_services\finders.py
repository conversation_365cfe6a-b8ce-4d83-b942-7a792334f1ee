from fid_core.imports import *
from fid_services.graphnav import *
from fid_services.datatools import *
from fid_utils.md_utils import printer

from com.nomagic.uml2.ext.magicdraw.compositestructures.mdports import Port

'''
If it finds or gets, it lives here.
'''

def find_generalization_classes(element):
    """
    Find all generalization classes for an element.

    Parameters
    ----------
    element : Element
        The element to find generalizations for.

    Returns
    -------
    list
        List of generalization class names.
    """
    classifiers = []
    rel = element.get_relationshipOfRelatedElement()
    generalization_rel = [rels for rels in rel if rels.getHumanType() == "Generalization"]
    for rel_ in generalization_rel:
        for ix in rel_.getTarget():
            classifiers.append(ix.getName())
    return classifiers

def find_cross_system_ports(site_block, classifiers_list, nc_list):
    """
    Find cross-system ports for a given site block.

    Parameters
    ----------
    site_block : Element
        The site block element to search for cross-system ports.
    classifiers_list : list
        List of classifier names to use for filtering.
    nc_list : list
        List of name components to use for filtering.

    Returns
    -------
    tuple or None
        The result of traverse_connections for cross-system ports.
    """
    connectors_ob = []
    ob_cons = find_nested_connectors(site_block, connectors_ob, False)
    pure_part = []
    
    for conn in ob_cons:
        new_conn = dict(conn)
        new_conn['connection_info'] = dict(conn['connection_info'])
        new_conn['connection_info']['connected_elements'] = conn['connection_info']['connected_parts']
        pure_part.append(new_conn)

    element_ob_d_pp = combine_part_dicts(pure_part)
    con_ob_pairs = [ob_con['connection_info']['connected_elements'] for ob_con in pure_part]
    ob_results = traverse_connections(con_ob_pairs, element_ob_d_pp, classifiers_list, nc_list)

    return ob_results

def find_relevant_partproperties(element, part_props, classifiers_list, nc_list):
    for subel in element.getOwnedMember():
        if subel.getHumanType() == "Part Property":
            classifiers = find_classifiers(subel.getType())
            if all(x not in classifiers for x in ["Building", "Floor", "Level", "Room"]) and all(x not in classifiers for x in classifiers_list) and not any(nc.lower() in subel.getName().lower() for nc in nc_list):
                part_props.add(subel)
            find_relevant_partproperties(subel.getType(), part_props, classifiers_list, nc_list)
    return part_props

def find_nested_connectors(element, connectors, nested, seen_ids = None):
    """
    Recursively find all nested connectors within an element.

    Parameters
    ----------
    element : Element
        The element to search for connectors.
    connectors : list
        List to append found connector data to.
    nested : bool
        Whether to search nested part properties.
    seen_ids : set, optional
        Set of already seen connector IDs to avoid duplicates.

    Returns
    -------
    list
        List of connector data dictionaries.
    """
    if seen_ids is None:
        seen_ids = set()

    for subel in element.getOwnedMember():
        if subel.getHumanType()=="Connector":
            conn_id = subel.getID()
            if conn_id not in seen_ids:
                seen_ids.add(conn_id)
                connector_data = {
                    'connector_obj': subel,
                    'connector_id': subel.getID(),
                    'connection_name' : subel.getName() if subel.getName() else None,
                    'connection_info': get_con_end_info(subel),
                    'connection_stereotypes': get_stereotypes(subel),
                    'connection_metaname': get_metaname(subel)
                }
                connectors.append(connector_data)
        if nested and subel.getHumanType()=="Part Property" and all(x not in find_generalization_classes(subel.getType()) for x in ["Building", "Floor", "Level", "Room"]):
                find_nested_connectors(subel.getType(), connectors, True, seen_ids)
    return connectors

def find_nested_elements_by_classifer(element, target_elements, nested, classifier, seen_ids = None):
    """
    Recursively find all nested elements of Classifier within an element.

    Parameters
    ----------
    element : Element
        The element to search for nested elements.
    target_elements : dict
        Dictionary to store found system elements and their info.
    nested : bool
        Whether to search nested part properties.
    seen_ids : set, optional
        Set of already seen element IDs to avoid duplicates.

    Returns
    -------
    dict
        Dictionary of found classified elements and their info.
    """
    if seen_ids is None:
        seen_ids = set()

    for subel in element.getOwnedMember():
        if nested and subel.getHumanType()=="Part Property":
                el_id = subel.getType().getID()
                if el_id in seen_ids:
                    continue
                seen_ids.add(el_id)
                gen_classes = find_generalization_classes(subel.getType())
                if classifier in gen_classes:
                    target_elements[subel.getType()] = {
                        "gen_classes": gen_classes,
                        "part" : subel
                    }
                    find_nested_elements_by_classifer(subel.getType(), target_elements, True, classifier, seen_ids)

    return target_elements

def get_stereotypes(connection):
    """
    Get stereotypes from a connection element.

    Parameters
    ----------
    connection : Element
        Connection element to get stereotypes from.

    Returns
    -------
    list or None
        List of stereotypes or None if not found.
    """
    sts_ = sh.getStereotypes(connection)
    if sts_:
        return sts_
    else:
        return None
    
def get_metaname(connection):
    """
    Get the connector meta name from the FIDConnectorInfo stereotype.

    Parameters
    ----------
    connection : Element
        Connection element to get the meta name from.

    Returns
    -------
    str
        Meta name or "None" if not found.
    """
    sts_ = sh.getStereotypes(connection)
    if sts_:
        for st in sts_:
            if st.getName() == "FIDConnectorInfo":
                for tv in sh.collectVisibleTaggedValues(connection):
                    if tv.getTagDefinition().getName() == "connectorMetaName":
                        return tv.getValue()[0]
    else:
        return "None"
    
def get_con_end_info(connector):
    """
    Get all relevant information from each end of a connector.

    Parameters
    ----------
    connector : Element
        The connector element to extract end info from.

    Returns
    -------
    dict
        Dictionary containing connected elements, parts, and their info.
    """
    all_data = {}
    connected_elements = []
    connected_parts = []
    element_dict = {}
    part_dict = {}
    for end in connector.getEnd():
        end_info = {}
        nested_c = False
        chain = []
        classifiers = []
        prop_path = []
        part_desc = None
        b_port_check = False
        sys_owner = None
        port_name = None
        port_obj = None
        for x in sh.getStereotypes(end):
            if x.getName() == "NestedConnectorEnd":
                chain = sh.getTaggedValue(end, "propertyPath").getValue()
                connected_parts.append(end.getRole())
                if "port" not in end.getRole().getHumanType().lower():
                    chain = chain + [end.getRole()]
                
                if "System" in find_generalization_classes(end.getRole().getOwner()):
                    b_port_check = True
                    sys_owner = end.getRole().getOwner()

                classifiers = find_classifiers(chain[-1].getType())
                part_desc = get_classifier_description(chain[-1].getType())

                connected_elements.append(chain[-1])
                end_identifier = chain[-1]
                prop_path = chain
                nested_c = True
                full_name = ".".join([c.getName() for c in chain])

        if not nested_c:
            prop_path = [end.getRole()]
            connected_elements.append(end.getRole())
            connected_parts.append(end.getRole())
            end_identifier = end.getRole()
            if "port" not in end_identifier.getHumanType().lower():
                classifiers = find_classifiers(end.getRole().getType())
                part_desc = get_classifier_description(end.getRole().getType())
            else:
                classifiers = end_identifier.getHumanType()
                if "System" in find_generalization_classes(end.getRole().getOwner()):
                    b_port_check = True
                    sys_owner = end.getRole().getOwner()
            full_name = end.getRole().getName()

        if isinstance(end.getRole(), Port):
            port_name = end.getRole().getName()
            port_obj = end.getRole()

        end_info = {
                'prop_path' : prop_path,
                'classifiers' : classifiers,
                'full_name' : full_name,
                'is_boundary_port': b_port_check,
                'sys_owner': sys_owner,
                'part_desc': part_desc,
                'port_name': port_name,
                'port_obj': port_obj
            }
        element_dict[end_identifier] = end_info
        part_dict[end.getRole()] = end_info
        if port_obj:
            part_dict[end.getRole()]['prop_path'] = prop_path + [port_obj] 


    all_data = {
        'connected_elements': connected_elements,
        'element_dict': element_dict,
        'connected_parts': connected_parts,
        'part_dict' : part_dict
    }   
    return all_data


def find_classifiers(element, visited=None):
    """
    Recursively collect the names of all classifiers (superclasses)
    reachable via Generalization relationships for a given element.

    Parameters
    ----------
    element : Element
        The element whose classifiers are to be found.
    visited : set, optional
        Set of already visited classifier IDs to avoid cycles.

    Returns
    -------
    list
        List of classifier names (superclasses) for the element.
    """
    if visited is None:
        visited = set()
    classifiers = []

    for rel in element.get_relationshipOfRelatedElement():
        if rel.getHumanType() == "Generalization":
            for target in rel.getTarget():
                tid = target.getID()
                if tid not in visited:
                    visited.add(tid)
                    # add this immediate classifier
                    classifiers.append(target.getName())
                    # recurse to find *its* classifiers
                    classifiers.extend(find_classifiers(target, visited))

    return classifiers


def get_classifier_description(element):
    """
    Get the 'description' property value from the classifier relationships of an element.

    Parameters
    ----------
    element : Element
        The element whose classifier description is to be retrieved.

    Returns
    -------
    str or None
        The description value if found, otherwise None.
    """
    for rel in element.get_relationshipOfRelatedElement():
        if rel.getHumanType() == "Generalization":
            for target in rel.getTarget():
                for prop in target.getOwnedAttribute():
                    if prop.getName() == "description":
                        return prop.getDefaultValue().getValue()
    return None

def find_association_owning_block(element):
    """
    Find the owning block (element) for an association relationship.

    Parameters
    ----------
    element : Element
        The element whose association owning block is to be found.

    Returns
    -------
    Element or None
        The owning block element if found, otherwise None.
    """
    owning_el = None
    rel = element.get_relationshipOfRelatedElement()
    assoc_rel = [rels for rels in rel if rels.getHumanType() == "Association"]
    for rel_ in assoc_rel:
        for ix in rel_.getMemberEnd():
            if ix.getAggregation().toString() == "none":
                if "Site" in find_generalization_classes(ix.getType()) and ix.getType() != element:
                    owning_el = ix.getType()
    return owning_el

def find_stereotype_by_name_in_package(stereotype_name, top_package, project):
    for s in sh.getAllStereotypes(project):
        if s.getName() == stereotype_name and top_package in get_recursive_owner_array(s):
            return s
        
def find_sys_ports(element):
    ports = {}
    for ele in element.getOwnedElement():
        if ele.getHumanType() != "Part Property":
            continue
        if "System" in find_generalization_classes(ele.getType()):
            op = ele.getType().getOwnedPort()
            for p_ in op:                    
                ports[p_] = {
                        'name' : p_.getName(),
                        'prop_path' : tuple([ele, p_]),
                        'owning_sys' : ele.getType(),
                    }
    return ports


def get_recursive_owner_array(element, owner_array=None):
    """Get array of all owners in the ownership hierarchy.
    
    Parameters
    ----------
    element : Element
        Element to get owners for
    owner_array : list, optional
        Array to append owners to
        
    Returns
    -------
    list
        Array of owner names
    """
    if owner_array is None:
        owner_array = []
    owner = element.getOwner()
    if hasattr(owner, "getName"):
        owner_array.append(owner.getName())
        get_recursive_owner_array(owner, owner_array)
    return owner_array

def get_property_values(element, property_list):
    prop_values = {}
    good_props = []

    for prop in element.getAttribute():
        if prop.getName() in property_list:
            prop_values[prop.getName()] = prop.getDefaultValue().getValue()
            good_props.append(prop.getName())

    missing_props = list(set(property_list) - set(good_props))
    if missing_props:
        for prop in element.getInheritedMember():
            if prop.getName() in missing_props:
                prop_values[prop.getName()] = prop.getDefaultValue().getValue()

    return prop_values

def find_common_el(var1, var2, selectedElement):
    common_el = None
    if not isinstance(var1, tuple):
        common_el = var1.getOwner()
    elif not isinstance(var2, tuple):
        common_el = var2.getOwner()
    else:
        for item in var1:
            if item in var2:
                common_el  = item
                if isinstance(common_el, Property):
                    common_el = common_el.getType()
        if not common_el:
            if var1[0].getOwner() == var2[0].getOwner():
                common_el = var1[0].getOwner()
            else:
                common_el = climb_el(var1[0],var2[0], selectedElement)
    return common_el

def climb_el(el1, el2, topLevelElement):
    p1 = get_part_prop_chain(topLevelElement, el1)
    p2 = get_part_prop_chain(topLevelElement, el2)
    common_el = next((a for a, b in zip(p1, p2) if a == b), None)
    common_el = common_el.getType()
    return common_el

def get_part_prop_chain(topLevelElement, target):
    path = []

    def recurse(current_element, trail):
        if hasattr(current_element, 'getOwnedMember'):
            for sub in current_element.getOwnedMember():
                if sub.getHumanType() == "Part Property":
                    if sub == target:
                        path.extend(trail + [sub])
                        return True
                    next_type = sub.getType()
                    if recurse(next_type, trail + [sub]):
                        return True
        return False
    
    recurse(topLevelElement,[])
    return path

def get_name(element):
    """Get name from element, checking type first.
    
    Parameters
    ----------
    element : Element
        Element to get name for
        
    Returns
    -------
    str
        Element name
    """
    if element.getType():
        return element.getType().getName()
    else:
        return element.getName()
    
def get_host_asset(pe):
    """Get the host asset name from a presentation element.
    
    Parameters
    ----------
    pe : PresentationElement
        The presentation element to examine
        
    Returns
    -------
    str
        The host asset name or "None" if not found
    """
    ele = pe.getElement().getType()
    for prop in ele.getOwnedAttribute():
        if sh.hasStereotype(prop, "ReferenceProperty") and prop.getName() == "host_asset":
            return prop.getType().getName()
    return "None"


def find_and_delete_existing_diagram(project, owner_element, diagram_type, name_prefix):
    """
    Finds and deletes existing diagrams of the specified type owned by the given element
    with matching name prefix to distinguish between different diagram sources
    
    :param project: The MagicDraw project
    :param owner_element: The element that owns the diagram
    :param diagram_type: The type of diagram to find and delete
    :param name_prefix: The prefix to match in diagram names (e.g., "FID_L2")
    :return: True if existing diagram was found and deleted, False otherwise
    """
    found = False
    diagrams = project.getDiagrams()
    
    for diagram in diagrams:
        diagram_element = diagram.getElement()
        
        if (diagram_element is not None and 
            diagram_element.getOwner() == owner_element and 
            diagram.getDiagramType().getType() == diagram_type):
            
            diagram_name = diagram_element.getName()
            if diagram_name is not None and ("_" + name_prefix) in diagram_name:
                try:
                    mem.getInstance().removeElement(diagram_element)
                    found = True
                except Exception as e:
                    printer("Error deleting existing diagram: " + str(e))
                break  # Only delete the first matching diagram
    
    return found

def get_host_asset(pe):
    """Get the host asset name from a presentation element.
    
    Parameters
    ----------
    pe : PresentationElement
        The presentation element to examine
        
    Returns
    -------
    str
        The host asset name or "None" if not found
    """
    ele = pe.getElement().getType()
    for prop in ele.getOwnedAttribute():
        if sh.hasStereotype(prop, "ReferenceProperty") and prop.getName() == "host_asset":
            return prop.getType().getName()
    return "None"

def get_host_location(pe):
    """Get the host location name from a presentation element.
    
    Parameters
    ----------
    pe : PresentationElement
        The presentation element to examine
        
    Returns
    -------
    str
        The host location name or "None" if not found
    """
    ele = pe.getElement().getType()
    for prop in ele.getOwnedAttribute():
        if sh.hasStereotype(prop, "ReferenceProperty") and prop.getName() == "host_location":
            return prop.getType().getName()
    return "None"

def get_tag(element, tag_name_val):
    if sh.getStereotypes(element):
        for tags in element.getTaggedValue():
            tag_name = tags.tagDefinition.name
            if tag_name == tag_name_val:
                return tags