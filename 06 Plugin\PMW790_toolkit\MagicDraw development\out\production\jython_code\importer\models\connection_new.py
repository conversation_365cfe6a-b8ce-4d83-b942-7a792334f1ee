from core.imports import *
from core.constants import PAC<PERSON>GE_VIEWS
from utils.md_utils import printer, ancestry_tracer

class ConnectionService:
    """Service for managing and creating connections between model elements.
    Handles both physical and logical connections between assets, ports, cables and connectors.
    """
    def __init__(self, project, package_service, creator_service, assets_dict, systems_dict, locations_dict, is_new_asset_set=None, asset_service=None):
        self.project = project
        self.package_service = package_service
        self.creator = creator_service
        self.existing_assets_dict = assets_dict
        self.existing_systems_dict = systems_dict
        self.existing_locations_dict = locations_dict
        self.is_new_asset_set = is_new_asset_set or set() # Set of asset IDs that are not in Unknown packages
        self.asset_service = asset_service

        # Core configuration
        self.site_block = None
        self.fullport_stereotype = None

        # Package configurations
        self.physical_packages = [PACKAGE_VIEWS["ASSETS"], PACKAGE_VIEWS["LOCATION"]]
        self.logical_packages = [PACKAGE_VIEWS["SYSTEMS"]]

        # Location elements structure
        self.location_elements = {
            'room': {},
            'floor': {},
            'building': {},
            'site': {}
        }

        # Caches and tracking
        self._path_name_cache = {}
        self._connector_cache = {}
        self._system_element_cache = {}
        self._system_path_build_cache = {}
        self._diversion_point_cache = {}
        self._asset_element_cache = {}
        self._asset_property_cache = {}
        self._asset_ports_cache = {}
        self.connectors = []

        # Part property dictionaries
        self.as_par_dict = {}
        self.sys_par_dict = {}
        self.loc_par_dict = {}

    #_____INITIAL SET-UP_________________#
    def set_site_block(self, site_block):
        """Sets reference to site block for system connections"""
        self.site_block = site_block


    def initialize_fullport_stereotype(self):
        """Initialize FullPort stereotype from SysML profile"""
        try:
            sysml_profile = sh.getProfile(self.project, "SysML")
            if not sysml_profile:
                raise ValueError("SysML Profile not found in project")
                
            self.fullport_stereotype = sh.getStereotype(self.project, "FullPort", sysml_profile)
            if not self.fullport_stereotype:
                raise ValueError("FullPort stereotype not found in SysML profile")

        except Exception as e:
            printer("ERROR: Failed to initialize FullPort stereotype: {0}".format(str(e)))
            raise e

    def part_prop_dictionaries(self, as_par_dict, sys_par_dict, loc_par_dict, site_par_dict):
        """Sets dictionaries for part property lookups"""
        self.as_par_dict = as_par_dict
        self.sys_par_dict = sys_par_dict
        self.loc_par_dict = loc_par_dict
        self.site_par_dict = site_par_dict

    def set_new_asset_set(self, is_new_asset_set):
        """Updates the set of new asset IDs from all imported assets"""
        self.is_new_asset_set = is_new_asset_set or set()

    def get_unknown_package(self):
        """Gets the Unknown package name from the asset service"""
        if self.asset_service:
            unknown_package = self.asset_service.get_unknown_package()
            return unknown_package.getName() if unknown_package else "Unknown"
        return "Unknown"

    def build_location_elements(self):
        """Build location element mappings"""
        hierarchy_map = {}  # parent_id -> {level: {name: id}}

        # First pass - build hierarchy map
        for loc_id, data in self.existing_locations_dict.items():
            classifier = data.get('base_classifier')
            if not classifier:
                continue

            parent_id = data.get('parent_composite_element')
            if parent_id not in hierarchy_map:
                hierarchy_map[parent_id] = {}
            if classifier.lower() not in hierarchy_map[parent_id]:
                hierarchy_map[parent_id][classifier.lower()] = {}

            hierarchy_map[parent_id][classifier.lower()][data['location_name']] = loc_id

        # Cache part property check
        part_property = "PartProperty"

        # Second pass - map elements
        for loc_id, data in self.existing_locations_dict.items():
            classifier = data.get('base_classifier', '').lower()
            if not classifier:
                continue

            # Get element
            element = f.byQualifiedName().find(self.project, data['qualified_name'])
            if not element:
                continue

            # Get children at this level
            children = hierarchy_map.get(loc_id, {})

            # Map part properties to children
            for part in element.getPart():
                if any(st.getName() == part_property for st in part.getAppliedStereotype()):
                    part_name = part.getName()
                    # Check each level for matching child
                    for level, names in children.items():
                        if part_name in names:
                            self.location_elements[level][names[part_name]] = part
                            break

        # Return for convenience
        return self.location_elements

    #_____CACHE MANAGEMENT METHODS_________________#
    def clear_system_path_caches(self):
        """Clears all system path build related caches"""
        self._system_path_build_cache.clear()
        self._diversion_point_cache.clear()
        
    def clear_all_caches(self):
        """Clears all caches including new optimization caches"""
        self.clear_system_path_caches()
        self._path_name_cache.clear()
        self._connector_cache.clear()
        self._system_element_cache.clear()
        self._asset_element_cache.clear()
        self._asset_property_cache.clear()
        self._asset_ports_cache.clear()

    #_____MAIN: BUILD STRUCTURE FOR CONNECTION_________________#
    def process_connections(self, connections_data):
        # Find existing connections
        existing_connectors = self.find_existing_connections()
        import_connections_dissembled = []

        # Return the source/target info for the created element in the model
        for connection_id, connection in connections_data.items():
            source_info = self.get_endpoint_info(connection, 'source')
            target_info = self.get_endpoint_info(connection, 'target')

            if not source_info or not target_info:
                continue

            # Then get context with paths
            context_info, system_context_info = self.determine_connection_context(source_info, target_info)

            source_connectors = self.get_source_or_target_connectors(connection.get('source_connectors'), connection)
            target_connectors = self.get_source_or_target_connectors(connection.get('target_connectors'), connection)
            cable_element = self.get_cable_info(connection)
            if context_info:
                self.update_endpoint_path(source_info, target_info, context_info)
                self.connector_decomposer(import_connections_dissembled, source_info, target_info,
                                          source_connectors,
                                          target_connectors,
                                          cable_element, context_info)

            # Add system connections
            if system_context_info:
                self.connector_decomposer_system(import_connections_dissembled,
                                                system_context_info,
                                                source_info,
                                                target_info,
                                                cable_element)

        existing_to_remove, new_to_create = self.compare_connection_sets(
            self.sort_elements(existing_connectors),
            self.sort_elements(import_connections_dissembled)
        )

        return new_to_create, existing_to_remove

    #-----------# HELPER FUNCTIONS--DECOMPOSE EACH CONNECTION #
    def get_endpoint_info(self, connection, endpoint_type):
        """Gets complete endpoint information for connection endpoint"""

        try:
            endpoint = connection[endpoint_type]
            asset_id = endpoint['id']
            asset_name = self.get_asset_property_cached(asset_id, 'qualified_name')
            parent_id = self.get_asset_property_cached(asset_id, 'parent_composite_element')

            # Look up element in relevant dictionary based on parent
            element = None
            if parent_id in self.as_par_dict:
                element = self.as_par_dict[parent_id].get(asset_id)
            elif parent_id in self.loc_par_dict:
                element = self.loc_par_dict[parent_id].get(asset_id)

            # Get system element reference
            element_sys = None
            owning_package = None
            
            # Check if asset is in the new asset set (not in Unknown packages)
            if asset_id in self.is_new_asset_set:
                
                # Case 1: Try direct lookup with owning_package
                owning_package = self.get_asset_property_cached(asset_id, 'owning_package')
                element_sys = self.get_system_element_cached(owning_package, asset_id)
                
                if element_sys:
                    element_p_sys_el = element_sys.getOwner()
                else:
                    # Case 2: Asset not found immediately - traverse ancestry to find system parent
                    element_sys = element
                    if element_sys:
                        highest_ancestor_id = ancestry_tracer.get_highest_ancestor(asset_id)
                        highest_ancestor_owning_package = self.get_asset_property_cached(highest_ancestor_id, 'owning_package')
                        highest_ancestor_element = self.get_system_element_cached(highest_ancestor_owning_package, highest_ancestor_id)
                        if highest_ancestor_element:
                            element_p_sys_el = highest_ancestor_element.getOwner()
                        else:
                            pass
            else:
                # Case 3: Asset is in Unknown package - handle differently
                element_sys = element
                owning_package = self.get_unknown_package()
                element_p_sys_el = owning_package

            # Look up port directly from asset
            port = None
            port_name = connection[endpoint_type + '_port']['name']
            
            # Check if we've already cached all ports for this asset
            if asset_name in self._asset_ports_cache:
                port = self._asset_ports_cache[asset_name].get(port_name)
            else:
                # First time seeing this asset - cache all its ports
                asset_element = self.get_asset_element_cached(asset_name)
                if asset_element:
                    asset_ports = {}
                    for p in asset_element.getOwnedPort():
                        asset_ports[p.getName()] = p
                    self._asset_ports_cache[asset_name] = asset_ports
                    port = asset_ports.get(port_name)
                else:
                    # Asset element not found - cache empty dict to avoid repeated lookups
                    self._asset_ports_cache[asset_name] = {}

            # Build concatenated name with port if present. Will be update after finding connection context
            if port_name:
                asset_concat = "{0}.{1}".format(
                    endpoint['name'],
                    port_name
                )
            else:
                asset_concat = "{0}_{1}".format(endpoint['name'], asset_id)

            return {
                'element': element,
                'element_id': asset_id,
                'element_name' : asset_name,
                'element_sys': element_sys,
                'port': port,
                'element_p_el': parent_id,
                'element_p_pkg': owning_package,
                'element_p_sys_el': element_p_sys_el,
                'asset_concat': asset_concat
            }

        except Exception as e:
            printer("Error getting endpoint info for {0}: {1}".format(endpoint_type, str(e)))
            return None

    def determine_connection_context(self, source_info, target_info):
        """Determines all applicable connection contexts and types """
        contexts = []
        system_contexts = []

        # Context 1: System-level connections for assets in same system
        unknown_pkg_name = self.get_unknown_package()
        if source_info['element_p_pkg'] != unknown_pkg_name and target_info['element_p_pkg'] != unknown_pkg_name:
            source_system = source_info['element_p_sys_el']
            target_system = target_info['element_p_sys_el']

            if source_system == target_system:
                context_data = {
                    'ConnectionContextEle': source_system,
                    'c_tag_2': "Same System"
                }

                source_highest_ancestor = ancestry_tracer.get_highest_ancestor(source_info['element_id'])
                target_highest_ancestor = ancestry_tracer.get_highest_ancestor(target_info['element_id'])
                
                # Store highest ancestors directly for easier access
                context_data['source_highest_ancestor'] = source_highest_ancestor if source_highest_ancestor else source_info['element_id']
                context_data['target_highest_ancestor'] = target_highest_ancestor if target_highest_ancestor else target_info['element_id']
                contexts.append(context_data)

            # Context 2: Different systems need multiple contexts:
            # - Source system for intra connections
            # - Target system for intra connections
            # - Site block for between systems
            # - Site block for FID Level 1
            else:
                # Only add system contexts
                if source_system:
                    context_data = {
                        'ConnectionContextEle': source_system,
                        'c_tag_2': "Intra Source System"
                    }
                    # Add path info if available
                    source_highest_ancestor = ancestry_tracer.get_highest_ancestor(source_info['element_id'])
                    if source_highest_ancestor:
                        context_data['highest_ancestor'] = source_highest_ancestor
                    system_contexts.append(context_data)
                    
                if target_system:
                    context_data = {
                        'ConnectionContextEle': target_system,
                        'c_tag_2': "Intra Target System"
                    }
                    # Add path info if available
                    target_highest_ancestor = ancestry_tracer.get_highest_ancestor(target_info['element_id'])
                    if target_highest_ancestor:
                        context_data['highest_ancestor'] = target_highest_ancestor
                    system_contexts.append(context_data)
                if self.site_block:
                    contexts.append({
                        'ConnectionContextEle': self.site_block,
                        'c_tag_2': "Between Systems"
                    })

        # Context 3: Known system to Unknown system connections
        elif source_info['element_p_pkg'] != "Unknown" and target_info['element_p_pkg'] == "Unknown":
            context_data = {
                'ConnectionContextEle': source_info['element_p_sys_el'],
                'c_tag_2': "Knw + Unk"
            }
            # Add path info if available
            source_highest_ancestor = ancestry_tracer.get_highest_ancestor(source_info['element_id'])
            if source_highest_ancestor:
                context_data['highest_ancestor'] = source_highest_ancestor
            system_contexts.append(context_data)

        # Context 4: Unknown system to Known system connections
        elif source_info['element_p_pkg'] == "Unknown" and target_info['element_p_pkg'] != "Unknown":
            context_data = {
                'ConnectionContextEle': target_info['element_p_sys_el'],
                'c_tag_2': "Unk + Knw"
            }
            # Add path info if available
            target_highest_ancestor = ancestry_tracer.get_highest_ancestor(target_info['element_id'])
            if target_highest_ancestor:
                context_data['highest_ancestor'] = target_highest_ancestor
            system_contexts.append(context_data)

        # Context 5: Physical connections with same parent asset
        if source_info['element_p_el'] == target_info['element_p_el']:
            parentasset = source_info['element_p_el']
            parent_element = None

            if parentasset in self.existing_assets_dict:
                parent_element = self.get_asset_element_cached(
                    self.get_asset_property_cached(parentasset, 'qualified_name'))
            elif parentasset in self.existing_locations_dict:
                parent_element = self.get_asset_element_cached(
                    self.existing_locations_dict[parentasset]['qualified_name'])

            if parent_element:
                # Add path info for same parent
                path_info = {
                    'common_ancestor' : parentasset,
                    'path_to_el1': [parentasset, source_info['element_id']],
                    'path_to_el2': [parentasset, target_info['element_id']]
                }

                contexts.append({
                    'ConnectionContextEle': parent_element,
                    'c_tag_2': "Same Parent Element",
                    'path_info': path_info
                })

            return contexts, system_contexts

        # Context 6: Physical connections with different parent assets
        common_ancestor = self.find_me_a_match(
            source_info['element_p_el'],
            target_info['element_p_el'],
            source_info['element_id'],
            target_info['element_id']
        )

        if common_ancestor:
            try:
                qualified_name = self.get_asset_property_cached(common_ancestor, 'qualified_name')
                context_element = self.get_asset_element_cached(qualified_name)
            except KeyError:
                context_element = self.get_asset_element_cached(
                    self.existing_locations_dict[common_ancestor]['qualified_name']
                )

            cache_key = tuple(sorted([
                (source_info['element_p_el'], source_info['element_id']),
                (target_info['element_p_el'], target_info['element_id'])
            ]))

            contexts.append({
                'ConnectionContextEle': context_element,
                'c_tag_2': "Different Parent Element",
                'path_info': self._path_cache[cache_key] if cache_key in self._path_cache else None
            })

        system_contexts =  [ctx for ctx in system_contexts
                            if ctx.get('c_tag_2') and ctx.get('ConnectionContextEle')]

        return contexts, system_contexts

    def update_endpoint_path(self, source_info, target_info, contexts):

        physical_context = next((ctx for ctx in contexts
                                 if ctx.get('c_tag_2') in ["Different Parent Element", "Same Parent Element"]
                                 and ctx.get('path_info')), None)

        if physical_context:
            path_info = physical_context['path_info']

            # Process source path
            source_key = tuple(path_info['path_to_el1'])
            if source_key not in self._path_name_cache:
                source_path, source_names = self._process_path(path_info['path_to_el1'])
                self._path_name_cache[source_key] = (source_path, source_names)

            # Process target path
            target_key = tuple(path_info['path_to_el2'])
            if target_key not in self._path_name_cache:
                target_path, target_names = self._process_path(path_info['path_to_el2'])
                self._path_name_cache[target_key] = (target_path, target_names)

            source_path, source_names = self._path_name_cache[source_key]
            target_path, target_names = self._path_name_cache[target_key]

            source_info['nested_Source'] = tuple(source_path) if len(source_path) > 1 else source_path[0]
            target_info['nested_Target'] = tuple(target_path) if len(target_path) > 1 else target_path[0]

            source_info['asset_concat'] = "{0}.{1}".format(
                ".".join(source_names),
                source_info['port'].getName() if source_info['port'] else ""
            )

            target_info['asset_concat'] = "{0}.{1}".format(
                ".".join(target_names),
                target_info['port'].getName() if target_info['port'] else ""
            )

    def get_source_or_target_connectors(self, connectors, connection):
        """Process connector elements in connection"""
        if not connectors:
            return None

        processed = []
        for connector in connectors:
            connector_id = connector['id']
            processed.append({
                'faux_name': connector['name'] if not connection.get('cable') or not connection['cable'].get('id') else "{0}-{1}".format(
                    connector['name'],
                    connection['cable']['name'].split("-Cable_")[0]
                ),
                'con_id': connector_id,
                'con_el': self.get_asset_element_cached(self.get_asset_property_cached(connector_id, 'qualified_name'))
            })
        return processed

    def get_cable_info(self, connection):
        """Extract cable element information if present"""
        try:
            if not connection.get('cable') or connection['cable'].get('id') is None:
                return None

            cable_id = connection['cable']['id']
            cable_data = self.existing_assets_dict[cable_id]

            return {
                'cable_element': self.get_asset_element_cached(cable_data['qualified_name']),
                'cable_name': "{0}".format(cable_data.get("asset_name")),
                'cable_id': cable_id
            }

        except Exception as e:
            printer("Error getting cable info: {0}".format(str(e)))
            return None

    #_____MAIN: BRINGING ALL COMPONENTS TOGETHER_________________#
    def connector_decomposer(self, import_connections, source_info, target_info,
                             source_connectors, target_connectors, cable_info, contexts):

        """Decomposes a complex connection into individual segments based on connection type and context.

        Args:
            import_connections: List to store decomposed connections
            source_concat: Source element name with port
            target_concat: Target element name with port
            source_connectors: List of source connector data
            target_connectors: List of target connector data
            cable_info: Cable element info dictionary or None
            context_info: Dictionary containing connection context and type
        """
        source_concat = source_info.get('asset_concat')
        target_concat = target_info.get('asset_concat')

        # Helper Function to add decomposed connection entry
        def add_connection(connected_elements, c_tag, con_el=None):
            if context['c_tag_2'] == "Between Systems":
                if c_tag in ["s_t_sc", "s_t_c", "s_t_tc"]:
                    connected_elements[0] = "{0}.{1}".format(
                        source_info['element_p_sys_el'].getName(),
                        source_info['port'].getName() if source_info['port'] else ""
                    )
                elif c_tag in ["c_t_t", "tc_t_t", "sc_t_t"]:
                    connected_elements[1] = "{0}.{1}".format(
                        target_info['element_p_sys_el'].getName(),
                        target_info['port'].getName() if target_info['port'] else ""
                    )
            base_data = {
                'connected_elements': connected_elements,
                'connection_type': connection_type,
                'con_p_d': {
                    'c_tag': c_tag,
                    'ConnectionContextEle': context['ConnectionContextEle'],
                    'c_tag_2': context['c_tag_2'],
                    'con_el': con_el,
                    'cable_el': cable_info['cable_element'] if cable_info else None,
                    'sourcePort': source_info['port'],
                    'targetPort': target_info['port'],
                    'source_sys_Element_parent': source_info['element_p_sys_el'],
                    'target_sys_Element_parent': target_info['element_p_sys_el']
                }
            }

            # Add logical connection specific data
            if connection_type == "Logical":
                # Get physical context for path mapping if available
                physical_context = None
                for ctx in contexts:
                    if ctx.get('c_tag_2') in ["Different Parent Element", "Same Parent Element"] and ctx.get('path_info'):
                        physical_context = ctx['path_info']
                        break

                # Get system elements using path information
                if context.get('c_tag_2') == "Same System":
                    # For same system, use separate highest ancestors for source and target
                    source_highest_ancestor_info = {'highest_ancestor': context.get('source_highest_ancestor')} if context.get('source_highest_ancestor') else None
                    target_highest_ancestor_info = {'highest_ancestor': context.get('target_highest_ancestor')} if context.get('target_highest_ancestor') else None

                    source_sys_element = self._get_system_element_from_path(source_info, source_highest_ancestor_info, physical_context, is_source_element=True)
                    target_sys_element = self._get_system_element_from_path(target_info, target_highest_ancestor_info, physical_context, is_source_element=False)
                else:
                    # For other cases, use the single highest_ancestor
                    highest_ancestor_info = {'highest_ancestor': context.get('highest_ancestor')} if context.get('highest_ancestor') else None
                    source_sys_element = self._get_system_element_from_path(source_info, highest_ancestor_info, physical_context, is_source_element=True)
                    target_sys_element = self._get_system_element_from_path(target_info, highest_ancestor_info, physical_context, is_source_element=False)
                    
                base_data['con_p_d'].update({
                    'source_sys_Element': source_sys_element,
                    'target_sys_Element': target_sys_element
                })
                
            # Add physical connection specific data
            elif connection_type == "Physical":
                base_data['con_p_d'].update({
                    'nestedSource': source_info.get('nested_Source'),
                    'nestedTarget': target_info.get('nested_Target'),
                    'source' : source_info['element'],
                    'target' : target_info['element']
                })

            import_connections.append(base_data)

        # Get connection type based on context element's package
        for context in contexts:
            if self.get_top_package(context['ConnectionContextEle']).getName() in self.logical_packages:
                connection_type = "Logical"
            else:
                connection_type = "Physical"

            # Scenario 1: Only Cable connection
            if cable_info and not source_connectors and not target_connectors:
                add_connection([source_concat, cable_info['cable_name']], "s_t_c")
                add_connection([cable_info['cable_name'], target_concat], "c_t_t")

            # Scenario 2: Direct connection
            elif not cable_info and not source_connectors and not target_connectors:
                add_connection([source_concat, target_concat], "s_t_t")

            # Scenario 3: Cable with source connectors
            elif cable_info and source_connectors and not target_connectors:
                if len(source_connectors) == 1:
                    connector = source_connectors[0]
                    add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                    add_connection([connector['faux_name'], cable_info['cable_name']], "sc_t_c", connector['con_el'])
                else:
                    for i, connector in enumerate(source_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                        elif i == len(source_connectors) - 1:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], cable_info['cable_name']], "sc_t_c", connector['con_el'])
                        else:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                add_connection([cable_info['cable_name'], target_concat], "c_t_t")

            # Scenario 4: Cable with target connectors
            elif cable_info and not source_connectors and target_connectors:
                if len(target_connectors) == 1:
                    connector = target_connectors[0]
                    add_connection([cable_info['cable_name'], connector['faux_name']], "c_t_tc", connector['con_el'])
                    add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                else:
                    for i, connector in enumerate(target_connectors):
                        if i == 0:
                            add_connection([cable_info['cable_name'], connector['faux_name']], "c_t_tc", connector['con_el'])
                        elif i == len(target_connectors) - 1:
                            add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                        else:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                add_connection([source_concat, cable_info['cable_name']], "s_t_c")

            # Scenarion 5: Source connectors only
            elif not cable_info and source_connectors and not target_connectors:
                if len(source_connectors) == 1:
                    connector = source_connectors[0]
                    add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                    add_connection([connector['faux_name'], target_concat], "sc_t_t", connector['con_el'])
                else:
                    for i, connector in enumerate(source_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                        elif i == len(source_connectors) -1:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], target_concat], "sc_t_t", connector['con_el'])
                        else:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})

            # Scenarion 6: Target connectors only
            elif not cable_info and not source_connectors and target_connectors:
                if len(target_connectors) == 1:
                    connector = target_connectors[0]
                    add_connection([source_concat, connector['faux_name']], "s_t_tc", connector['con_el'])
                    add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                else:
                    for i, connector in enumerate(target_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_tc", connector['con_el'])
                        elif i == len(target_connectors) - 1:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                        else:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})

            # Scenario 7: Cable with both connectors
            elif cable_info and source_connectors and target_connectors:
                if len(source_connectors) == 1 and len(target_connectors) == 1:
                    s_connector = source_connectors[0]
                    t_connector = target_connectors[0]
                    add_connection([source_concat, s_connector['faux_name']], "s_t_sc", s_connector['con_el'])
                    add_connection([s_connector['faux_name'], cable_info['cable_name']], "sc_t_c", s_connector['con_el'])
                    add_connection([cable_info['cable_name'], t_connector['faux_name']], "c_t_tc", t_connector['con_el'])
                    add_connection([t_connector['faux_name'], target_concat], "tc_t_t", t_connector['con_el'])
                elif len(source_connectors) == 1 and len(target_connectors) != 1:
                    s_connector = source_connectors[0]
                    add_connection([source_concat, s_connector['faux_name']], "s_t_sc", s_connector['con_el'])
                    add_connection([s_connector['faux_name'], cable_info['cable_name']], "sc_t_c", s_connector['con_el'])
                    for i, connector in enumerate(target_connectors):
                        if i == 0:
                            add_connection([cable_info['cable_name'], connector['faux_name']], "c_t_tc", connector['con_el'])
                        elif i == len(target_connectors) - 1:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                        else:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                elif len(source_connectors) != 1 and len(target_connectors) == 1:
                    t_connector = target_connectors[0]
                    add_connection([cable_info['cable_name'], t_connector['faux_name']], "c_t_tc", t_connector['con_el'])
                    add_connection([t_connector['faux_name'], target_concat], "tc_t_t", t_connector['con_el'])
                    for i, connector in enumerate(source_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                        elif i == len(source_connectors) - 1:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], cable_info['cable_name']], "sc_t_c", connector['con_el'])
                        else:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                else:
                    for i, connector in enumerate(source_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                        elif i == len(source_connectors) - 1:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], cable_info['cable_name']], "sc_t_c", connector['con_el'])
                        else:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                    for i, connector in enumerate(target_connectors):
                        if i == 0:
                            add_connection([cable_info['cable_name'], connector['faux_name']], "c_t_tc", connector['con_el'])
                        elif i == len(target_connectors) - 1:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                        else:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})

            # Scenario 8: Both connectors without cable
            elif not cable_info and source_connectors and target_connectors:
                if len(source_connectors) == 1 and len(target_connectors) == 1:
                    s_connector = source_connectors[0]
                    t_connector = target_connectors[0]
                    add_connection([source_concat, s_connector['faux_name']], "s_t_sc", s_connector['con_el'])
                    add_connection([s_connector['faux_name'], t_connector['faux_name']], "sc_t_tc",
                                       {'s_con_el': s_connector['con_el'], 't_con_el': t_connector['con_el']})
                    add_connection([t_connector['faux_name'], target_concat], "tc_t_t", t_connector['con_el'])
                elif len(source_connectors) == 1 and len(target_connectors) != 1:
                    s_connector = source_connectors[0]
                    add_connection([source_concat, s_connector['faux_name']], "s_t_sc", s_connector['con_el'])
                    for i, connector in enumerate(target_connectors):
                        if i == 0:
                            add_connection([s_connector['faux_name'], connector['faux_name']], "sc_t_tc",
                                            {'s_con_el': s_connector['con_el'], 't_con_el': connector['con_el']})
                        elif i == len(target_connectors) - 1:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                        else:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                elif len(source_connectors) != 1 and len(target_connectors) == 1:
                    t_connector = target_connectors[0]
                    add_connection([t_connector['faux_name'], target_concat], "tc_t_t", t_connector['con_el'])
                    for i, connector in enumerate(source_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                        elif i == len(source_connectors) - 1:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], t_connector['faux_name']], "sc_t_tc",
                                       {'s_con_el': connector['con_el'], 't_con_el': t_connector['con_el']})
                        else:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                else:
                    for i, connector in enumerate(source_connectors):
                        if i == 0:
                            add_connection([source_concat, connector['faux_name']], "s_t_sc", connector['con_el'])
                        else:
                            add_connection([source_connectors[i-1]['faux_name'], connector['faux_name']],"sc_t_sc",
                                           {'s_con_el': source_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                    for i, connector in enumerate(target_connectors):
                        if i == 0:
                            add_connection([source_connectors[-1]['faux_name'], connector['faux_name']], "sc_t_tc",
                                       {'s_con_el': source_connectors[-1]['con_el'], 't_con_el': connector['con_el']})
                        elif i == len(target_connectors) - 1:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})
                            add_connection([connector['faux_name'], target_concat], "tc_t_t", connector['con_el'])
                        else:
                            add_connection([target_connectors[i-1]['faux_name'], connector['faux_name']], "tc_t_tc",
                                           {'s_con_el': target_connectors[i-1]['con_el'], 't_con_el': connector['con_el']})

    def connector_decomposer_system(self, import_connections_dissembled, system_context_info,
                                   source_info, target_info, cable_element):
        """Process system-level connections including intra-system and FID level connections

        Args:
            import_connections_dissembled: List to store decomposed connections
            system_context_info: List of system context dictionaries
            source_info: Dictionary with source element information
            target_info: Dictionary with target element information
            cable_element: Dictionary with cable element information if present
        """
        system_name = source_info['element_p_pkg']
        for context in system_context_info:
            connection_type = "Physical" if context['c_tag_2'] == "FID L 1" else "Logical"
            
            # Get physical context for path mapping if available
            cache_key = tuple(sorted([
                (source_info['element_p_el'], source_info['element_id']), 
                (target_info['element_p_el'], target_info['element_id'])
            ]))
            physical_context = self._path_cache.get(cache_key) if hasattr(self, '_path_cache') else None

            # Calculate system elements using the same logic as main connector_decomposer
            highest_ancestor_info = {'highest_ancestor': context.get('highest_ancestor')} if context.get('highest_ancestor') else None
            source_sys_element = self._get_system_element_from_path(source_info, highest_ancestor_info, physical_context, is_source_element=True)
            target_sys_element = self._get_system_element_from_path(target_info, highest_ancestor_info, physical_context, is_source_element=False)

            base_data = {
                'connection_type': connection_type,
                'con_p_d': {
                    'c_tag': "s_t_t",
                    'ConnectionContextEle': context['ConnectionContextEle'],
                    'c_tag_2': context['c_tag_2'],
                    'cable_el': cable_element['cable_element'] if cable_element else None,
                    'sourcePort': source_info['port'],
                    'targetPort': target_info['port'],
                    'source_sys_Element': source_sys_element,
                    'target_sys_Element': target_sys_element,
                    'source_sys_Element_parent': source_info['element_p_sys_el'],
                    'target_sys_Element_parent': target_info['element_p_sys_el']
                }
            }

            # Helper function to build element name from system element and port
            def build_element_name(sys_element, port):
                if isinstance(sys_element, tuple):
                    element_names = []
                    for elem in sys_element:
                        if hasattr(elem, 'getName'):
                            element_names.append(elem.getName())
                        else:
                            element_names.append(str(elem))
                    element_path = ".".join(element_names)
                else:
                    # Single element
                    if hasattr(sys_element, 'getName'):
                        element_path = sys_element.getName()
                    else:
                        element_path = str(sys_element)
                
                # Combine with port name
                port_name = port.getName() if port else ""
                if port_name:
                    return "{0}.{1}".format(element_path, port_name)
                else:
                    return element_path

            if context['c_tag_2'] == "Intra Source System":
                source_system_element_name = build_element_name(source_sys_element, source_info['port'])
                system_parent_name = "{0}.{1}".format(
                    source_info['element_p_sys_el'].getName(), 
                    source_info['port'].getName() if source_info['port'] else ""
                )
                elements = [source_system_element_name, system_parent_name]
            elif context['c_tag_2'] == "Intra Target System":
                target_system_element_name = build_element_name(target_sys_element, target_info['port'])
                system_parent_name = "{0}.{1}".format(
                    target_info['element_p_sys_el'].getName(), 
                    target_info['port'].getName() if target_info['port'] else ""
                )
                elements = [target_system_element_name, system_parent_name]
            elif context['c_tag_2'] == "Knw + Unk":
                source_system_element_name = build_element_name(source_sys_element, source_info['port'])
                target_port_name = source_info['port'].getName() if source_info['port'] else ""
                elements = [source_system_element_name, target_port_name]
            elif context['c_tag_2'] == "Unk + Knw":
                target_system_element_name = build_element_name(target_sys_element, target_info['port'])
                source_port_name = target_info['port'].getName() if target_info['port'] else ""
                elements = [target_system_element_name, source_port_name]
            else:
                elements = [source_info['element_p_sys_el'].getName(), target_info['element_p_sys_el'].getName()]

            base_data['connected_elements'] = elements
            import_connections_dissembled.append(base_data)

    #-----------# ULTILITIES FUNCTIONS________#
    def get_system_element_cached(self, system_pkg, highest_ancestor):
        """Get system element with caching for performance optimization
        """
        cache_key = (system_pkg, highest_ancestor)
        
        if cache_key in self._system_element_cache:
            return self._system_element_cache[cache_key]
        
        # Perform the lookup
        system_element = None
        if system_pkg in self.sys_par_dict and highest_ancestor in self.sys_par_dict[system_pkg]:
            system_element = self.sys_par_dict[system_pkg][highest_ancestor]
        
        # Cache the result (even if None)
        self._system_element_cache[cache_key] = system_element
        return system_element

    def get_asset_element_cached(self, qualified_name):
        """Get asset element with caching for performance optimization of byQualifiedName().find() calls
        """
        # Handle None or empty qualified names
        if not qualified_name:
            return None
            
        if qualified_name in self._asset_element_cache:
            return self._asset_element_cache[qualified_name]
        
        # Perform the expensive MagicDraw lookup
        asset_element = f.byQualifiedName().find(self.project, qualified_name)
        
        # Cache the result (even if None)
        self._asset_element_cache[qualified_name] = asset_element
        return asset_element

    def get_asset_property_cached(self, asset_id, property_name):
        """Get asset property with caching for performance optimization of dictionary lookups
        """
        # Handle None asset_id or property_name
        if not asset_id or not property_name:
            return None
            
        # Check if asset_id exists in cache
        if asset_id in self._asset_property_cache:
            if property_name in self._asset_property_cache[asset_id]:
                return self._asset_property_cache[asset_id][property_name]
        else:
            # Initialize nested dictionary for this asset_id
            self._asset_property_cache[asset_id] = {}
        
        # Perform the dictionary lookup - throw KeyError if asset_id doesn't exist
        # This preserves the original error handling behavior for fallback to locations_dict
        if asset_id not in self.existing_assets_dict:
            raise KeyError("Asset ID {0} not found in existing_assets_dict".format(asset_id))
        
        property_value = self.existing_assets_dict[asset_id].get(property_name)

        # Cache the result
        self._asset_property_cache[asset_id][property_name] = property_value

        return property_value

    def _get_system_element_from_path(self, element_info, highest_ancestor_info, physical_context_path_info, is_source_element=True):
        """
        Gets system element(s) based on path information, handling nested scenarios.
        
        Args:
            element_info: Dictionary containing element information
            highest_ancestor_info: Dictionary with highest ancestor information  
            physical_context_path_info: Dictionary with path_to_el1 and path_to_el2
            is_source_element: Boolean - True for source elements (use path_to_el1), False for target elements (use path_to_el2)
        """
        # Generate cache key for system path build results
        element_id = element_info['element_id']
        element_p_pkg = element_info['element_p_pkg']
        highest_ancestor = highest_ancestor_info.get('highest_ancestor') if highest_ancestor_info else None
        
        # Create cache key using available information
        cache_key = (
            element_id,
            highest_ancestor,
            element_p_pkg,
            is_source_element,
            # Include nested path info for key uniqueness
            tuple(element_info.get('nested_Source', [])) if is_source_element and isinstance(element_info.get('nested_Source'), tuple) else element_info.get('nested_Source'),
            tuple(element_info.get('nested_Target', [])) if not is_source_element and isinstance(element_info.get('nested_Target'), tuple) else element_info.get('nested_Target')
        )
        
        # Check cache first
        if cache_key in self._system_path_build_cache:
            cached_result = self._system_path_build_cache[cache_key]
            return cached_result

        if not highest_ancestor_info:
            result = element_info['element_sys']
            # Cache the result before returning
            self._system_path_build_cache[cache_key] = result
            return result
            
        # Scenario 1: Not nested - element is its own highest ancestor
        if highest_ancestor_info.get('highest_ancestor') == element_info['element_id']:
            result = element_info['element_sys']
            # Cache the result before returning
            self._system_path_build_cache[cache_key] = result
            return result
        
        # Get physical path directly
        if physical_context_path_info:
            if is_source_element:
                physical_path = physical_context_path_info.get('path_to_el1', [])
            else:
                physical_path = physical_context_path_info.get('path_to_el2', [])
        else:
            physical_path = None
        
        if not physical_path or highest_ancestor not in physical_path:
            return element_info['element_sys']
        
        # Find diversion point in physical path with simplified caching
        diversion_cache_key = (tuple(physical_path), highest_ancestor)
        if diversion_cache_key in self._diversion_point_cache:
            diversion_index = self._diversion_point_cache[diversion_cache_key]
        else:
            diversion_index = physical_path.index(highest_ancestor)
            self._diversion_point_cache[diversion_cache_key] = diversion_index
        
        system_pkg = element_info['element_p_pkg']
        
        # Build optimized result: find and replace the correct element
        if is_source_element:
            nested_tuple = element_info['nested_Source']
        else:
            nested_tuple = element_info['nested_Target']
        
        # Get the system element for the highest ancestor (using cache)
        system_element = self.get_system_element_cached(system_pkg, highest_ancestor)
        if not system_element:
            system_element = element_info['element_sys']
        
        if isinstance(nested_tuple, tuple):
            # Calculate elements to keep based on physical path length from diversion point
            elements_to_keep = len(physical_path) - diversion_index
            if elements_to_keep <= len(nested_tuple):
                result_elements = list(nested_tuple[-elements_to_keep:])
                # Replace the first element (corresponding to highest_ancestor) with system_element
                result_elements[0] = system_element
            else:
                # Special case: elements_to_keep > nested_tuple length (typically when diversion_index == 0)
                # This means we need to add system_element at the beginning and keep all nested_tuple elements
                if diversion_index == 0:
                    result_elements = [system_element] + list(nested_tuple)
                else:
                    # For other cases, use replacement behavior
                    result_elements = list(nested_tuple)
                    result_elements[0] = system_element
            result = tuple(result_elements) if len(result_elements) > 1 else result_elements[0]
        else:
            # Single element case - need to apply same collision logic
            if diversion_index == 0:
                # Special case: collision at index 0 - add system element at beginning, keep physical element
                result_elements = [system_element, nested_tuple]
                result = tuple(result_elements)
            else:
                # Normal case - replace at correct index, but since it's single element, just return system
                result = system_element
        
        # Cache the result before returning
        self._system_path_build_cache[cache_key] = result

        return result

    def get_top_package(self, element):
        """Returns topmost package in element hierarchy"""
        current = element
        while current:
            owner = current.getOwner()
            if not owner or (isinstance(owner, Element) and not owner.getOwner()):
                return current
            current = owner
        return None

    def find_me_a_match(self, element1, element2, source_id, target_id):
        """Returns closest common ancestor between two elements with path caching"""
        orig_order = ((element1, source_id), (element2, target_id))
        cache_key = tuple(sorted(orig_order))

        if not hasattr(self, '_path_cache'):
            self._path_cache = {}

        if cache_key in self._path_cache:
            cached = self._path_cache[cache_key]
            if orig_order[0] != cached['original_order'][0]:
                cached['path_to_el1'], cached['path_to_el2'] = cached['path_to_el2'], cached['path_to_el1']
                cached['original_order'] = orig_order
            return cached['common_ancestor']

        # Get ancestry for both elements (caching handled by consolidated tracer)
        anc1 = self.trace_ancestry(element1)
        anc2 = self.trace_ancestry(element2)

        common_elements = set(anc1) & set(anc2)

        for ancestor in anc1:
            if ancestor in common_elements:
                # Include the actual elements in paths
                path_to_el1 = [source_id] + anc1[0:anc1.index(ancestor)+1]
                path_to_el2 = [target_id] + anc2[0:anc2.index(ancestor)+1]

                self._path_cache[cache_key] = {
                    'common_ancestor': ancestor,
                    'original_order': orig_order,
                    'path_to_el1': list(reversed(path_to_el1)),
                    'path_to_el2': list(reversed(path_to_el2))
                }
                return ancestor

        return None

    def trace_ancestry(self, element):
        """Builds ancestry chain from element to root"""
        from utils.md_utils import ancestry_tracer
        return ancestry_tracer.trace_full_ancestry(
            element, self.existing_assets_dict, self.existing_locations_dict
        )

    def _process_path(self, path_ids):
        path = []
        names = []

        start_id = path_ids[1]
        if start_id in self.existing_locations_dict:
            location_elements = []
            location_names = []
            for path_id in path_ids[1:]:
                if path_id in self.existing_locations_dict:
                    loc_data = self.existing_locations_dict[path_id]
                    classifier = loc_data.get('base_classifier', '').lower()
                    if classifier in self.location_elements:
                        element = self.location_elements[classifier].get(path_id)
                        if element:
                            location_elements.append(element)
                            location_names.append(loc_data['location_name'])

            path.extend(location_elements)
            names.extend(location_names)

        # Process asset path
        for i in range(len(path_ids)-1):
            parent_id = path_ids[i]
            child_id = path_ids[i+1]

            element = (self.loc_par_dict.get(parent_id, {}).get(child_id) or
                       self.as_par_dict.get(parent_id, {}).get(child_id))
            if element:
                path.append(element)
                if child_id in self.existing_assets_dict:
                    names.append(self.get_asset_property_cached(child_id, 'asset_name'))

        return path, names


    def get_connected_elements(self, connector):
        """Gets names of elements connected by connector"""
        connected = []
        for end in connector.getEnd():
            role = end.getRole()
            path_elements = []

            if end.getOwnedElement():
                for element in end.getOwnedElement()[0].getValue():
                    path_elements.append(element.getName())

            if path_elements:
                connected.append("{0}.{1}".format(
                    ".".join(path_elements),
                    role.getName()
                ))
            else:
                connected.append(role.getName())
        return connected

    #_____MAIN: CREATE CONNECTION________________#
    def create_new_connection(self, connection_data):
        """Creates new connection based on connection data"""
        new_con = connection_data['con_p_d']
        context = new_con['ConnectionContextEle']

        source, target, s_port, t_port = self.get_connection_elements(new_con)

        # Check if cable element exists then create unique part_property cable for each connection that needs a cable
        if new_con.get('cable_el') is not None and any('cable' in elem.lower() for elem in connection_data['connected_elements']):
            cable_par_pr = self.find_or_create_cable_par_pr(context, new_con['cable_el'])

        # Cable to target
        if new_con['c_tag'] == 'c_t_t':
            self.create_connection(context, cable_par_pr, target, None, t_port)
        # Cable to target connector
        elif new_con['c_tag'] == 'c_t_tc':
            tc = self.find_or_create_conn_par_pr(context, new_con['con_el'], new_con['cable_el'].getName())
            self.create_connection(context, cable_par_pr, tc, None, None)
        #  Source to target connector
        elif new_con['c_tag'] == 's_t_tc':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            tc = self.find_or_create_conn_par_pr(context, new_con['con_el'], cable_name)
            self.create_connection(context, source, tc, s_port, None)
        # Source to source connector
        elif new_con['c_tag'] == 's_t_sc':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            sc = self.find_or_create_conn_par_pr(context, new_con['con_el'], cable_name)
            self.create_connection(context, source, sc, s_port, None)
        # Target connector to target
        elif new_con['c_tag'] == 'tc_t_t':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            tc = self.find_or_create_conn_par_pr(context, new_con['con_el'], cable_name)
            self.create_connection(context, tc, target, None, t_port)
        # Source Connector to Source Connector
        elif new_con['c_tag'] == 'sc_t_sc':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            sc1 = self.find_or_create_conn_par_pr(context, new_con['con_el']['s_con_el'], cable_name)
            sc2 = self.find_or_create_conn_par_pr(context, new_con['con_el']['t_con_el'], cable_name)
            self.create_connection(context, sc1, sc2, None, None)
        # Target Connector to Target Connector
        elif new_con['c_tag'] == 'tc_t_tc':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            tc1 = self.find_or_create_conn_par_pr(context, new_con['con_el']['s_con_el'], cable_name)
            tc2 = self.find_or_create_conn_par_pr(context, new_con['con_el']['t_con_el'], cable_name)
            self.create_connection(context, tc1, tc2, None, None)
        # Source Connector to Target Connector
        elif new_con['c_tag'] == 'sc_t_tc':
            sc = self.find_or_create_conn_par_pr(context, new_con['con_el']['s_con_el'])
            tc = self.find_or_create_conn_par_pr(context, new_con['con_el']['t_con_el'])
            self.create_connection(context, sc, tc, None, None)
        # Source to cable
        elif new_con['c_tag'] == 's_t_c':
            self.create_connection(context, source, cable_par_pr, s_port, None)
        # Source connector to cable
        elif new_con['c_tag'] == 'sc_t_c':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            sc = self.find_or_create_conn_par_pr(context, new_con['con_el'], cable_name)
            self.create_connection(context, sc, cable_par_pr, None, None)
        # Source connector to target
        elif new_con['c_tag'] == 'sc_t_t':
            cable_name = new_con.get('cable_el', {}).getName() if new_con.get('cable_el') else None
            sc = self.find_or_create_conn_par_pr(context, new_con['con_el'], cable_name)
            self.create_connection(context, sc, target, None, t_port)
        # Direct source to target
        elif new_con['c_tag'] == 's_t_t':
            self.create_connection(context, source, target, s_port, t_port)

    #-----------# HELPER FUNCTIONS--CREATE CONNECTION________________#
    def get_connection_elements(self, new_con):
        """Gets source and target elements based on connection context type"""
        sourceElement = None
        targetElement = None
        sourcePort = new_con.get('sourcePort')
        targetPort = new_con.get('targetPort')

        sourceParent_sys = new_con.get('source_sys_Element_parent')
        targetParent_sys = new_con.get('target_sys_Element_parent')

        # Map system blocks to their part properties in site
        site_parts = {part.getType(): part for part in self.site_par_dict.values()}
        sourceSystemPartProp = site_parts.get(sourceParent_sys)
        targetSystemPartProp = site_parts.get(targetParent_sys)

        if new_con['c_tag_2'] in ["Different Parent Element", "Same Parent Element"]:
            sourceElement = new_con['nestedSource']
            targetElement = new_con['nestedTarget']

        elif new_con['c_tag_2'] == "Unk + Knw":
            targetSystemBlock = targetParent_sys
            sourceElement = self.find_or_create_endpoint_port(targetSystemBlock,targetPort)
            targetElement = new_con['target_sys_Element']
            sourcePort = None

        elif new_con['c_tag_2'] == "Knw + Unk":
            sourceSystemBlock = sourceParent_sys
            sourceElement = new_con['source_sys_Element']
            targetElement = self.find_or_create_endpoint_port(sourceSystemBlock,sourcePort)
            targetPort = None

        elif new_con['c_tag_2'] == "Intra Source System":
            sourceSystemBlock = sourceParent_sys
            targetPort = None
            sourceElement = new_con['source_sys_Element']
            targetElement =self.find_or_create_endpoint_port(sourceSystemBlock,sourcePort)

        elif new_con['c_tag_2'] == "Intra Target System":
            targetSystemBlock = targetParent_sys
            sourceElement = self.find_or_create_endpoint_port(targetSystemBlock,targetPort)
            targetElement = new_con['target_sys_Element']
            sourcePort = None

        elif new_con['c_tag_2'] == "Between Systems":
            sourceSystemBlock = sourceParent_sys
            targetSystemBlock = targetParent_sys
            sourceElement = sourceSystemPartProp
            targetElement = targetSystemPartProp
            sourcePort = self.find_or_create_endpoint_port(sourceSystemBlock,sourcePort)
            targetPort = self.find_or_create_endpoint_port(targetSystemBlock,targetPort)

        elif new_con['c_tag_2'] == "Same System":
            sourceElement = new_con['source_sys_Element']
            targetElement = new_con['target_sys_Element']
        else:
            pass

        return sourceElement, targetElement, sourcePort, targetPort

    def find_or_create_endpoint_port(self, system_element, original_port):
        """
        Create or find FullPort on system element for connection endpoints
        """
        # Check for existing port first
        for port in system_element.getOwnedPort():
            if port.getName() == original_port.getName():
                return port

        return self._create_fullport_with_session(system_element, original_port)
    
    def _create_fullport_with_session(self, system_element, original_port):
        """Create FullPort with proper session management and comprehensive error handling"""
        session_created = False
        
        try:
            # Validate prerequisites
            if not system_element or not original_port:
                raise ValueError("Invalid system element or original port provided")
                
            # Initialize FullPort stereotype if needed
            if not self.fullport_stereotype:
                self.initialize_fullport_stereotype()
            
            # Ensure session is active for model modifications
            if not sm.getInstance().isSessionCreated():
                sm.getInstance().createSession(self.project, "FullPort Creation")
                session_created = True
            
            # Create the FullPort instance
            fullport = self.project.getElementsFactory().createPortInstance()
            fullport.setVisibility(VisibilityKindEnum.getByName("public"))
            fullport.setAggregation(AggregationKindEnum.getByName("composite"))
            fullport.setName(original_port.getName())
            fullport.setOwner(system_element)
            
            # Apply FullPort stereotype
            sh.addStereotype(fullport, self.fullport_stereotype)
            
            # Copy tagged values from original port
            self._copy_tagged_values_safely(fullport, original_port)
            
            # Close session if we created it
            if session_created:
                sm.getInstance().closeSession()

            return fullport
            
        except Exception as e:
            # Cancel session if we created it and there was an error
            if session_created:
                try:
                    sm.getInstance().cancelSession()
                except:
                    pass
                    
            printer("ERROR: FullPort creation failed for port '{0}' on system '{1}': {2}".format(
                original_port.getName() if original_port else "Unknown",
                system_element.getName() if system_element else "Unknown",
                str(e)))
            
            # Fallback: try to return existing port or create basic port
            printer("Attempting fallback port creation...")
            return self._fallback_port_creation(system_element, original_port)
    
    def _copy_tagged_values_safely(self, target_port, source_port):
        """Safely copy tagged values from source port to target port"""
        if not isinstance(target_port, Element) or not source_port:
            return
            
        copied_count = 0
        failed_count = 0
        
        for port_tag in source_port.getTaggedValue():
            try:
                attribute_name = port_tag.tagDefinition.name
                attribute_value = port_tag.getValue()[0] if port_tag.getValue() else None
                stereotype_name = sh.getTagDefinitionOwner(port_tag)
                
                if stereotype_name and attribute_value:
                    sh.setStereotypePropertyValue(target_port, stereotype_name, attribute_name, attribute_value)
                    copied_count += 1
                    
            except Exception as e:
                failed_count += 1
                printer("Warning: Could not copy tagged value '{0}': {1}".format(
                    attribute_name if 'attribute_name' in locals() else "Unknown", str(e)))
    
    def _fallback_port_creation(self, system_element, original_port):
        """Fallback method to create a basic port when FullPort creation fails"""
        try:
            # Try to find existing port again
            for port in system_element.getOwnedPort():
                if port.getName() == original_port.getName():
                    printer("Found existing port as fallback: {0}".format(port.getName()))
                    return port
            
            # Create basic port without stereotype as last resort
            basic_port = self.project.getElementsFactory().createPortInstance()
            basic_port.setVisibility(VisibilityKindEnum.getByName("public"))
            basic_port.setAggregation(AggregationKindEnum.getByName("composite"))
            basic_port.setName(original_port.getName())
            basic_port.setOwner(system_element)
            
            printer("Created basic fallback port: {0}".format(original_port.getName()))
            return basic_port
            
        except Exception as fallback_error:
            printer("ERROR: Even fallback port creation failed: {0}".format(str(fallback_error)))
            raise fallback_error

    def find_or_create_cable_par_pr(self, context_element, cable_element):
        """Finds or creates cable part property in context element"""
        # Check if the cable part property exists
        cable_name = cable_element.getName()
        cable_pp_qn = "{0}::{1}".format(context_element.getQualifiedName(), cable_name)
        cable_pp = self.get_asset_element_cached(cable_pp_qn)

        if not cable_pp:
            # Create relationship if doesn't exist
            self.creator.createCompositeRelationship(context_element, cable_element,
                                                     cable_name, context_element.getOwningPackage())
            cable_pp = self.get_asset_element_cached(cable_pp_qn)

            # Fallback search by iterating parts if QN not found
            if not cable_pp:
                for part in context_element.getPart():
                    if (part.getType() and
                            part.getType().getName() == cable_name):
                        cable_pp = part
                        break
        return cable_pp

    def find_or_create_conn_par_pr(self, context_element, connection_el, cable_name=None):
        """Finds or creates connector part property in context element"""
        conn_asset = connection_el

        if not conn_asset:
            return None

        if cable_name and "-Cable_" in cable_name:
            conn_name = "{0}-{1}".format(
                conn_asset.getName(),
                cable_name.split("-Cable_")[0].rstrip('_')
            )
        else:
            conn_name = conn_asset.getName()

        # Check if part property exists
        for att in context_element.getOwnedAttribute():
            if att.getName() == conn_name:
                return att

        # Create new relationship if not found
        self.creator.createCompositeRelationship(context_element, conn_asset,
                                                 conn_name, context_element.getOwningPackage())

        return self.get_asset_element_cached(
                                        context_element.getQualifiedName() + "::" + conn_name)

    def create_connection(self, context_element, source_element, target_element, source_port=None, target_port=None):
        """Creates a connector between elements/ports in the given context

        Args:
            context_element: Element serving as context block
            source_element: Element at the source end of desired connection
            target_element: Element at the target end of desired connection
            source_port: Source port (optional)
            target_port: Target port (optional)
        """
        # Create connector instance
        connector = self.project.getElementsFactory().createConnectorInstance()

        # Port to Port
        if source_port and target_port:
            mh.setClientElement(connector, source_port)
            mh.setSupplierElement(connector, target_port)

            end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()

            end1 = mh.getFirstEnd(connector)
            sh.addStereotype(end1, end)
            sh.setStereotypePropertyValue(end1, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, source_element)

            end2 = mh.getSecondEnd(connector)
            sh.addStereotype(end2, end)
            sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, target_element)

        # Target has port (source to target port)
        elif target_port and not source_port:
            mh.setClientElement(connector, source_element)
            mh.setSupplierElement(connector, target_port)

            end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
            end2 = mh.getSecondEnd(connector)
            sh.addStereotype(end2, end)
            sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, target_element)

        # Source has port (source port to target)
        elif source_port and not target_port:
            mh.setClientElement(connector, source_port)
            # Handle nested target parts
            if isinstance(target_element, tuple):
                target_el = target_element[-1]
                parent_parts = target_element[:-1]
                mh.setSupplierElement(connector, target_el)

                end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
                end2 = mh.getSecondEnd(connector)
                sh.addStereotype(end2, end)
                sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, parent_parts)
            else:
                mh.setSupplierElement(connector, target_element)

            # Add nested connector end for source port
            end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
            end1 = mh.getFirstEnd(connector)
            sh.addStereotype(end1, end)
            sh.setStereotypePropertyValue(end1, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, source_element)

        # Part to Part
        else:
            mh.setClientElement(connector, source_element)
            mh.setSupplierElement(connector, target_element)

        connector.setOwner(context_element)
        return connector

    #_____MAIN: MAINTAIN/UPDATE CONNECTION________________#
    def find_existing_connections(self):
        """Finds all existing connectors in the model"""
        connectors = []
        for models in self.project.getModels():
            for items in models.getOwnedMember():
                if items.getName() in [PACKAGE_VIEWS["ASSETS"], PACKAGE_VIEWS["LOCATION"], PACKAGE_VIEWS["SYSTEMS"]]:
                    self.find_connections_recursive(items, connectors)
        return connectors

    def find_connections_recursive(self, element, connectors):
        """Recursively finds connections in element hierarchy"""
        for item in element.getOwnedMember():
            if isinstance(item, Package):
                self.find_connections_recursive(item, connectors)
            elif sh.hasStereotype(item, "Block"):
                for subel in item.getOwnedMember():
                    if subel.getHumanType() == "Connector":
                        top_package = self.get_top_package(subel)
                        if top_package.getName() in self.physical_packages:
                            connection_type = "Physical"
                        elif top_package.getName() in self.logical_packages:
                            connection_type = "Logical"
                        else:
                            continue

                        connectors.append({
                            'connector_id': subel.getID(),
                            'connected_elements': self.get_connected_elements(subel),
                            'connection_type': connection_type
                        })
        return connectors

    def sort_elements(self, dict):
        for item in dict:
            item['connected_elements'].sort()
        return dict

    def compare_connection_sets(self, existing, desired):
        def normalize_connection(item):
            elements = sorted(item['connected_elements'])
            # For Between Systems connections, include port info in the key
            if (item.get('con_p_d', {}).get('c_tag_2') == "Between Systems" and
                    item.get('con_p_d', {}).get('sourcePort') and
                    item.get('con_p_d', {}).get('targetPort')):
                return (
                    tuple(elements),
                    item['connection_type'],
                    item['con_p_d']['sourcePort'].getName(),
                    item['con_p_d']['targetPort'].getName()
                )
            # For FID L1 connections, don't need to check for ports
            return (
                tuple(elements),
                item['connection_type']
            )

        existing_set = {normalize_connection(item) for item in existing}
        desired_set = {normalize_connection(item) for item in desired}

        seen = set()
        to_create = []

        for item in desired:
            key = normalize_connection(item)
            if key in (desired_set - existing_set):
                if key not in seen:
                    to_create.append(item)
                    seen.add(key)

        to_remove = [
            item for item in existing
            if normalize_connection(item) in (existing_set - desired_set)
        ]

        return to_remove, to_create