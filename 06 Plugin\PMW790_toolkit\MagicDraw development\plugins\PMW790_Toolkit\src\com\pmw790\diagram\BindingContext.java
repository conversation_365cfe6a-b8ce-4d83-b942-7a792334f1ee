package com.pmw790.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.pmw790.schema.BindingPattern;

import java.util.List;
import java.util.Map;

/**
 * Context class for binding operations to reduce parameter passing
 * This class encapsulates all the parameters needed for binding operations
 */
public class BindingContext {
    // Core elements
    private final BindingPattern pattern;
    private final Class cabinetBlock;
    private final Project project;
    private final Property ownerBlock;
    private final DiagramPresentationElement diagram;
    private final CabinetDiagramContext context;

    // Constraint and property maps
    private final Map<String, Map<String, Property>> constraintPortsMap;
    private final Map<String, Property> valueProperties;

    // Consumer-specific field
    private final boolean isConsumer;

    /**
     * Constructor for constraint-to-property pattern processing
     */
    public BindingContext(
            BindingPattern pattern,
            Class cabinetBlock,
            Project project,
            Property ownerBlock,
            Map<String, Map<String, Property>> constraintPortsMap,
            Map<String, Property> valueProperties,
            DiagramPresentationElement diagram,
            CabinetDiagramContext context,
            boolean isConsumer) {

        this.pattern = pattern;
        this.cabinetBlock = cabinetBlock;
        this.project = project;
        this.ownerBlock = ownerBlock;
        this.constraintPortsMap = constraintPortsMap;
        this.valueProperties = valueProperties;
        this.diagram = diagram;
        this.context = context;
        this.isConsumer = isConsumer;
    }

    // Getters
    public BindingPattern getPattern() {
        return pattern;
    }

    public Class getCabinetBlock() {
        return cabinetBlock;
    }

    public Project getProject() {
        return project;
    }

    public Property getOwnerBlock() {
        return ownerBlock;
    }

    public Map<String, Map<String, Property>> getConstraintPortsMap() {
        return constraintPortsMap;
    }

    public Map<String, Property> getValueProperties() {
        return valueProperties;
    }

    public DiagramPresentationElement getDiagram() {
        return diagram;
    }

    public CabinetDiagramContext getContext() {
        return context;
    }

    public boolean isConsumer() {
        return isConsumer;
    }
}
