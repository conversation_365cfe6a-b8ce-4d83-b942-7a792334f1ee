package com.pmw790.functions;

import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;

import java.util.*;

public class ConnectorUtils {
	
    public static class ConnectorEndInfo {
        public List<Element> connectedElements = new ArrayList<>();
        public Map<Element, Map<String, Object>> elementDict = new HashMap<>();
    }
    
    public static ConnectorEndInfo getConEndInfo(Connector connector) {
    	ConnectorEndInfo allData = new ConnectorEndInfo();
    	
    	for (ConnectorEnd end: connector.getEnd()) {
    		boolean nestedConnector = false;
    		List<Property> chain;
    		List<String> classifiers = new ArrayList<>();
    		List<Element> propPath = new ArrayList<>();
    		Element endIdentifier = null;
    		String fullName = "";
    		
    		for (Stereotype st : StereotypesHelper.getStereotypes(end)) {
    			if ("NestedConnectorEnd".equals(st.getName())) {
    				chain = (List<Property>) StereotypesHelper.getStereotypePropertyValue(end, st, "propertyPath");
    				if (!end.getRole().getHumanType().toLowerCase().contains("port")) {
    					chain.add((Property) end.getRole());
    				}
    				
    				Element last = chain.get(chain.size()-1).getType();
    				classifiers = ToolkitUtilities.findGeneralizationClasses(last);
    				
    				allData.connectedElements.add(chain.get(chain.size()-1));
    				endIdentifier = chain.get(chain.size() - 1);
    				propPath.addAll(chain);
    				nestedConnector = true;
    				StringBuilder nameBuilder = new StringBuilder();
    				for (Property p : chain) {
    					if (nameBuilder.length()>0) nameBuilder.append(".");
    					nameBuilder.append(p.getName());
    				}
    				fullName = nameBuilder.toString();
    			}
    		}
    		
    		if(!nestedConnector) {
    			Property role = (Property) end.getRole();
    			propPath.add(role);
    			allData.connectedElements.add(role);
    			endIdentifier = role;
    			classifiers = ToolkitUtilities.findGeneralizationClasses(role.getType());
    			fullName = role.getName();
    		}
    		
    		Map<String, Object> endInfo = new HashMap<>();
    		endInfo.put("prop_path", propPath);
    		endInfo.put("classifiers", classifiers);
    		endInfo.put("full_name", fullName);
    		
    		allData.elementDict.put(endIdentifier, endInfo);
    		
    	}
    	
    	return allData;
    }
    
    public static List<Map<String, Object>> findNestedConnectors(Element element, List<Map<String, Object>> connectors, boolean nested, Set<String> seenIds){
    	if (seenIds == null) {
    		seenIds = new HashSet<>();
    	}
    	
    	for (Element subel : element.getOwnedElement()) {
    		if ("Connector".equals(subel.getHumanType()) && subel instanceof Connector) {
    			String connId = subel.getID();
    			if (!seenIds.contains(connId)) {
    				seenIds.add(connId);
    				NamedElement subel_n = (NamedElement) subel;
    				Map<String, Object> connectorData = new HashMap<>();
    				connectorData.put("connector_id", connId);
    				connectorData.put("connection_name", subel_n.getName() != null ? subel_n.getName() : null);
    				connectorData.put("connection_info", getConEndInfo((Connector) subel));
    				
    				connectors.add(connectorData);
    			}
    		}
    		
    		if (nested && "Part Property".equals(subel.getHumanType()) && subel instanceof Property) {
    			Type type = ((Property) subel).getType();
    			if (type instanceof Classifier) {
    				findNestedConnectors((Classifier) type, connectors, true, seenIds);
    			}
    		}
    	}
    	
    	return connectors;
    }
}
