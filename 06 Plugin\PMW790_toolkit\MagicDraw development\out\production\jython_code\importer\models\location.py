from core.imports import *
from core.constants import PACKAGE_VIEWS
from utils.md_utils import printer

class LocationService:
    """
    Manages system blocks in Systems View
    """
    def __init__(self, project, package_service, creator_service, locations_dict):
        self.project = project
        self.creator = creator_service
        self.existing_locations_dict = locations_dict
        self.location_view = PACKAGE_VIEWS["LOCATION"]
        self.location_package = f.byQualifiedName().find(self.project, self.location_view)

        # Add these collections
        self.location_blocks_list = []
        self.location_elements = {}
        self.relationships_to_create = []
        self.site_block = None

    def get_location_block(self, location_id):
        """Find existing location block by ID

        Args:
            location_id: Unique identifier for location
            location_data: Dictionary containing location information
        """
        if location_id in self.existing_locations_dict:
            return f.byQualifiedName().find(self.project,
                                            self.existing_locations_dict[location_id]['qualified_name'])
        return None

    def create_location_block(self, location_id, location_data):
        """Create or update location block with properties"""
        existing_block = self.get_location_block(location_id)
        if existing_block:
            location_data['base_classifier'] = self.existing_locations_dict[location_id].get('base_classifier')
            self.update_existing_location(location_id, location_data)
            return existing_block

        location_type = location_data['type'].capitalize()
        location_name = location_data['name']

        location_block = self.creator.create_IDP_Element(
            location_name,
            location_type,
            self.location_package
        )

        if location_block:
            # Track the new block
            self.location_blocks_list.append(location_block)
            self.location_elements[location_id] = location_block

            # Set properties
            self.creator.create_properties(
                location_block,
                'Generic',
                {'id': location_id}
            )

            # Track site block if applicable
            if location_type == 'Site':
                self.site_block = location_block

            # Queue relationship if has parent
            parent_id = location_data.get('parent', {}).get('id')
            if parent_id:
                self.relationships_to_create.append((parent_id, location_id))
               
        return location_block

    def update_existing_location(self, location_id, location_data):
        location_type = location_data['type'].capitalize()
        location_name = location_data['name']
        current_location = self.existing_locations_dict.get(location_id, {})
        location_block = self.get_location_block(location_id)

        if location_type == 'Site':
            self.site_block = location_block

        # Update name if changed
        if location_block.getName() != location_name:
            location_block.setName(location_name)

        current_parent = current_location.get('parent_composite_element')
        parent_id = location_data.get('parent', {}).get('id')

        if current_parent != parent_id:
            # Update dictionary entry
            self.existing_locations_dict[location_id] = {
                'parent_composite_element': parent_id,
                'qualified_name': location_block.getQualifiedName(),
                'base_classifier': location_data.get('base_classifier')
            }

            # Remove old relationship if exists
            if current_parent:
                old_rel = current_location.get('composite_relationship')
                if old_rel:
                    mem.getInstance().removeElement(old_rel)

            # Queue new relationship
            if parent_id:
                self.relationships_to_create.append((parent_id, location_id))

        return location_block

    def process_relationships(self):
        """Process all queued location relationships"""
        processed = set()
        created_relationship = []

        for parent_id, child_id in self.relationships_to_create:
            pair_key = (parent_id, child_id)
            if pair_key in processed:
                continue

            parent = self.location_elements.get(parent_id) or self.get_location_block(parent_id)
            child = self.location_elements.get(child_id) or self.get_location_block(child_id)

            if parent and child:
                # Create composite relationship
                new_relationship = self.creator.createCompositeRelationship(
                    parent, child, child.getName(), self.location_package)
                if new_relationship:
                    relationship_tuple = (new_relationship, parent_id, child)
                    created_relationship.append(relationship_tuple)
            processed.add(pair_key)

        self.relationships_to_create = []
        return created_relationship