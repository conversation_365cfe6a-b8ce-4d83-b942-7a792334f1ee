package com.pmw790.configurators;

import com.nomagic.magicdraw.ui.browser.actions.DefaultBrowserAction;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.pmw790.functions.PowerTableManager;

import java.awt.event.ActionEvent;

public class PowerConsumptionAction extends DefaultBrowserAction {
    private Element targetElement;

    public PowerConsumptionAction(Element element) {
        super("CALCULATE_POWER_CONSUMPTION", "Calculate Power Consumption", null, null);
        this.targetElement = element;
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        PowerTableManager.calculateTotalPowerConsumption(targetElement);
    }
}