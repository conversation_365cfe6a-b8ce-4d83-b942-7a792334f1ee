package com.pmw790.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.pmw790.functions.ConnectionRegistry;

import java.util.List;
import java.util.Map;

/**
 * Generic interface for diagram contexts.
 * This interface defines the contract that all diagram contexts (room, cabinet, future types) must implement.
 * It provides a standardized way for utility classes to interact with different context types without tight coupling.
 */
public interface DiagramContext {
    
    // Core context information
    
    /**
     * Gets the MagicDraw project
     * @return The project
     */
    Project getProject();
    
    /**
     * Gets the connection registry
     * @return The connection registry
     */
    ConnectionRegistry getConnectionRegistry();
    
    /**
     * Gets the context name (room name, cabinet name, etc.)
     * @return The context name
     */
    String getContextName();
    
    /**
     * Gets the main block element for this context (room block, cabinet block, etc.)
     * @return The block element
     */
    Class getContextBlock();
    
    // Provider and consumer management
    
    /**
     * Gets the list of power providers in this context
     * @return List of provider names
     */
    List<String> getProviders();
    
    /**
     * Gets consumers connected to a specific provider
     * @param providerName The provider name
     * @return List of consumer names
     */
    List<String> getConsumersForProvider(String providerName);
    
    /**
     * Checks if a provider is a top-level provider (has no parent providers)
     * @param providerName The provider name
     * @return true if this is a top-level provider
     */
    boolean isTopProvider(String providerName);
    
    /**
     * Gets child providers for a top provider
     * @param topProviderName The top provider name
     * @return List of child provider names
     */
    List<String> getChildProvidersForTop(String topProviderName);
    
    // Property management
    
    /**
     * Gets a part property by name from this context
     * @param propertyName The property name
     * @return The Property object, or null if not found
     */
    Property getPartProperty(String propertyName);
    
    /**
     * Gets constraint properties for a specific element
     * @param elementName The element name (provider name)
     * @return Map of constraint type to Property object
     */
    Map<String, Property> getConstraintProperties(String elementName);
    
    /**
     * Gets constraint ports for a specific constraint type, of an element
     * @param elementName The element name
     * @param constraintType The constraint type name
     * @return Map of port name to Property object
     */
    Map<String, Property> getConstraintPorts(String elementName, String constraintType);
    
    /**
     * Gets value properties for an element
     * @param elementName The element name
     * @return Map of property name to Property object
     */
    Map<String, Property> getValueProperties(String elementName);
    
    /**
     * Gets external load property for an element
     * @param elementName The element name
     * @return The external load Property, or null if not found
     */
    Property getExternalLoadProperty(String elementName);
    
    // Caching support
    
    /**
     * Caches constraint ports for an element and constraint type
     * @param elementName The element name
     * @param constraintType The constraint type
     * @param ports Map of port name to port property
     */
    void cacheConstraintPorts(String elementName, String constraintType, Map<String, Property> ports);
    
    /**
     * Caches value properties for an element
     * @param elementName The element name
     * @param valueProperties Map of property name to Property object
     */
    void cacheValueProperties(String elementName, Map<String, Property> valueProperties);
    
    // Context type identification (for specialized behavior when needed)
    
    /**
     * Gets the context type identifier
     * @return The context type (e.g., "CABINET", "ROOM")
     */
    String getContextType();
    
    /**
     * Checks if this context is operating within a room context
     * @return true if this is a room context or a cabinet within a room
     */
    boolean isRoomContext();
}
