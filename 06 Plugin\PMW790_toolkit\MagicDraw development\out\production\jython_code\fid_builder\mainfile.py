import sys
import os

import traceback

for mod in list(sys.modules.keys()):
    if mod.startswith("services.") or mod == "services" or mod.startswith("utils."):
        del sys.modules[mod]

def setup_paths(base_dir):
    fid_utils_dir = os.path.join(base_dir, 'fid_utils')
    fid_services_dir = os.path.join(base_dir, 'fid_services')
    fid_core_dir = os.path.join(base_dir, 'fid_core')

    # Add directories to sys.path if not already present
    for directory in [base_dir, fid_utils_dir, fid_services_dir, fid_core_dir]:
        if directory not in sys.path:
            sys.path.insert(0, directory)
    return fid_utils_dir, fid_services_dir, fid_core_dir

# Call setup_paths and get all directories
fid_utils_dir, fid_services_dir, fid_core_dir = setup_paths(base_dir)

from fid_core.imports import *
from fid_utils.md_utils import printer
from fid_services.finders import *
from fid_services.creators import FIDL2Creator, build_FIDL2_diagram

def main():
    project = Application.getInstance().getProject()
    # selectedElement is provided by the Java plugin
    app = FIDL2Creator(project)

    if selectedElement:

        gen_classes = find_generalization_classes(selectedElement)
        classifiers_list = ["Cable", "Connection", "Transceiver", "Connector"]
        nc_list = ["Patch Panel","Chassis"]

        all_pp = set()
        all_part_ps = find_relevant_partproperties(selectedElement, all_pp, classifiers_list, nc_list)

        if "Site" in gen_classes or "System" in gen_classes:
            
            target_els = {}
            all_cons = []
            all_parts = set()
            if "Site" in gen_classes:
                t_cs = find_nested_elements_by_classifer(selectedElement, target_els, "System", True)
                t_cs[selectedElement] = {
                    "gen_classes": gen_classes,
                    "part": selectedElement
                    }
                ss_cons = find_cross_system_ports(selectedElement, classifiers_list, nc_list)

                for sys in t_cs:
                    if "System" in t_cs[sys]["gen_classes"]:
                        data_sys = app.create_fid_l2_main(sys, ss_cons, True, [], set(), False, False)
                        build_FIDL2_diagram(sys, 10, data_sys['initial_elements'], data_sys['display_cons'], data_sys['display_parts'], "System", all_part_ps, systemCabinetMap)

                        all_cons.extend(data_sys['display_cons'])
                        all_parts.update(data_sys['display_parts'])
                        all_parts.add(t_cs[sys]['part'])
                for site in t_cs:
                    if "Site" in t_cs[site]['gen_classes']:
                        data_s = app.create_fid_l2_main(site, ss_cons, False, all_cons, all_parts, True, False, systemCabinetMap)
                        build_FIDL2_diagram(site, 10, data_s['initial_elements'], data_s['display_cons'], data_s['display_parts'], "Site", all_part_ps, systemCabinetMap)

            elif "System" in gen_classes:
                assoc_ob = find_association_owning_block(selectedElement)
                ss_cons = find_cross_system_ports(assoc_ob, classifiers_list, nc_list)
                data_sys_solo = app.create_fid_l2_main(selectedElement, ss_cons, True, all_cons, all_parts, False, False)
                build_FIDL2_diagram(selectedElement, 10, data_sys_solo['initial_elements'], data_sys_solo['display_cons'], data_sys_solo['display_parts'], "System", all_part_ps, systemCabinetMap)

            else:
                printer("Please select a Site or System element to run the FID L2 Nested script.")

if __name__ in ['__builtin__', '__main__']:
    try:
        main()
    except Exception as e:
        printer(traceback.format_exc())