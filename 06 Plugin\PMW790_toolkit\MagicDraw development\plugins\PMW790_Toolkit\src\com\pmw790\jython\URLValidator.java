package com.pmw790.jython;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * Utility class for URL validation and normalization
 */
public final class URLValidator {
    
    /**
     * Normalizes a URL by adding the appropriate protocol if missing
     * @param url The URL to normalize
     * @return The normalized URL
     * @throws IllegalArgumentException if the URL is invalid
     */
    public static String normalizeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL cannot be null or empty");
        }
        
        url = url.trim();
        
        // If no protocol specified, add appropriate one
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            if (url.startsWith("localhost") || url.startsWith("127.0.0.1")) {
                url = "http://" + url;
            } else {
                url = "https://" + url;
            }
        }
        
        return url;
    }
    
    /**
     * Normalizes a URL for GET requests (forces HTTP for HTTPS)
     * @param url The URL to normalize
     * @return The normalized URL with HTTP protocol
     * @throws IllegalArgumentException if the URL is invalid
     */
    public static String normalizeUrlForGet(String url) {
        url = normalizeUrl(url);
        
        // Convert HTTPS to HTTP for GET requests
        if (url.startsWith("https://")) {
            url = url.replace("https://", "http://");
        }
        
        return url;
    }
    
    /**
     * Validates that a URL is properly formatted
     * @param url The URL to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidUrl(String url) {
        try {
            new URL(normalizeUrl(url));
            return true;
        } catch (MalformedURLException | IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Validates and normalizes a URL, throwing an exception if invalid
     * @param url The URL to validate and normalize
     * @return The normalized URL
     * @throws IllegalArgumentException if the URL is invalid
     */
    public static String validateAndNormalize(String url) {
        String normalized = normalizeUrl(url);
        
        try {
            new URL(normalized);
            return normalized;
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("Invalid URL format: " + url, e);
        }
    }
    
    /**
     * Extracts a user-friendly error message for connection issues
     * @param url The URL that failed
     * @param exception The exception that occurred
     * @return A formatted error message
     */
    public static String getConnectionErrorMessage(String url, Exception exception) {
        if (exception instanceof java.net.ConnectException) {
            return "✗ Connection Failed!\n\n" +
                   "Could not connect to: " + url + "\n\n" +
                   "Possible causes:\n" +
                   "• API server is not running\n" +
                   "• Wrong port number\n" +
                   "• Firewall blocking connection\n" +
                   "• URL format incorrect";
        } else {
            return "✗ Connection error: " + exception.getMessage();
        }
    }
    
    // Private constructor to prevent instantiation
    private URLValidator() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}