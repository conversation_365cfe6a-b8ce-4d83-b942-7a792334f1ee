import json, difflib, sys, os, re
from datetime import datetime, date, time
from com.nomagic.magicdraw.core import Application
from com.nomagic.magicdraw.openapi.uml import ModelElementsManager as mem
from com.nomagic.uml2.impl import ElementsFactory as ef
from com.nomagic.magicdraw.uml import Finder as f
from com.nomagic.magicdraw.uml.symbols.shapes import DiagramFrameView as DFV
from com.nomagic.magicdraw.uml2 import Profiles
from com.nomagic.magicdraw.openapi.uml import SessionManager
from com.nomagic.magicdraw.sysml.util import SysMLProfile, MDCustomizationForSysMLProfile
from com.nomagic.magicdraw.sysml.util import SysMLConstants
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import AggregationKindEnum
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import VisibilityKindEnum
from com.nomagic.magicdraw.openapi.uml import SessionManager as sm
from com.nomagic.uml2.ext.jmi.helpers import StereotypesHelper as sh
from com.nomagic.uml2.ext.magicdraw.mdprofiles import Stereotype
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Package
from com.nomagic.text.html import HtmlTextUtils
from com.nomagic.uml2.ext.jmi.helpers import ModelHelper as mh
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Element, Class, Property
from com.nomagic.uml2.ext.magicdraw.compositestructures.mdports import Port
from com.nomagic.magicdraw.openapi.uml import PresentationElementsManager
from com.nomagic.magicdraw.uml.symbols import PresentationElement;

from com.nomagic.magicdraw.properties import ChoiceProperty, PropertyID, PropertyManager;
from com.nomagic.magicdraw.uml.symbols.shapes import ClassifierView;
from com.nomagic.magicdraw.uml.symbols import ConverterToShape;
from com.nomagic.magicdraw.uml.symbols.shapes import StereotypesDisplayModeOwner;

from com.nomagic.uml2.ext.jmi.helpers import ElementImageHelper as eih;
from com.nomagic.uml2.ext.magicdraw.mdprofiles import Image;

from itertools import combinations
from java.util import HashMap

import base64
import binascii

from javax.imageio import ImageIO
from java.net import URL
from java.io import InputStream
from java.io import ByteArrayOutputStream

#_________________________________

def printer(e):
    """Helper function that prints to the MagicDraw GUI Log.

        Parameters
        ----------
        e : AnyType
            Element that will be printed
    """
    printer = Application.getInstance().getGUILog()
    printer.log(str(e))

##simple function to return the display properties of a selected PresentationElement (thing in a digram)
def property_finder(diagram):
    sm.getInstance().createSession(project,'test')
    symbol = diagram.getSelected()
    pe = symbol[0].getElement()

def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')
    
def encode_image_to_hex(image_path):
    with open(image_path, 'rb') as f:
        binary_data = f.read()
    return binascii.hexlify(binary_data)

def get_diagram_holder(project, image_package_name, diagram_holder_name):
    project_models = project.getModels()
    for model in project_models:
        if find_package_by_name_uaf(model, image_package_name, None): 
            image_package_holder = find_package_by_name_uaf(model, image_package_name, None)
    if image_package_holder:
        for items in image_package_holder.getOwnedMember():
            if "Diagram" in items.getHumanType() and items.getName() == diagram_holder_name:
                diagram = items
                return diagram
    return None

def get_image_hex(diagram_holder, image_name):
    app = Application.getInstance()    
    prev_diagram = app.getProject().getActiveDiagram()
    diagram = app.getProject().getDiagram(diagram_holder)
    if diagram:
        diagram.open()

    presEls = diagram.getPresentationElements()

    if prev_diagram:
        prev_diagram.open()

    for pe in presEls:
        if pe.getHumanType() == "Attached File" and image_name.lower() in pe.getElement().getBody():
            imageUrl = pe.getImage().getIcon().getURL()
            stream = imageUrl.openStream()
            buffered_img = ImageIO.read(stream)
            baos = ByteArrayOutputStream()
            ImageIO.write(buffered_img, "png", baos)
            raw_b = baos.toByteArray()
            hex_data = binascii.hexlify(raw_b)
            return hex_data

    return None    
    

def isBlock(element):
    if isinstance(element,Class):
        return sh.hasStereotype(element,"Block")
    return False

def isPartProperty(element):
    if isinstance(element,Property):
        return sh.hasStereotype(element,"PartProperty")
    return False

def update_elements_in_package(package, diagram_holder):
    shape_dict = build_shape_dictionary()
    for element in package.getOwnedMember():
        if isBlock(element):
            shape_name = get_shape_name(element, shape_dict)
            if shape_name:
                image_data = get_image_hex(diagram_holder, shape_name)
                if image_data:
                    n_image = create_image_obj(image_data)
                    eih.setCustomImageInformation(element, n_image)
            for x in element.getOwnedMember():
                if isPartProperty(x):
                    p_shape_name = get_shape_name(x.getType(), shape_dict)
                    if p_shape_name:
                        p_image_data = get_image_hex(diagram_holder, p_shape_name)
                        p_image = create_image_obj(p_image_data)
                        eih.setCustomImageInformation(x, p_image)

def create_image_obj(image_data):
    image = Application.getInstance().getProject().getElementsFactory().createImageInstance()
    image.setContent(image_data)
    return image

def get_shape_name(element, shape_dict):
    name = element.getName().lower()
    if "patch panel" in name:
        shape_name = shape_dict["Patch Panel"]
    elif "router" in name:
        shape_name = shape_dict["Router"]
    elif "switch" in name:
        shape_name = shape_dict["Switch"]
    elif "ctp" in name:
        shape_name = shape_dict["CTP"]
    elif "power" in name:
        shape_name = shape_dict["Power"]
    else:
        shape_name = None
    return shape_name

def get_shape_name_old(element, shape_dict):
    classifiers = find_classifiers(element)
    for c in classifiers:
        if c in shape_dict:
            shape_name = shape_dict[c]
            return shape_name       
    return None

def build_shape_dictionary():
    shape_dict = {}
    #shape_dict["classifier_building"] = "circle"
    shape_dict["Patch Panel"] = "rectangle (vertical)"
    shape_dict["Router"] = "pentagon"
    shape_dict["Switch"] = "octagon"
    shape_dict["CTP"] = "slanted_rhombus"
    shape_dict["Power"] = "square"
    return shape_dict

def find_classifiers(element, visited=None):
    """
    Recursively collect the names of all classifiers (superclasses)
    reachable via Generalization relationships.
    """
    if visited is None:
        visited = set()
    classifiers = []

    for rel in element.get_relationshipOfRelatedElement():
        if rel.getHumanType() == "Generalization":
            for target in rel.getTarget():
                tid = target.getID()
                if tid not in visited:
                    visited.add(tid)
                    # add this immediate classifier
                    classifiers.append(target.getName())
                    # recurse to find *its* classifiers
                    classifiers.extend(find_classifiers(target, visited))

    return classifiers

def find_classifiers_old(element):
    classifiers = []
    gen_rels = []
    for rel in element.get_relationshipOfRelatedElement():
        if rel.getHumanType() == "Generalization":
            gen_rels.append(rel)

    for gr in gen_rels:
        for ex in gr.getTarget():
            classifiers.append(ex.getName())

    return classifiers

def create_package_uaf(package_name, owning_package,project, stereotype = None):
    new_package = project.getElementsFactory().createPackageInstance()
    new_package.setVisibility(VisibilityKindEnum.getByName("public"))
    new_package.setOwner(owning_package)
    new_package.setName(package_name)
    if stereotype:
        sh.addStereotype(new_package, stereotype)
    return new_package

def find_package_uaf(project_or_package,package_name,search_in_model, project,stereotype = None):
    package = None
    if search_in_model:
        for model in project.getModels():
            for packages_ in model.getOwnedMember():
                if isinstance(packages_,Package):
                    if packages_.getName() == package_name:
                        if stereotype is None or stereotype in sh.getStereotypes(packages_):
                            package = packages_
                            return package
                else:
                    package_sub = find_package_by_name_uaf(packages_,package_name,stereotype)
                    if package_sub:
                        return package_sub
    else:
        package = find_package_by_name_uaf(project_or_package,package_name, stereotype)
    return package

def find_package_by_name_uaf(package,package_name, stereotype = None):
    ret_package = None
    if hasattr(package, "getOwnedMember"):
        for packages_ in package.getOwnedMember():
            if isinstance(packages_,Package):
                if packages_.getName() == package_name:
                    if stereotype is None or stereotype in sh.getStereotypes(packages_):
                        package = packages_
                    return package
                else:
                    package_sub = find_package_by_name_uaf(packages_,package_name, stereotype)
                    if package_sub:
                        return package_sub
    return ret_package

def find_or_create_package_uaf(package_name, owning_package, project, find_in_model, stereotype = None):
    package = find_package_uaf(project if find_in_model else owning_package, package_name, find_in_model, project, stereotype)
    return package if package else create_package_uaf(package_name, owning_package, project, stereotype)

def find_stereotype(stereotype, top_package, project):
    for s in sh.getAllStereotypes(project):
        if s.getName() == stereotype and top_package in recursive_owner_array(s):
            return s
        
def recursive_owner_array(element, owner_array=None):
    if owner_array is None:
        owner_array = []
    owner = element.getOwner()
    if hasattr(owner,"getName"):
        owner_array.append(owner.getName())
        recursive_owner_array(owner,owner_array)
    return owner_array   

#Setup variables

def update_element_images():
    project = Application.getInstance().getProject()
    baseModel = project.getPrimaryModel()
    sm.getInstance().createSession(project, 'update_images')
    image_diagram = get_diagram_holder(project, "Image Library","Image Holder")
    assets_package = find_package_uaf(project, "Assets View", True, project, None)
    list_of_packages = [x for x in assets_package.getOwnedMember() if isinstance(x, Package)]
    for package in list_of_packages:
        update_elements_in_package(package, image_diagram)

    sm.getInstance().closeSession()