package com.pmw790.layout;

import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.magicdraw.uml.symbols.paths.PathElement;
import com.nomagic.magicdraw.uml.symbols.shapes.PartView;
import com.nomagic.magicdraw.uml.symbols.shapes.PortView;

import java.awt.Point;
import java.awt.Rectangle;
import java.util.*;

import static com.pmw790.functions.Utilities.Log;

/**
 * Handles conversion between MagicDraw FID presentation elements and ELK graph structures
 * Specialized for Internal Block Diagrams with parts, ports, and connectors
 * Preserves cabinet-based grouping and hierarchical relationships
 */
public class FIDDiagramConverter {

    // Cache for ELK reflection access - loaded dynamically if ELK is available
    private static Class<?> elkNodeClass;
    private static Class<?> elkEdgeClass;
    private static Class<?> elkPortClass;
    private static Class<?> elkGraphUtilClass;
    private static boolean initialized = false;
    private static final Object initLock = new Object();
    
    private Map<PresentationElement, Object> elementToNodeMap;
    private Map<Object, PresentationElement> nodeToElementMap;
    private Map<String, Object> cabinetGroupNodes; // Track cabinet group nodes
    
    /**
     * Lazy initialization of ELK classes
     */
    private static void initializeELKClasses() {
        if (initialized) return;
        
        synchronized (initLock) {
            if (initialized) return;
            
            try {
                // Use custom ELK classloader with fallback to standard loading
                ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
                
                elkNodeClass = elkClassLoader.loadClass("org.eclipse.elk.graph.ElkNode");
                elkEdgeClass = elkClassLoader.loadClass("org.eclipse.elk.graph.ElkEdge");
                elkPortClass = elkClassLoader.loadClass("org.eclipse.elk.graph.ElkPort");
                elkGraphUtilClass = elkClassLoader.loadClass("org.eclipse.elk.graph.util.ElkGraphUtil");
                
                initialized = true;
            } catch (ClassNotFoundException e) {
                Log("FIDDiagramConverter: ELK classes not available - " + e.getMessage());
            } catch (Exception e) {
                Log("FIDDiagramConverter: Error loading ELK classes - " + e.getMessage());
            }
        }
    }

    public FIDDiagramConverter() {
        this.elementToNodeMap = new HashMap<>();
        this.nodeToElementMap = new HashMap<>();
        this.cabinetGroupNodes = new HashMap<>();
    }

    /**
     * Convert a MagicDraw FID diagram to an ELK graph structure using structured data from Python
     * Optimized version that uses pre-collected part/port data instead of diagram traversal
     * 
     * @param diagram The MagicDraw FID diagram to convert
     * @param systemCabinetMap Optional cabinet grouping data from display_tools.py
     * @param spec Structured data containing parts/ports information from Python fidCreator_v1
     * @return ELK graph root node
     */
    public Object convertFIDToElkGraph(DiagramPresentationElement diagram,
                                       Object systemCabinetMap, Object spec) throws Exception {
        initializeELKClasses();
        if (elkNodeClass == null) {
            throw new RuntimeException("ELK classes not available");
        }
        
        // Extract structured data from spec
        java.util.List<?> partsData = null;
        java.util.List<?> portsData = null;
        java.util.List<?> connectorData = null;
        
        if (spec instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> specMap = (java.util.Map<String, Object>) spec;
            
            partsData = (java.util.List<?>) specMap.get("parts_data");
            portsData = (java.util.List<?>) specMap.get("ports_data");
            connectorData = (java.util.List<?>) specMap.get("connector_data");
            
            Log("DEBUG: Structured data - Parts: " + (partsData != null ? partsData.size() : 0) + 
                ", Ports: " + (portsData != null ? portsData.size() : 0) +
                ", Connectors: " + (connectorData != null ? connectorData.size() : 0));
        }

        // Create root graph node
        Object rootGraph = elkGraphUtilClass.getMethod("createGraph").invoke(null);
        
        // Set root graph dimensions based on diagram bounds
        Rectangle diagramBounds = diagram.getBounds();
        int rootWidth = Math.max(diagramBounds.width, 1200);
        int rootHeight = Math.max(diagramBounds.height, 800);
        setElkNodeDimensions(rootGraph, rootWidth, rootHeight);
        
        // If cabinet data is available, create cabinet group structure
        if (systemCabinetMap != null) {
            createCabinetGroupStructure(rootGraph, systemCabinetMap);
        } else {
            Log("DEBUG: No cabinet data available, skipping cabinet structure");
        }
        
        // Process parts
        int processedParts = 0;
        if (partsData != null) {
            Log("====DEBUG: Processing parts from structured data...====");
            for (Object partDataObj : partsData) {
                if (partDataObj instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> partInfo = (java.util.Map<String, Object>) partDataObj;

                    Object presentationElement = partInfo.get("presentation_element");
                    if (presentationElement instanceof PresentationElement) {
                        processPartForFID((PresentationElement) presentationElement, rootGraph, partInfo);
                        processedParts++;
                    } else {
                        Log("WARNING: No valid presentation element for part: " + partInfo.get("name") +
                            " (got: " + (presentationElement != null ? presentationElement.getClass().getSimpleName() : "null") + ")");
                    }
                }
            }
        }
        Log("DEBUG: Processed " + processedParts + " parts from structured data");
        
        // Process ports
        int processedPorts = 0;
        if (portsData != null) {
            Log("====DEBUG: Processing ports from structured data...====");
            for (Object portDataObj : portsData) {
                if (portDataObj instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> portInfo = (java.util.Map<String, Object>) portDataObj;

                    Object presentationElement = portInfo.get("presentation_element");

                    if (presentationElement instanceof PresentationElement) {
                        processPortForFID(
                            (PresentationElement) presentationElement,
                            rootGraph, portInfo);
                        processedPorts++;
                    } else {
                        Log("WARNING: No valid presentation element for port: " + portInfo.get("name") +
                            " (got: " + (presentationElement != null ? presentationElement.getClass().getSimpleName() : "null") + ")");
                    }
                }
            }
        }
        Log("DEBUG: Processed " + processedPorts + " ports from structured data");
        
        // Process connectors
        int processedConnectors = 0;
        if (connectorData != null) {
            Log("====DEBUG: Processing connectors from structured data...====");
            for (Object connectorDataObj : connectorData) {
                if (connectorDataObj instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> connectorInfo = (java.util.Map<String, Object>) connectorDataObj;

                    Object presentationElement = connectorInfo.get("presentation_element");
                    if (presentationElement instanceof PathElement) {
                        String connectorName = (String) connectorInfo.get("human_name");
                        Log("DEBUG: Processing connector " + (processedConnectors + 1) + " from structured data: " + connectorName);
                        processConnectorForFID((PathElement) presentationElement, rootGraph);
                        processedConnectors++;
                    } else {
                        Log("WARNING: No valid presentation element for connector: " + connectorInfo.get("name"));
                    }
                }
            }
        }
        
        // Fallback: Process any remaining connectors using standard diagram approach
        int additionalConnectors = 0;
        Log("DEBUG: Checking for any additional connectors not in structured data...");
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe instanceof PathElement && pe.isVisible()) {
                // Check if this connector was already processed
                boolean alreadyProcessed = false;
                if (connectorData != null) {
                    for (Object connectorDataObj : connectorData) {
                        // Check new Map format
                        if (connectorDataObj instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> connectorInfo = (java.util.Map<String, Object>) connectorDataObj;
                            Object presentationElement = connectorInfo.get("presentation_element");
                            if (presentationElement == pe) {
                                alreadyProcessed = true;
                                break;
                            }
                        }
                        // Check legacy tuple format
                        else if (connectorDataObj instanceof java.util.List) {
                            @SuppressWarnings("unchecked")
                            java.util.List<?> connectorTuple = (java.util.List<?>) connectorDataObj;
                            if (connectorTuple.size() >= 1) {
                                Object connector = connectorTuple.get(0);
                                if (connector instanceof com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element &&
                                    pe.getElement() == connector) {
                                    alreadyProcessed = true;
                                    break;
                                }
                            }
                        }
                    }
                }

                if (!alreadyProcessed) {
                    Log("DEBUG: Processing additional connector " + (++additionalConnectors) + ": " + pe.getHumanName());
                    processConnectorForFID((PathElement) pe, rootGraph);
                }
            }
        }

        Log("DEBUG: Finished processing connectors - " + processedConnectors + " from structured data, " +
            additionalConnectors + " additional from diagram");
        Log("DEBUG: Structured data conversion complete - Created ELK graph with " + elementToNodeMap.size() + " nodes");

        Log("Converted MagicDraw FID to ELK graph using structured data: " + elementToNodeMap.size() + " nodes");
        return rootGraph;
    }
    
    /**
     * Find the presentation element for a given model element within the diagram
     */
    private PresentationElement findPresentationElementForModelElement(
        com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element modelElement, 
        DiagramPresentationElement diagram) {
        
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe.getElement() == modelElement) {
                return pe;
            }
            
            // Check child elements recursively
            PresentationElement found = findPresentationElementRecursive(modelElement, pe);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * Recursively search for presentation element matching model element
     */
    private PresentationElement findPresentationElementRecursive(
        com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element modelElement,
        PresentationElement parent) {
        
        for (PresentationElement child : parent.getPresentationElements()) {
            if (child.getElement() == modelElement) {
                return child;
            }
            
            PresentationElement found = findPresentationElementRecursive(modelElement, child);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * Process a part using structured data instead of presentation element traversal
     */
    private void processPartForFID(PresentationElement partPE, Object parentNode,
                                                    java.util.Map<String, Object> partInfo) throws Exception {

        // Create ELK node for this part using existing presentation element
        Object elkNode = elkGraphUtilClass.getMethod("createNode", elkNodeClass).invoke(null, parentNode);

        // Get part information from structured data
        String partName = (String) partInfo.get("name");
        String humanName = (String) partInfo.get("human_name");
        Integer level = (Integer) partInfo.get("level");

        // Store bidirectional mapping using the presentation element

        elementToNodeMap.put(partPE, elkNode);
        nodeToElementMap.put(elkNode, partPE);

        // Set node dimensions from existing presentation element bounds
        Rectangle bounds = partPE.getBounds();
        double width = Math.max(bounds.width, 120);
        double height = Math.max(bounds.height, 80);
        setElkNodeDimensions(elkNode, width, height);

        // Set node position (relative to parent)
        setElkNodePosition(elkNode, bounds.x, bounds.y);

        // Set identifier for debugging
        elkNode.getClass().getMethod("setIdentifier", String.class)
                .invoke(elkNode, "FID_Part_" + partName);
    }
    
    /**
     * Process a port using structured data instead of presentation element traversal
     */
    private void processPortForFID(PresentationElement portPE,
                                                   Object rootGraph, java.util.Map<String, Object> portInfo) throws Exception {
        // Get port information from structured data
        String portName = (String) portInfo.get("name");
        String humanName = (String) portInfo.get("human_name");
        Object parentPresentationElement = portInfo.get("parent_presentation_element");
        Integer level = (Integer) portInfo.get("level");
        Boolean isTopLevel = (Boolean) portInfo.get("is_top_level");
        
        // Get parent node directly from presentation element mapping
        Object parentNode = null;
        if (parentPresentationElement instanceof PresentationElement) {
            parentNode = elementToNodeMap.get((PresentationElement) parentPresentationElement);
        }

        // If no parent found from structured data, fallback to port view hierarchy
        if (parentNode == null) {
            PresentationElement parentPE = portPE.getParent();
            if (parentPE != null) {
                parentNode = elementToNodeMap.get(parentPE);
            }
        }
        
        if (parentNode != null) {
            // Create ELK port for this port view
            Object elkPort = elkGraphUtilClass.getMethod("createPort", elkNodeClass).invoke(null, parentNode);

            // Store mapping
            elementToNodeMap.put(portPE, elkPort);
            nodeToElementMap.put(elkPort, portPE);
            
            // Set port dimensions and position
            Rectangle bounds = portPE.getBounds();
            setElkPortDimensions(elkPort, Math.max(bounds.width, 10), Math.max(bounds.height, 10));
            setElkPortPosition(elkPort, bounds.x, bounds.y);
            
            // Set identifier
            elkPort.getClass().getMethod("setIdentifier", String.class)
                    .invoke(elkPort, "FID_Port_" + portName);
        }
    }

    /**
     * Create cabinet group structure in ELK graph if cabinet data is available
     * This preserves the grouping logic from display_tools.py rearrange_items()
     */
    private void createCabinetGroupStructure(Object rootGraph, Object systemCabinetMap) throws Exception {
        if (systemCabinetMap == null) {
            Log("No cabinet grouping data provided - skipping group structure creation");
            return;
        }

        try {
            if (systemCabinetMap instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> hierarchyMap = (java.util.Map<String, Object>) systemCabinetMap;
                
                // Create ELK group nodes for each system/cabinet combination
                for (java.util.Map.Entry<String, Object> systemEntry : hierarchyMap.entrySet()) {
                    String systemName = systemEntry.getKey();
                    
                    if (systemEntry.getValue() instanceof java.util.Map) {
                        @SuppressWarnings("unchecked")
                        java.util.Map<String, Object> systemCabinets = (java.util.Map<String, Object>) systemEntry.getValue();
                        
                        for (java.util.Map.Entry<String, Object> cabinetEntry : systemCabinets.entrySet()) {
                            String cabinetName = cabinetEntry.getKey();
                            
                            if (cabinetEntry.getValue() instanceof java.util.Map) {
                                @SuppressWarnings("unchecked")
                                java.util.Map<String, Object> cabinetData = (java.util.Map<String, Object>) cabinetEntry.getValue();
                                
                                Object elementsObj = cabinetData.get("elements");
                                if (elementsObj instanceof java.util.List) {
                                    @SuppressWarnings("unchecked")
                                    java.util.List<Object> elements = (java.util.List<Object>) elementsObj;
                                    
                                    // Only create groups for cabinets with multiple elements
                                    if (elements.size() > 1) {
                                        createELKCabinetGroup(rootGraph, systemName, cabinetName, elements, cabinetData);
                                    }
                                }
                            }
                        }
                    }
                }
                
                Log("#".repeat(50));
            } else {
                Log("Warning: systemCabinetMap is not a Map - skipping cabinet group structure");
            }
            
        } catch (Exception e) {
            Log("Error creating cabinet group structure: " + e.getMessage());
        }
    }
    
    /**
     * Create an ELK group node for a specific cabinet with its elements
     */
    private void createELKCabinetGroup(Object rootGraph, String systemName, String cabinetName, 
                                      java.util.List<Object> elements, java.util.Map<String, Object> cabinetData) throws Exception {
        
        // Create ELK group node for this cabinet
        Object cabinetGroupNode = elkGraphUtilClass.getMethod("createNode", elkNodeClass).invoke(null, rootGraph);
        
        // Set group identifier for debugging
        String groupId = "CabinetGroup_" + systemName + "_" + cabinetName;
        cabinetGroupNode.getClass().getMethod("setIdentifier", String.class).invoke(cabinetGroupNode, groupId);
        cabinetGroupNodes.put(groupId, cabinetGroupNode);
        Log("Created ELK cabinet group: " + groupId + " with " + elements.size() + " elements");
    }

    /**
     * Process a connector (PathElement) for FID and create corresponding ELK edge
     * Handles connectors created by create_connectors_after_rearrangement()
     */
    private void processConnectorForFID(PathElement connector, Object rootGraph) throws Exception {
        String connectorName = connector.getElement() != null ? connector.getElement().getHumanName() : "Unnamed";
        Log("DEBUG: === Processing connector: " + connectorName + " ===");

        // Get connector endpoints using MagicDraw connector API
        PresentationElement source = getConnectorSource(connector);
        PresentationElement target = getConnectorTarget(connector);

        Log("DEBUG: Connector source: " + (source != null ? source.getHumanName() + " (" + source.getClass().getSimpleName() + ")" : "null"));
        Log("DEBUG: Connector target: " + (target != null ? target.getHumanName() + " (" + target.getClass().getSimpleName() + ")" : "null"));

        if (source != null && target != null) {
            Object sourceNode = elementToNodeMap.get(source);
            Object targetNode = elementToNodeMap.get(target);

            Log("DEBUG: Source ELK node: " + (sourceNode != null ? sourceNode.getClass().getSimpleName() : "null"));
            Log("DEBUG: Target ELK node: " + (targetNode != null ? targetNode.getClass().getSimpleName() : "null"));

            if (sourceNode != null && targetNode != null) {
                // Create ELK edge between the connected elements
                Object elkEdge = createElkEdge(sourceNode, targetNode);

                if (elkEdge != null) {
                    // Debug logging for edge creation
                    String sourceName = source.getHumanName();
                    String targetName = target.getHumanName();

                    Log("DEBUG: Created ELK edge: " + connectorName + " from " + sourceName + " to " + targetName);
                    elkEdge.getClass().getMethod("setIdentifier", String.class)
                            .invoke(elkEdge, "FID_Conn_" + connectorName);
                } else {
                    Log("DEBUG: Skipped connector edge creation (returned null)");
                }
            } else {
                Log("DEBUG: Cannot create edge - missing ELK nodes in elementToNodeMap");
                Log("DEBUG: elementToNodeMap size: " + elementToNodeMap.size());
                Log("DEBUG: Available elements in map:");
                for (PresentationElement pe : elementToNodeMap.keySet()) {
                    Log("DEBUG:   - " + pe.getHumanName() + " (" + pe.getClass().getSimpleName() + ")");
                }
            }
        } else {
            Log("DEBUG: Cannot process connector - missing source or target");
        }
        Log("DEBUG: === End processing connector: " + connectorName + " ===");
    }

    /**
     * Get the source presentation element of a connector
     * Uses MagicDraw PathElement API
     */
    private PresentationElement getConnectorSource(PathElement connector) {
        try {
            // Get the client (source) of the connector
            return connector.getClient();
        } catch (Exception e) {
            Log("Warning: Could not find connector source: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get the target presentation element of a connector
     * Uses MagicDraw PathElement API  
     */
    private PresentationElement getConnectorTarget(PathElement connector) {
        try {
            // Get the supplier (target) of the connector
            return connector.getSupplier();
        } catch (Exception e) {
            Log("Warning: Could not find connector target: " + e.getMessage());
            return null;
        }
    }

    /**
     * Create an ELK edge between two nodes using reflection
     * Handles both node-to-node and node-to-port connections
     */
    private Object createElkEdge(Object sourceNode, Object targetNode) throws Exception {
        // Since all connections are port-to-port, check for this case first
        boolean sourceIsPort = sourceNode.getClass().getSimpleName().contains("Port");
        boolean targetIsPort = targetNode.getClass().getSimpleName().contains("Port");
        
        Log("DEBUG: Creating edge - Source: " + sourceNode.getClass().getSimpleName() + 
            ", Target: " + targetNode.getClass().getSimpleName());
        
        if (sourceIsPort && targetIsPort) {
            // Port-to-port connection: create edge between parent nodes and set source/target ports
            try {
                Object sourceParent = sourceNode.getClass().getMethod("getParent").invoke(sourceNode);
                Object targetParent = targetNode.getClass().getMethod("getParent").invoke(targetNode);

                // Validate parent nodes
                if (sourceParent == null) {
                    throw new Exception("Source port has no parent node");
                }
                if (targetParent == null) {
                    throw new Exception("Target port has no parent node");
                }

                Log("DEBUG: Creating edge between parent nodes for port-to-port connection");
                Log("DEBUG: Source parent: " + sourceParent.getClass().getSimpleName());
                Log("DEBUG: Target parent: " + targetParent.getClass().getSimpleName());

                // Create edge between parent nodes
                Log("DEBUG: About to call createSimpleEdge with:");
                Log("DEBUG:   elkGraphUtilClass: " + elkGraphUtilClass.getName());
                Log("DEBUG:   elkNodeClass: " + elkNodeClass.getName());
                Log("DEBUG:   sourceParent class: " + sourceParent.getClass().getName());
                Log("DEBUG:   targetParent class: " + targetParent.getClass().getName());

                Object edge = elkGraphUtilClass.getMethod("createSimpleEdge", elkNodeClass, elkNodeClass)
                        .invoke(null, sourceParent, targetParent);
                
                // Set source and target ports on the edge
                try {
                    // Get the sources collection and add the source port
                    Object sources = edge.getClass().getMethod("getSources").invoke(edge);
                    sources.getClass().getMethod("add", Object.class).invoke(sources, sourceNode);
                    
                    // Get the targets collection and add the target port
                    Object targets = edge.getClass().getMethod("getTargets").invoke(edge);
                    targets.getClass().getMethod("add", Object.class).invoke(targets, targetNode);
                    
                    Log("DEBUG: Successfully created port-to-port edge with source/target ports set");
                } catch (Exception portEx) {
                    Log("DEBUG: Warning - Could not set source/target ports on edge: " + portEx.getMessage());
                    // Edge creation succeeded, just port assignment failed - continue
                }
                
                return edge;
                
            } catch (Exception parentEx) {
                Log("DEBUG: Failed to get parent nodes for ports: " + parentEx.getMessage());
                Log("DEBUG: Attempting fallback: skip this connector edge");
                // Instead of throwing exception, return null to skip this edge
                // This allows the layout to continue with other connectors
                return null;
            }
            
        } else {
            // Fallback for non-port connections (shouldn't happen in FID but include for safety)
            try {
                return elkGraphUtilClass.getMethod("createSimpleEdge", elkNodeClass, elkNodeClass)
                        .invoke(null, sourceNode, targetNode);
            } catch (Exception e) {
                Log("DEBUG: Failed to create standard edge");
                Log("DEBUG: Source type: " + sourceNode.getClass().getSimpleName());
                Log("DEBUG: Target type: " + targetNode.getClass().getSimpleName());
                throw new Exception("Could not create ELK edge between " + 
                                  sourceNode.getClass().getSimpleName() + " and " + 
                                  targetNode.getClass().getSimpleName(), e);
            }
        }
    }

    /**
     * Extract layout coordinates from ELK graph result and map back to MagicDraw elements
     * 
     * @param elkGraph The laid-out ELK graph
     * @param diagram The original MagicDraw FID diagram
     * @return Map of presentation elements to their new coordinates
     */
    public Map<PresentationElement, Point> extractLayoutCoordinates(Object elkGraph, 
                                                                   DiagramPresentationElement diagram) throws Exception {
        Map<PresentationElement, Point> coordinates = new HashMap<>();
        
        // Recursively extract coordinates from ELK nodes
        extractNodeCoordinates(elkGraph, coordinates, 0, 0);
        
        Log("Extracted " + coordinates.size() + " FID element coordinates from ELK layout");
        return coordinates;
    }

    /**
     * Recursively extract coordinates from ELK nodes
     */
    private void extractNodeCoordinates(Object elkNode, Map<PresentationElement, Point> coordinates, 
                                      double parentX, double parentY) throws Exception {
        // Get the corresponding MagicDraw presentation element
        PresentationElement pe = nodeToElementMap.get(elkNode);
        
        if (pe != null) {
            // Get ELK node position
            double x = (Double) elkNode.getClass().getMethod("getX").invoke(elkNode);
            double y = (Double) elkNode.getClass().getMethod("getY").invoke(elkNode);
            
            // Convert to absolute coordinates with proper rounding
            Point absolutePosition = new Point(
                (int) Math.round(parentX + x), 
                (int) Math.round(parentY + y)
            );
            coordinates.put(pe, absolutePosition);
            
            // Process children with updated parent coordinates
            Collection<?> children = (Collection<?>) elkNode.getClass().getMethod("getChildren").invoke(elkNode);
            for (Object child : children) {
                extractNodeCoordinates(child, coordinates, parentX + x, parentY + y);
            }
        }
    }

    // Helper methods for setting ELK properties using reflection
    
    private void setElkNodeDimensions(Object elkNode, double width, double height) throws Exception {
        elkNode.getClass().getMethod("setDimensions", double.class, double.class)
                .invoke(elkNode, width, height);
    }

    private void setElkNodePosition(Object elkNode, double x, double y) throws Exception {
        elkNode.getClass().getMethod("setLocation", double.class, double.class)
                .invoke(elkNode, x, y);
    }

    private void setElkPortDimensions(Object elkPort, double width, double height) throws Exception {
        elkPort.getClass().getMethod("setDimensions", double.class, double.class)
                .invoke(elkPort, width, height);
    }

    private void setElkPortPosition(Object elkPort, double x, double y) throws Exception {
        elkPort.getClass().getMethod("setLocation", double.class, double.class)
                .invoke(elkPort, x, y);
    }
}