from core.imports import *
from core.constants import REQUIRED_RESOURCES
from utils.md_utils import printer

class CreatorService:
    """Service for creating MagicDraw model elements"""

    def __init__(self, project, taxonomy_dict, taxonomy_package, package_service, materials_dict, bcl_package=None):
        self.project = project
        self.element_factory = project.getElementsFactory()
        self.model = project.getPrimaryModel()
        self.taxonomy_dictionary = taxonomy_dict
        self.taxonomy_package = taxonomy_package
        self.bcl_package = bcl_package
        self.package_service = package_service
        self.materials_dict = materials_dict

    def create_IDP_Element(self, name, type, package):
        """Creates a block type element with proper generalization"""
        blockStereotype = SysMLProfile.getInstance(self.project).getBlock()
        blockElement = self.element_factory.createClassInstance()
        sh.addStereotype(blockElement, blockStereotype)
        blockElement.setOwner(self.model)
        blockElement.setName(name)
        if type and "::" in type:
            classifier_by_qn = f.byQualifiedName().find(self.project, type)
            if classifier_by_qn:
                generalization = self.element_factory.createGeneralizationInstance()
                generalization.setSpecific(blockElement)
                generalization.setGeneral(classifier_by_qn)
                generalization.setOwner(blockElement)

        elif type and "::" not in type:
            # Determine where classifier is defined
            type_owner = self.type_owner_translator(type)

            if type_owner:
                # Find classifier in taxonomy or parts catalog
                type_owner_package = next((
                    self.package_service.find_package_by_name(models, type_owner)
                    for models in self.project.getModels()
                    if self.package_service.find_package_by_name(models, type_owner)
                ), None)

                if type_owner_package:
                    classifier_qn = type_owner_package + "::" + type
                    classifier_by_qn = f.byQualifiedName().find(self.project, classifier_qn)
                    if classifier_by_qn:
                        generalization = self.element_factory.createGeneralizationInstance()
                        generalization.setSpecific(blockElement)
                        generalization.setGeneral(classifier_by_qn)
                        generalization.setOwner(blockElement)

        blockElement.setOwner(package)
        return blockElement

    def createPropGeneric(self, element, attName, attValue, className):
        """Creates a generic Property with redefinition from taxonomy"""
        if attValue in (None, "", "None"):
            attValue = "None"
        try:
            propDef = self.element_factory.createPropertyInstance()
            propDef.setVisibility(VisibilityKindEnum.getByName("public"))
            propDef.setName(attName)
            propDef.setAggregation(AggregationKindEnum.getByName("composite"))
            propDef.setOwner(element)

            gen = self._find_attribute_in_classifier(className, attName)
            
            if gen:
                propDef.getRedefinedProperty().add(gen)
                propDef.setType(gen.getType())

            # Handle value type
            if isinstance(attValue, (float, int)) and attName != 'id':
                actDef = self.element_factory.createLiteralRealInstance()
                actDef.setValue(attValue)
            else:
                actDef = self.element_factory.createLiteralStringInstance()
                actDef.setValue(str(attValue))

            actDef.setVisibility(VisibilityKindEnum.getByName("public"))
            actDef.setOwner(propDef)
            propDef.setDefaultValue(actDef)

            return propDef
        except Exception as e:
            printer("Error in createPropGeneric: {0}".format(str(e)))
            return None

    def create_properties(self, block_element, classifier, data):
        """Creates or updates properties on a block element based on taxonomy

        Args:
            block_element: Target block element to process
            classifier: The classifier type (e.g. "System", "Asset")
            data: Property values to apply
        """
        # Get existing properties for efficient lookup
        existing_props = {prop.getName(): prop for prop in block_element.getOwnedAttribute()}
        # Get owned attributes from taxonomy
        owned_attrs = self.taxonomy_dictionary.get(classifier, {}).get('Owned Attributes', [])

        for owned_attr in owned_attrs:
            if owned_attr in data:
                value = data[owned_attr]
                if value in (None, "", "None"):
                    value = "None"

                if owned_attr in existing_props:
                    try:
                        # Update existing property
                        existing_prop = existing_props[owned_attr]
                        old_value = existing_prop.getDefaultValue().getValue()
                        if str(old_value) != str(value):
                            existing_prop.getDefaultValue().setValue(value)
                            self.project.getRepository().commit(existing_prop)
                    except:
                        continue
                else:
                    # Create new property
                    self.createPropGeneric(block_element, owned_attr, value, classifier)

    def process_classifiers(self, block_element, start_classifier, data):
        """Process properties recursively through classifier hierarchy

        Args:
            block_element: Target block element
            start_classifier: Initial classifier to start processing from
            data: Data to apply to properties
        """
        processed = set()
        classifiers_to_process = [start_classifier]

        while classifiers_to_process:
            current = classifiers_to_process.pop(0)
            if current not in processed and current in self.taxonomy_dictionary:
                processed.add(current)

                self.create_properties(block_element, current, data)
                next_classifiers = self.taxonomy_dictionary[current]['Classifier Elements']
                classifiers_to_process.extend([c for c in next_classifiers if c not in processed])

    def createCompositeRelationship(self, source, target, targetName, package):
        """Creates directed composition relationship between elements"""
        # Check existing part properties under source block
        for owned_attr in source.getOwnedAttribute():
            if any(st.getName() == "PartProperty" for st in owned_attr.getAppliedStereotype()):
               # check name match if targetName specified
                if targetName and owned_attr.getName() == targetName:
                    return None

        # Create new relationship only if no existing one found
        compRelation = self.element_factory.createAssociationInstance()
        compRelation.setOwner(package)
        parent = mh.getFirstMemberEnd(compRelation)
        sh.addStereotypeByString(parent, "PartProperty")
        parent.setOwner(source)
        if targetName is not None:
            parent.setName(targetName)
        parent.setType(target)
        parent.setVisibility(VisibilityKindEnum.getByName("public"))
        parent.setAggregation(AggregationKindEnum.getByName("composite"))
        mh.setNavigable(parent, True)

        # Set up child end
        child = mh.getSecondMemberEnd(compRelation)
        child.setOwner(target)
        child.setType(source)
        child.setVisibility(VisibilityKindEnum.getByName("public"))
        mh.setNavigable(child, False)

        return compRelation

    def createReferenceProperty(self, source, target, targetName, package):
        """Creates a reference property relationship between elements"""
        refRelation = self.element_factory.createAssociationInstance()
        refRelation.setOwner(package)

        # Set up reference end
        refEnd = mh.getFirstMemberEnd(refRelation)
        sh.addStereotypeByString(refEnd, "ReferenceProperty")
        refEnd.setOwner(source)
        if targetName:
            refEnd.setName(targetName)
        refEnd.setType(target)
        refEnd.setVisibility(VisibilityKindEnum.getByName("public"))
        mh.setNavigable(refEnd, True)

        # Set up opposite end
        oppositeEnd = mh.getSecondMemberEnd(refRelation)
        oppositeEnd.setOwner(target)
        oppositeEnd.setType(source)
        oppositeEnd.setVisibility(VisibilityKindEnum.getByName("public"))
        mh.setNavigable(oppositeEnd, False)

        return refRelation

    def type_owner_translator(self, type):
        """Determines where a classifier type is defined

        Args:
            type (str): Name of the type/classifier to lookup

        Returns:
            str: Either REQUIRED_RESOURCES["TAXONOMY"], REQUIRED_RESOURCES["PARTS_CATALOG"], or REQUIRED_RESOURCES["BCL"]
        """

        # Convert input to lowercase for case-insensitive comparison
        type_lower = type.lower()

        # Check taxonomy dictionary
        for tax_type in self.taxonomy_dictionary:
            if type_lower == tax_type.lower():
                return REQUIRED_RESOURCES["TAXONOMY"]

        # Check materials catalog
        if type_lower in [m.lower() for m in self.materials_dict.keys()]:
            return REQUIRED_RESOURCES["PARTS_CATALOG"]

        return None

    def createConnection(self, connectionOwner, sourcePart, targetPart, sourcePort, targetPort):
        """Create connection between part properties of a context block

        Args:
            connectionOwner: Element serving as context block
            sourcePart: Source element
            targetPart: Target element
            sourcePort: Source port (optional)
            targetPort: Target port (optional)
        """
        # Create connector instance
        connector = self.element_factory.createConnectorInstance()

        # Port to Port
        if targetPort and sourcePort:
            mh.setClientElement(connector, sourcePort)
            mh.setSupplierElement(connector, targetPort)

            # Set up nested connector ends
            end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()

            end1 = mh.getFirstEnd(connector)
            sh.addStereotype(end1, end)
            sh.setStereotypePropertyValue(end1, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, sourcePart)

            end2 = mh.getSecondEnd(connector)
            sh.addStereotype(end2, end)
            sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, targetPart)

        # Port to Part
        elif targetPort and not sourcePort:
            mh.setClientElement(connector, sourcePart)
            mh.setSupplierElement(connector, targetPort)

            end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
            end2 = mh.getSecondEnd(connector)
            sh.addStereotype(end2, end)
            sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, targetPart)

        # Port to Part
        elif sourcePort and not targetPort:
            mh.setClientElement(connector, sourcePort)
            if isinstance(targetPart, tuple):
                targetElement = targetPart[-1]
                parentParts = targetPart[:-1]
                mh.setSupplierElement(connector, targetElement)
                end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
                end2 = mh.getSecondEnd(connector)
                sh.addStereotype(end2, end)
                sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, parentParts)
            else:
                mh.setSupplierElement(connector, targetPart)

            end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
            end1 = mh.getFirstEnd(connector)
            sh.addStereotype(end1, end)
            sh.setStereotypePropertyValue(end1, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, sourcePart)

        # Part to Part
        else:
            mh.setClientElement(connector, sourcePart)
            mh.setSupplierElement(connector, targetPart)

        # Set owner and return
        connector.setOwner(connectionOwner)
        return connector

    def _find_attribute_in_classifier(self, className, attName):
        """
        Finds an attribute in a specific classifier class.
        
        Search order:
        1. IDP Taxonomy package (for taxonomy classes)
        2. BCL package structure (for BCL classes in sub-packages)
        
        Args:
            className (str): Name of the classifier to search in
            attName (str): Name of the attribute to find
            
        Returns:
            Property: The found attribute property, or None if not found
        """
        # First try taxonomy package (most common case for IDP taxonomy classes)
        taxonomy_class = f.byQualifiedName().find(
            self.project, 
            self.taxonomy_package.getQualifiedName() + "::" + className
        )
        
        if taxonomy_class:
            attribute = next(
                (attr for attr in taxonomy_class.getOwnedAttribute() 
                 if attr.getName() == attName), None
            )
            if attribute:
                return attribute
        
        # BCL classes are distributed across sub-packages within BCL package
        # Search specifically within BCL package structure
        if self.bcl_package:
            bcl_class = self._search_class_in_package(self.bcl_package, className)
            if bcl_class:
                attribute = next(
                    (attr for attr in bcl_class.getOwnedAttribute() 
                     if attr.getName() == attName), None
                )
                if attribute:
                    return attribute
        
        return None

    def _search_class_in_package(self, package, class_name):
        """
        Recursively searches for a class in a package and its sub-packages.
        
        Args:
            package: Package to search in
            class_name (str): Name of the class to find
            
        Returns:
            Class: The found class element, or None if not found
        """
        # Check direct members
        for member in package.getOwnedMember():
            if hasattr(member, 'getName') and member.getName() == class_name:
                if sh.hasStereotype(member, "Block") or member.getHumanType() == "Class":
                    return member
            
            # Recursively search sub-packages
            if isinstance(member, Package):
                result = self._search_class_in_package(member, class_name)
                if result:
                    return result
        
        return None