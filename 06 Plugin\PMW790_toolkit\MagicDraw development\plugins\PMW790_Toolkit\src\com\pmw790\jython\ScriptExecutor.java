package com.pmw790.jython;

import org.python.util.PythonInterpreter;
import com.nomagic.magicdraw.core.Application;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * Utility class for executing Python scripts with standardized parameter setup
 */
public final class ScriptExecutor {
    
    private static final String MODULE_CACHE_FILE = "pmw790_module_cache.properties";
    private static final String CACHE_KEY_PREFIX = "module.";
    private static final String CACHE_KEY_SUFFIX = ".cached_time";
    
    /**
     * Sets up common MagicDraw application parameters in the interpreter
     * @param interp The Python interpreter
     */
    public static void setupMagicDrawParams(PythonInterpreter interp) {
        interp.set("app", Application.getInstance());
        interp.set("project", Application.getInstance().getProject());
    }
    
    /**
     * Sets up parameters for import system script
     * @param interp The Python interpreter
     * @param jsonFolderPath Path to the JSON folder
     * @param baseDir Base directory for scripts
     */
    public static void setupImportParams(PythonInterpreter interp, String jsonFolderPath, String baseDir) {
        interp.set("json_folder_path", jsonFolderPath);
        File importerDir = new File(baseDir, JythonConstants.IMPORTER_DIR);
        interp.set("base_dir", importerDir.getAbsolutePath());
        setupMagicDrawParams(interp);
    }
    
    /**
     * Sets up parameters for export script
     * @param interp The Python interpreter
     * @param exportFilePath Path for the export file
     * @param catalogFilePath Path for the catalog file (can be empty)
     */
    public static void setupExportParams(PythonInterpreter interp, String exportFilePath, String catalogFilePath) {
        interp.set("export_file_path", exportFilePath);
        interp.set("part_catalog_path", catalogFilePath);
        setupMagicDrawParams(interp);
    }
    
    /**
     * Sets up parameters for export to API (skips parts catalog)
     * @param interp The Python interpreter
     * @param exportFilePath Path for the export file
     */
    public static void setupExportToApiParams(PythonInterpreter interp, String exportFilePath) {
        interp.set("export_file_path", exportFilePath);
        interp.set("part_catalog_path", "");
        interp.set("skip_parts_catalog", true);
        setupMagicDrawParams(interp);
    }
    
    /**
     * Sets up parameters for FID L2 script with system cabinet mapping
     * @param interp The Python interpreter
     * @param selectedElement The selected element
     * @param depth The depth parameter
     * @param baseDir Base directory for scripts
     */
    public static void setupFidL2Params(PythonInterpreter interp, Element selectedElement, int depth, String baseDir) {
        interp.set("selectedElement", selectedElement);
        interp.set("depth", depth);
        File fidBuilderDir = new File(baseDir, JythonConstants.FID_BUILDER_DIR);
        interp.set("base_dir", fidBuilderDir.getAbsolutePath());
        setupMagicDrawParams(interp);
        
        // Add system cabinet mapping
        java.util.Map<String, java.util.Map<String, java.util.Map<String, Object>>> systemCabinetMap =
            com.pmw790.functions.Utilities.CabinetProperties.getSystemCabinetMap();
        interp.set("systemCabinetMap", systemCabinetMap);
    }
    
    /**
     * Executes a script from the importer directory
     * @param interp The Python interpreter
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script to execute
     */
    public static void executeImporterScript(PythonInterpreter interp, String baseDir, String scriptName) {
            File importerDir = new File(baseDir, JythonConstants.IMPORTER_DIR);
            File scriptFile = new File(importerDir, scriptName);
    
            // Load module cache timestamps
            Properties moduleCache = loadModuleCache();
            
            // Selective module reload - only reload modules that have changed
            String selectiveReloadScript =
                "import sys, os, imp\n" +
                "importer_path = os.path.abspath(r'" + importerDir.getAbsolutePath().replace("\\", "\\\\") + "')\n" +
                "changed_modules = []\n" +
                "module_cache = {}\n" +
                "for name, module in list(sys.modules.items()):\n" +
                "    try:\n" +
                "        file = getattr(module, '__file__', None)\n" +
                "        if file and os.path.abspath(file).startswith(importer_path):\n" +
                "            # Check if module file has been modified since last load\n" +
                "            if os.path.exists(file):\n" +
                "                file_time = os.path.getmtime(file)\n" +
                "                cached_time = module_cache.get(name, 0)\n" +
                "                if file_time > cached_time:\n" +
                "                    changed_modules.append(name)\n" +
                "            else:\n" +
                "                changed_modules.append(name)\n" +
                "    except Exception:\n" +
                "        pass\n" +
                "# Only remove changed modules from cache\n" +
                "for name in changed_modules:\n" +
                "    sys.modules.pop(name, None)\n";
            
            // Pass the module cache to Python
            interp.set("module_cache_java", moduleCache);
            
            // Update the script to use Java-provided cache (filtered by directory)
            String dirContext = importerDir.getName(); // "importer"
            String cacheScript = 
                "module_cache = {}\n" +
                "if 'module_cache_java' in locals():\n" +
                "    for key in module_cache_java.stringPropertyNames():\n" +
                "        if key.startswith('" + CACHE_KEY_PREFIX + dirContext + ".') and key.endswith('" + CACHE_KEY_SUFFIX + "'):\n" +
                "            full_key = key[" + CACHE_KEY_PREFIX.length() + ":-" + CACHE_KEY_SUFFIX.length() + "]\n" +
                "            module_name = full_key[" + (dirContext.length() + 1) + ":]\n" +  // Skip "importer."
                "            module_cache[module_name] = float(module_cache_java.getProperty(key, '0'))\n";
            
            interp.exec(cacheScript + selectiveReloadScript);
    
            interp.execfile(scriptFile.getAbsolutePath());
            
            // Update module cache with current timestamps
            updateModuleCache(interp, importerDir);
        }
    
    /**
     * Executes a script from the standalone directory
     * @param interp The Python interpreter
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script to execute
     */
    public static void executeStandaloneScript(PythonInterpreter interp, String baseDir, String scriptName) {
            File standaloneDir = new File(baseDir, JythonConstants.STANDALONE_DIR);
            interp.set("standalone_dir", standaloneDir.getAbsolutePath());
            File scriptFile = new File(standaloneDir, scriptName);

            // Remove all modules under standaloneDir from sys.modules to force reload
            String clearModulesScript =
                "import sys, os\n" +
                "standalone_path = os.path.abspath(r'" + standaloneDir.getAbsolutePath().replace("\\", "\\\\") + "')\n" +
                "to_remove = []\n" +
                "for name, module in sys.modules.items():\n" +
                "    try:\n" +
                "        file = getattr(module, '__file__', None)\n" +
                "        if file and os.path.abspath(file).startswith(standalone_path):\n" +
                "            to_remove.append(name)\n" +
                "    except Exception:\n" +
                "        pass\n" +
                "for name in to_remove:\n" +
                "    sys.modules.pop(name, None)\n";
            interp.exec(clearModulesScript);

            interp.execfile(scriptFile.getAbsolutePath());
        }
    
    /**
     * Executes a script from the fid_builder directory with module caching
     * @param interp The Python interpreter
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script to execute
     */
    public static void executeFidBuilderScript(PythonInterpreter interp, String baseDir, String scriptName) {
            File fidBuilderDir = new File(baseDir, JythonConstants.FID_BUILDER_DIR);
            File scriptFile = new File(fidBuilderDir, scriptName);
    
            // Load module cache timestamps
            Properties moduleCache = loadModuleCache();
            
            // Selective module reload - only reload modules that have changed
            String selectiveReloadScript =
                "import sys, os, imp\n" +
                "fid_builder_path = os.path.abspath(r'" + fidBuilderDir.getAbsolutePath().replace("\\", "\\\\") + "')\n" +
                "changed_modules = []\n" +
                "module_cache = {}\n" +
                "for name, module in list(sys.modules.items()):\n" +
                "    try:\n" +
                "        file = getattr(module, '__file__', None)\n" +
                "        if file and os.path.abspath(file).startswith(fid_builder_path):\n" +
                "            # Check if module file has been modified since last load\n" +
                "            if os.path.exists(file):\n" +
                "                file_time = os.path.getmtime(file)\n" +
                "                cached_time = module_cache.get(name, 0)\n" +
                "                if file_time > cached_time:\n" +
                "                    changed_modules.append(name)\n" +
                "            else:\n" +
                "                changed_modules.append(name)\n" +
                "    except Exception:\n" +
                "        pass\n" +
                "# Only remove changed modules from cache\n" +
                "for name in changed_modules:\n" +
                "    sys.modules.pop(name, None)\n";
            
            // Pass the module cache to Python
            interp.set("module_cache_java", moduleCache);
            
            // Update the script to use Java-provided cache (filtered by directory)
            String dirContext = fidBuilderDir.getName(); // "fid_builder"
            String cacheScript = 
                "module_cache = {}\n" +
                "if 'module_cache_java' in locals():\n" +
                "    for key in module_cache_java.stringPropertyNames():\n" +
                "        if key.startswith('" + CACHE_KEY_PREFIX + dirContext + ".') and key.endswith('" + CACHE_KEY_SUFFIX + "'):\n" +
                "            full_key = key[" + CACHE_KEY_PREFIX.length() + ":-" + CACHE_KEY_SUFFIX.length() + "]\n" +
                "            module_name = full_key[" + (dirContext.length() + 1) + ":]\n" +  // Skip "fid_builder."
                "            module_cache[module_name] = float(module_cache_java.getProperty(key, '0'))\n";
            
            interp.exec(cacheScript + selectiveReloadScript);
    
            interp.execfile(scriptFile.getAbsolutePath());
            
            // Update module cache with current timestamps
            updateModuleCache(interp, fidBuilderDir);
        }
    
    /**
     * Gets the path to a script in the importer directory
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script
     * @return Full path to the script
     */
    public static String getImporterScriptPath(String baseDir, String scriptName) {
        File importerDir = new File(baseDir, JythonConstants.IMPORTER_DIR);
        return new File(importerDir, scriptName).getAbsolutePath();
    }
    
    /**
     * Gets the path to a script in the standalone directory
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script
     * @return Full path to the script
     */
    public static String getStandaloneScriptPath(String baseDir, String scriptName) {
        File standaloneDir = new File(baseDir, JythonConstants.STANDALONE_DIR);
        return new File(standaloneDir, scriptName).getAbsolutePath();
    }
    
    /**
     * Checks if a script exists in the standalone directory
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script
     * @return true if script exists, false otherwise
     */
    public static boolean standaloneScriptExists(String baseDir, String scriptName) {
        File standaloneDir = new File(baseDir, JythonConstants.STANDALONE_DIR);
        File scriptFile = new File(standaloneDir, scriptName);
        return scriptFile.exists();
    }
    
    /**
     * Checks if a script exists in the importer directory
     * @param baseDir Base directory for scripts
     * @param scriptName Name of the script
     * @return true if script exists, false otherwise
     */
    public static boolean importerScriptExists(String baseDir, String scriptName) {
        File importerDir = new File(baseDir, JythonConstants.IMPORTER_DIR);
        File scriptFile = new File(importerDir, scriptName);
        return scriptFile.exists();
    }
    
    /**
     * Loads module cache timestamps from persistent storage
     * @return Properties containing module cache timestamps
     */
    private static Properties loadModuleCache() {
        Properties props = new Properties();
        try {
            File cacheFile = getModuleCacheFile();
            if (cacheFile.exists()) {
                try (FileInputStream fis = new FileInputStream(cacheFile)) {
                    props.load(fis);
                }
            }
        } catch (IOException e) {
            // Silently ignore - cache will just not be loaded
        }
        return props;
    }
    
    /**
     * Updates module cache with current timestamps for loaded modules
     * @param interp The Python interpreter
     * @param moduleDir The directory containing the modules to cache
     */
    private static void updateModuleCache(PythonInterpreter interp, File moduleDir) {
        try {
            // Determine directory context for cache key prefix
            String dirContext = moduleDir.getName(); // "importer", "fid_builder", "standalone"
            
            // Get current module information from Python
            String getCacheInfoScript =
                "import sys, os\n" +
                "module_path = os.path.abspath(r'" + moduleDir.getAbsolutePath().replace("\\", "\\\\") + "')\n" +
                "cache_info = {}\n" +
                "for name, module in sys.modules.items():\n" +
                "    try:\n" +
                "        file = getattr(module, '__file__', None)\n" +
                "        if file and os.path.abspath(file).startswith(module_path) and os.path.exists(file):\n" +
                "            cache_info[name] = os.path.getmtime(file)\n" +
                "    except Exception:\n" +
                "        pass\n";
            
            interp.exec(getCacheInfoScript);
            
            // Get the cache info from Python
            Object cacheInfoObj = interp.get("cache_info");
            if (cacheInfoObj instanceof org.python.core.PyDictionary) {
                org.python.core.PyDictionary cacheInfo = (org.python.core.PyDictionary) cacheInfoObj;
                
                // Load existing cache
                Properties moduleCache = loadModuleCache();
                
                // Update cache with new timestamps using directory-aware keys
                for (Object keyObj : cacheInfo.keys()) {
                    String moduleName = keyObj.toString();
                    Object timestampObj = cacheInfo.get(keyObj);
                    if (timestampObj != null) {
                        // Create directory-aware cache key: module.importer.mainfile.cached_time
                        String cacheKey = CACHE_KEY_PREFIX + dirContext + "." + moduleName + CACHE_KEY_SUFFIX;
                        moduleCache.setProperty(cacheKey, timestampObj.toString());
                    }
                }
                
                // Save updated cache
                saveModuleCache(moduleCache);
            }
        } catch (Exception e) {
            // Silently ignore - cache will just not be updated
        }
    }
    
    /**
     * Saves module cache timestamps to persistent storage
     * @param moduleCache Properties containing module cache timestamps
     */
    private static void saveModuleCache(Properties moduleCache) {
        try {
            File cacheFile = getModuleCacheFile();
            try (FileOutputStream fos = new FileOutputStream(cacheFile)) {
                moduleCache.store(fos, "PMW790 Plugin Module Cache");
            }
        } catch (IOException e) {
            // Silently ignore - cache will just not be saved
        }
    }
    
    /**
     * Gets the module cache file location in the user's home directory
     * @return File pointing to the module cache file
     */
    private static File getModuleCacheFile() {
        String userHome = System.getProperty("user.home");
        return new File(userHome, MODULE_CACHE_FILE);
    }
    
    // Private constructor to prevent instantiation
    private ScriptExecutor() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}