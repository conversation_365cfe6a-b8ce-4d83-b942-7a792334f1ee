package com.pmw790.jython;

import javax.swing.JOptionPane;
import javax.swing.SwingWorker;
import java.util.List;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import org.python.util.PythonInterpreter;
import com.pmw790.functions.Utilities;

public class ApiManager {
	private final PythonInterpreter interp;
	private final String baseDir;
	private jythonFunctions jythonFunctions;

	public ApiManager(PythonInterpreter interp, String baseDir) {
		this.interp = interp;
		this.baseDir = baseDir;
	}
	
	public void setJythonFunctions(jythonFunctions jythonFunctions) {
		this.jythonFunctions = jythonFunctions;
	}

	public void importSystemApi() {
		String apiUrl = JOptionPane.showInputDialog(null,
				"Enter HTTP API URL:",
				"API URL Input",
				JOptionPane.QUESTION_MESSAGE);

		if (apiUrl != null && !apiUrl.trim().isEmpty()) {
			this.makeApiCall(apiUrl.trim());
		} else {
			JOptionPane.showMessageDialog(null,
					"No URL provided. Operation cancelled.",
					"Input Required",
					JOptionPane.WARNING_MESSAGE);
		}
	}

	public void makeApiCall(String apiUrl) {
		try {
			apiUrl = URLValidator.normalizeUrlForGet(apiUrl);
			URL url = new URL(apiUrl);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();

			conn.setRequestMethod("GET");
			conn.setRequestProperty("Accept", JythonConstants.CONTENT_TYPE_JSON);
			conn.setRequestProperty("User-Agent", JythonConstants.USER_AGENT);
			conn.setConnectTimeout(JythonConstants.DEFAULT_CONNECT_TIMEOUT);
			conn.setReadTimeout(JythonConstants.DEFAULT_READ_TIMEOUT);

			int responseCode = conn.getResponseCode();

			if (responseCode != 200) {
				throw new RuntimeException("HTTP error code: " + responseCode);
			}

			BufferedReader br = new BufferedReader(new InputStreamReader(
					conn.getInputStream(), StandardCharsets.UTF_8));
			StringBuilder response = new StringBuilder();
			String output;

			while ((output = br.readLine()) != null) {
				response.append(output).append("\n");
			}
			br.close();

			File downloadFolder = FileDialogHelper.chooseDirectory(null, "Select Download Folder");
			if (downloadFolder == null) {
				return;
			}

			String userFilename = FileDialogHelper.promptForDownloadFilename(null);
			if (userFilename == null) {
				return;
			}

			String filename = FileDialogHelper.ensureJsonExtension(userFilename);
			File outputFile = new File(downloadFolder, filename);

			if (!FileDialogHelper.confirmOverwrite(null, outputFile)) {
				return;
			}

			try (java.io.FileWriter writer = new java.io.FileWriter(outputFile, StandardCharsets.UTF_8)) {
				writer.write(response.toString());
			}

			// Ask if user wants to immediately import the downloaded data
			int choice = JOptionPane.showOptionDialog(null,
					"Download successful!\n\n" +
							"File: " + outputFile.getAbsolutePath() + "\n" +
							"Size: " + outputFile.length() + " bytes\n\n" +
							"Would you like to import this data now?",
					"Download Complete",
					JOptionPane.YES_NO_OPTION,
					JOptionPane.QUESTION_MESSAGE,
					null,
					new String[]{"Import Now", "Later"},
					"Import Now");

			if (choice == 0 && jythonFunctions != null) {
				// Import the downloaded file immediately
				jythonFunctions.importSystem(downloadFolder.getAbsolutePath());
			}

			conn.disconnect();

		} catch (Exception e) {
			e.printStackTrace();
			JOptionPane.showMessageDialog(null,
					"Download failed: " + e.getMessage(),
					"Error",
					JOptionPane.ERROR_MESSAGE);
		}
	}

	public void sendDataToApi() {
		try {
			String apiUrl = JOptionPane.showInputDialog(null,
					"Enter API endpoint URL:\n\n",
					"API Endpoint Input",
					JOptionPane.QUESTION_MESSAGE);

			if (apiUrl == null || apiUrl.trim().isEmpty()) {
				JOptionPane.showMessageDialog(null,
						"No API URL provided. Operation cancelled.",
						"Input Required",
						JOptionPane.WARNING_MESSAGE);
				return;
			}

			String tempFilename = FileDialogHelper.createTempExportFilename();
			File tempFile = File.createTempFile(tempFilename, JythonConstants.JSON_EXTENSION);

			showProgressDialogAndSendToApi(tempFile, apiUrl.trim());

		} catch (Exception e) {
			e.printStackTrace();
			JOptionPane.showMessageDialog(null,
					"Failed to export and send data: " + e.getMessage(),
					"Export/Send Error",
					JOptionPane.ERROR_MESSAGE);
		}
	}

	private void showProgressDialogAndSendToApi(File tempFile, String apiUrl) {
		BaseProgressDialog progressHelper = new BaseProgressDialog("Exporting and Sending Data");

		SwingWorker<Void, Object[]> exportWorker = new SwingWorker<Void, Object[]>() {
			@Override
			protected Void doInBackground() throws Exception {
				try {
					if (isCancelled()) return null;
					
					publish(new Object[]{10, "Setting up export parameters..."});
					Thread.sleep(JythonConstants.PROGRESS_DELAY_INIT);
					if (isCancelled()) return null;

					if (!ScriptExecutor.standaloneScriptExists(baseDir, JythonConstants.EXPORT_SCRIPT)) {
						throw new RuntimeException(JythonConstants.EXPORT_SCRIPT + " script not found at: " + 
								ScriptExecutor.getStandaloneScriptPath(baseDir, JythonConstants.EXPORT_SCRIPT));
					}

					ScriptExecutor.setupExportToApiParams(interp, tempFile.getAbsolutePath());

					if (isCancelled()) return null;
					publish(new Object[]{30, "Executing export script..."});
					Thread.sleep(JythonConstants.PROGRESS_DELAY_SHORT);
					if (isCancelled()) return null;

					ScriptExecutor.executeStandaloneScript(interp, baseDir, JythonConstants.EXPORT_SCRIPT);

					if (!tempFile.exists() || tempFile.length() == 0) {
						throw new RuntimeException("Export failed - no data generated");
					}

					if (isCancelled()) return null;
					publish(new Object[]{70, "Sending data to API..."});
					Thread.sleep(JythonConstants.PROGRESS_DELAY_MEDIUM);
					if (isCancelled()) return null;

					sendJsonToApiWithUrl(tempFile, apiUrl);

					publish(new Object[]{100, "Data sent successfully!"});
					return null;
				} finally {
					if (tempFile.exists()) {
						tempFile.delete();
					}
				}
			}

			@Override
			protected void process(List<Object[]> chunks) {
				if (!chunks.isEmpty()) {
					Object[] lastChunk = chunks.get(chunks.size() - 1);
					if (lastChunk.length >= 2) {
						int percentage = (Integer) lastChunk[0];
						String message = (String) lastChunk[1];
						progressHelper.setProgressWithStatus(percentage, message);
					}
				}
			}

			@Override
			protected void done() {
				progressHelper.dispose();
				try {
					if (progressHelper.isCancelled()) {
						JOptionPane.showMessageDialog(null,
								"Export/Send operation was cancelled by user.",
								"Operation Cancelled",
								JOptionPane.INFORMATION_MESSAGE);
						return;
					}
					
					get();
				} catch (Exception e) {
					if (!progressHelper.isCancelled()) {
						JOptionPane.showMessageDialog(null,
								"Export/Send failed: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()),
								"Export/Send Error",
								JOptionPane.ERROR_MESSAGE);
					}
				}
			}
		};

		progressHelper.executeTask(exportWorker);
	}

	private void sendJsonToApiWithUrl(File jsonFile, String apiUrl) {
		try {
			apiUrl = URLValidator.validateAndNormalize(apiUrl);
			URL url = new URL(apiUrl);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();

			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", JythonConstants.CONTENT_TYPE_JSON);
			conn.setRequestProperty("Accept", JythonConstants.CONTENT_TYPE_JSON);
			conn.setRequestProperty("User-Agent", JythonConstants.USER_AGENT);
			conn.setRequestProperty("Cache-Control", JythonConstants.CACHE_CONTROL_NO_CACHE);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setConnectTimeout(JythonConstants.API_CONNECT_TIMEOUT);
			conn.setReadTimeout(JythonConstants.API_READ_TIMEOUT);

			byte[] jsonData = Files.readAllBytes(jsonFile.toPath());
			conn.setRequestProperty("Content-Length", String.valueOf(jsonData.length));

			try (OutputStream os = conn.getOutputStream()) {
				os.write(jsonData);
				os.flush();
			}

			int responseCode = conn.getResponseCode();
			String responseMessage = conn.getResponseMessage();

			BufferedReader br = null;
			try {
				if (responseCode >= 200 && responseCode < 300) {
					br = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
				} else {
					br = new BufferedReader(new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8));
				}
			} catch (Exception e) {
				System.out.println("Could not read response stream: " + e.getMessage());
			}

			StringBuilder response = new StringBuilder();
			if (br != null) {
				String output;
				while ((output = br.readLine()) != null) {
					response.append(output).append("\n");
				}
				br.close();
			}

			if (responseCode >= 200 && responseCode < 300) {
				JOptionPane.showMessageDialog(null,
						"✓ Data sent successfully!\n\n" +
								"Data Size: " + jsonFile.length() + " bytes",
						"Send Complete",
						JOptionPane.INFORMATION_MESSAGE);
			} else {
				throw new RuntimeException("HTTP " + responseCode + " " + responseMessage +
						"\nResponse: " + response.toString());
			}

			conn.disconnect();

		} catch (Exception e) {
			e.printStackTrace();
			String errorMessage = URLValidator.getConnectionErrorMessage(apiUrl, e);
			System.err.println("API Error: " + e.getMessage());
			JOptionPane.showMessageDialog(null, errorMessage, "Send Error", JOptionPane.ERROR_MESSAGE);
		}
	}

}