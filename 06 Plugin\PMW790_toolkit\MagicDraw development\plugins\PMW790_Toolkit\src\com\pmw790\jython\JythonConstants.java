package com.pmw790.jython;

public final class JythonConstants {
    
    // Timeouts
    public static final int DEFAULT_CONNECT_TIMEOUT = 10000; // 10 seconds
    public static final int DEFAULT_READ_TIMEOUT = 10000; // 10 seconds
    public static final int API_CONNECT_TIMEOUT = 30000; // 30 seconds
    public static final int API_READ_TIMEOUT = 30000; // 30 seconds
    
    // File extensions
    public static final String JSON_EXTENSION = ".json";
    public static final String PYTHON_EXTENSION = ".py";
    
    // Script names
    public static final String MAIN_SCRIPT = "mainfile.py";
    public static final String EXPORT_SCRIPT = "json_export_.py";
    public static final String FID_BUILDER_SCRIPT = "mainfile.py";
    public static final String UPDATE_IMAGES_SCRIPT = "update_model_images.py";
    
    // Directory names
    public static final String IMPORTER_DIR = "importer";
    public static final String STANDALONE_DIR = "standalone";
    public static final String FID_BUILDER_DIR = "fid_builder";
    
    // HTTP headers
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String USER_AGENT = "MagicDraw-Plugin/1.0";
    public static final String CACHE_CONTROL_NO_CACHE = "no-cache";
    
    // Dialog dimensions
    public static final int PROGRESS_DIALOG_WIDTH = 400;
    public static final int PROGRESS_DIALOG_HEIGHT = 140;
    public static final int CANCEL_BUTTON_WIDTH = 80;
    public static final int CANCEL_BUTTON_HEIGHT = 25;
    
    // Default file names
    public static final String DEFAULT_DOWNLOAD_PREFIX = "AAT_download";
    public static final String CATALOG_FILE_PREFIX = "product_catalog_";
    public static final String TEMP_EXPORT_PREFIX = "magicdraw_export_";
    
    // Date format
    public static final String TIMESTAMP_FORMAT = "yyyyMMdd_HHmmss";
    
    // Progress dialog delays (for smoother UX)
    public static final int PROGRESS_DELAY_INIT = 500;
    public static final int PROGRESS_DELAY_SHORT = 200;
    public static final int PROGRESS_DELAY_MEDIUM = 300;
    
    // Private constructor to prevent instantiation
    private JythonConstants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }
}