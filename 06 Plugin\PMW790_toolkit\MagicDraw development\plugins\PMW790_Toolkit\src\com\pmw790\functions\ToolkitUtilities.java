package com.pmw790.functions;

import com.nomagic.magicdraw.core.Application;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Relationship;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.NamedElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.DirectedRelationship;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.Connector;
import com.nomagic.uml2.impl.ElementsFactory;
import com.nomagic.uml2.ext.jmi.helpers.ModelHelper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

public class ToolkitUtilities {
		
	public static void Log(Object arg) {
		Application.getInstance().getGUILog().log(String.valueOf(arg));
	}
	
	public static List<String> findGeneralizationClasses(Element element){
		List<String> Classifiers = new ArrayList<>();
		Collection<Relationship> RelationshipsCol = element.get_relationshipOfRelatedElement();
		List<Relationship> genRelationships = RelationshipsCol.stream()
			    .filter(rel -> "Generalization".equals(rel.getHumanType()))
			    .collect(Collectors.toList());
		for (Relationship rel : genRelationships) {
			DirectedRelationship dRel = (DirectedRelationship) rel;
			for (Element ex : dRel.getTarget()) {
				NamedElement ix = (NamedElement) ex;
				Classifiers.add(ix.getName());
			}
		}
		return Classifiers;
	}
	
	public static Connector createSimpleConnection(Element connectionOwner,Property sourcePart, Property targetPart) {
		Connector newCon = Application.getInstance().getProject().getElementsFactory().createConnectorInstance();
		ModelHelper.setClientElement(newCon, sourcePart);
		ModelHelper.setSupplierElement(newCon, targetPart);
		newCon.setOwner(connectionOwner);
		newCon.setName("FID L1_"+sourcePart.getName()+"::"+targetPart.getName());
		return newCon;
	}
	
	public static Object lookupProperty(List<Map<String,Object>> list, String name) {
		for (Map<String, Object> map : list) {
			if (name.equals(map.get("name"))) {
				return map.get("partProp");
			}
		}
		return null;
	}
 
	
	public static class PathResult {
		Set<String> endPoints;
		
		public PathResult() {
			this.endPoints = new HashSet<>();
		}
	}
	
	public static PathResult tracePath(String start, Map<String, Set<String>> adjacency, Set<String> connectorNodes, Set<String> visited) {
		if (visited == null) {
			visited = new HashSet<>();
		}
		visited.add(start);
		PathResult result = new PathResult();
		Set <String> neighbors = adjacency.getOrDefault(start, Collections.emptySet());
		for (String neighbor: neighbors) {
			if (visited.contains(neighbor)) {
				continue;
			}
			if (!connectorNodes.contains(neighbor)) {
				result.endPoints.add(neighbor);
			} else {
				PathResult subResult = tracePath(neighbor, adjacency, connectorNodes, visited);  
				result.endPoints.addAll(subResult.endPoints);
				}
			}
		return result;
		}
	
	public static List<String> sortPair(String a, String b){
		if (a.compareTo(b) <= 0) {
			return Arrays.asList(a,b);
		} else {
			return Arrays.asList(b,a);
		}
	}

	public static Map<String, Set<String>> buildClassifierLists(){
		Map<String, Set<String>> classifiers = new HashMap<>();
		Set<String> hubs = new HashSet<>();
		Set<String> conns  = new HashSet<>();;
		Collections.addAll(hubs, "mat1","mat2","mat3","mat4");
		Collections.addAll(conns,"mat5", "Connector","Cable");
		classifiers.put("hubs",hubs);
		classifiers.put("conns", conns);
		
		return classifiers;
	}
	
	public static List<List<String>> extractPairedNames(List<Map<String,Object>> data){
		List<List<String>> namePairs = new ArrayList<>();
		for (Map<String,Object> connector: data) {
			List<Map<String,Object>> connectedElements = (List<Map<String,Object>>) connector.get("connected_elements");
			
		if (connectedElements != null && connectedElements.size() == 2) {
			String node1 = (String) connectedElements.get(0).get("name");
			String node2 = (String) connectedElements.get(1).get("name");
			
			namePairs.add(Arrays.asList(node1,node2));
		}
		}
		return namePairs;
	}
	
	public static boolean isConnectorMap(Map<String, Object> element,Set<String> connClassifiers) {
		List<String> classifier = (List<String>) element.get("classifier");
		if (classifier == null) {
			return false;
		}
		return classifier.stream().anyMatch(connClassifiers::contains);
	}
	
	public static boolean isConnectorStrings(List<String> elementClassifiers,Set<String> connClassifiers) {
		for (String classifier : elementClassifiers) {
			if (connClassifiers.contains(classifier)) {
				return true;
			}
		}
		return false;
	}
	
	public static List<List<String>> allPairsFromGroups(Set<Set<String>> groups){
		List<List<String>> result = new ArrayList<>();
		for (Set<String> group : groups) {
			List<String> endpoints = new ArrayList<>(group);
			for (int i = 0; i < endpoints.size(); i++) {
				for (int j = i +1; j <endpoints.size(); j++) {
					String a = endpoints.get(i);
					String b = endpoints.get(j);
					if (a.compareTo(b) < 0) {
						result.add(Arrays.asList(a,b));
					} else {
						result.add(Arrays.asList(b,a));
					}
				}
			}
		}
		return result;
	}
	
	public static String makePairKey(String name1, String name2) {
		if (name1.compareTo(name2) < 0) {
			return name1+"::"+name2;
		} else {
			return name2 + "::" + name1;
		}
	}
	
	public static String hexifyImage(String imagePath) throws IOException {
		try (InputStream inputStream = ToolkitUtilities.class.getResourceAsStream(imagePath)){
			if (inputStream == null) {
				throw new IOException("Image not found");
			}
			byte[] binaryData = readAllBytesFunc(inputStream);
			return ToolkitUtilities.bytesToHex(binaryData);
			}
	}
	
	public static String bytesToHex(byte[] bytes) {
		StringBuilder hexString = new StringBuilder(2 * bytes.length);
		for (byte b : bytes) {
			String hex = Integer.toHexString(0xff & b);
			if (hex.length() == 1) {
				hexString.append('0');
			}
			hexString.append(hex);
		}
		return hexString.toString();
	}
	
	public static byte[] readAllBytesFunc(InputStream inputStream) throws IOException{
		ByteArrayOutputStream buffer = new ByteArrayOutputStream();
		int bytesRead;
		byte[] data = new byte[1024];
		while ((bytesRead = inputStream.read(data, 0, data.length))!=-1) {
			buffer.write(data,0,bytesRead);
		}
		return buffer.toByteArray();
	}
	
	public static Object getValueByKey(List<Map<String, Object>> list, String matchKey, String matchValue, String returnKey) {
		for (Map<String, Object> map: list) {
			if (matchValue.equals(map.get(matchKey))) {
				return map.get(returnKey);
			}
		}
		return null;
	}
}