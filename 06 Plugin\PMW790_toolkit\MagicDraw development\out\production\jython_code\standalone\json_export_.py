import json, re, os
import sys

from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Class, NamedElement

from com.nomagic.magicdraw.core import Application
from com.nomagic.magicdraw.openapi.uml import ModelElementsManager as mem
from com.nomagic.magicdraw.uml import Finder as f
from com.nomagic.magicdraw.openapi.uml import SessionManager
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Generalization
from com.nomagic.magicdraw.sysml.util import SysMLProfile
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import AggregationKindEnum
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import VisibilityKindEnum
from com.nomagic.magicdraw.openapi.uml import SessionManager as sm
from com.nomagic.uml2.ext.jmi.helpers import StereotypesHelper as sh
from com.nomagic.uml2.ext.magicdraw.mdprofiles import Stereotype
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Package
from com.nomagic.uml2.ext.jmi.helpers import <PERSON><PERSON><PERSON><PERSON> as mh
from com.nomagic.magicdraw.core import Project

#_____________________________________Generic Functions_________________________________________________________

# Python user-defined exception
class InvalidQualifiedNameException(Exception):
    "Raised when the qualified name of the package cannot be found"
    pass

def printer(a, e=None):
    printer = Application.getInstance().getGUILog()
    if e is not None:
        s = str(a) + ': ' + str(e)
        printer.log(str(s))
    else:
        printer.log(str(a))

def convert_to_double(s):
    if re.search(r'\d', s):
        return float(s)
    elif s == '-':
        return None
    return s

def write_to_json(data, filepath):
    try:
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=4)
    except IOError as e:
        printer("Error writing to file: {}".format(e))

def traverseGeneralizationHierarchy(block, visitedBlocks, location):
    if block.getName() in visitedBlocks:
        return
    visitedBlocks[block.getName()] = block

    location_hierarchy = {
        "Site": ["room", "floor", "building", "site"],
        "Building": ["room", "floor", "building"],
        "Floor": ["room", "floor"],
        "Room": ["room"]
    }

    owningBlock = None
    for rel in block.get_relationshipOfRelatedElement():
        if rel.getHumanType() == "Association":
            rel_elements = rel.getMemberEnd()
            for m_e in rel_elements:
                if str(m_e.getAggregation()) == "none":
                    owningBlock = m_e.getType()
                    for gen in owningBlock.get_relationshipOfRelatedElement():
                        if gen.getHumanType() == "Generalization":
                            for classifier in gen.getTarget():
                                classifier_name = classifier.getName()
                                if classifier_name in location_hierarchy:
                                    current_dict = location
                                    for key in location_hierarchy[classifier_name]:
                                        if key not in current_dict:
                                            current_dict[key] = {}
                                        if key == location_hierarchy[classifier_name][-1]:
                                            current_dict[key]['name'] = owningBlock.getName()
                                        current_dict = current_dict[key]
                            traverseGeneralizationHierarchy(owningBlock, visitedBlocks, location)
                            break

def get_block_owned_attribute(block, store_list):
    attribute_name = {}
    attribute_value = {}
    material_type_name = {}
    for attribute in block.getOwnedAttribute():
        port_check = "false"
        for st_ in attribute.getAppliedStereotype():
            if st_.getName() == "PartProperty" or st_.getName() == "ReferenceProperty" or st_.getName() == "IDP Port":
                port_check = "true"
        if port_check == "false":
            attribute_name = attribute.getName()
            try:
                attribute_value = attribute.getDefaultValue().getValue()
            except:
                try:
                    attribute_value = attribute.getInstance().getName()
                except:
                    attribute_value = "-"
            attribute_value = is_none(attribute_value)
            store_list[attribute_name] = attribute_value
    return store_list

def is_number(value):
    if isinstance(value, (str, basestring)):
        try:
            float(value)
            return True
        except ValueError:
            return False
    elif isinstance(value, (int, float)):
        return True
    return False

def is_none(value):
    if isinstance(value, (str, basestring)):
        if value == 'None':
            return None
        elif value == 'True':
            return True
        elif value == 'False':
            return False
    return value

def check_sublist(dict, key, value):
    if "__" in key:
        split_text = key.split("__")
        dict[split_text[0]] = {split_text[1]: (value if not is_number(value) else int(value))}
        dict.pop(key)

def find_material_type_by_id(id):
    if id in connector_dict:
        sbom_value = connector_dict[id]
        if sbom_value in sbom_to_material_type:
            return sbom_to_material_type[sbom_value]
    return None

def get_attributes(role, attribute_type, attribute_name):
    attribute_gen = None
    owner_id = None
    port_att = {}
    attribute_name = role.getName()
    attribute_owner = role.getOwner().getName() if attribute_type == 'IDP Port' else role.getType().getName()
    id_d = re.sub(r'_.*$', '',attribute_owner)
    f_material = str(find_material_type_by_id(id_d))
    if attribute_type == 'IDP Port':
        attribute = 'port'
    elif f_material == 'Connector':
        attribute = 'connector'
    elif f_material == 'Cable':
        attribute = 'cable'
    else:
        attribute = 'none'
    attribute_o_attribute = role.getOwner().getOwnedAttribute() if attribute_type == 'IDP Port' else role.getType().getOwnedAttribute()
    attribute_mat = role.getOwner().getGeneral() if attribute_type == 'IDP Port' else role.getType().getGeneral()
    for ii_tem in attribute_mat:
        attribute_gen = ii_tem.getName()
    for item_o in attribute_o_attribute:
        if item_o.getHumanType() == "Value Property" and item_o.getName() == "id":
            owner_id = item_o.getDefaultValue().getValue()
    if attribute_type == 'IDP Port':
        for port_tag in role.getTaggedValue():
            attr_name = port_tag.tagDefinition.name
            attr_value = port_tag.getValue()[0]
            port_att[attr_name] = attr_value
            for k, v in list(port_att.items()):
                if is_number(v):
                    port_att[k] = int(v)
                check_sublist(port_att, k, v)
    return attribute, attribute_name, attribute_owner,owner_id,port_att,attribute_gen

def find_name(role):
    for stereotype in role.getAppliedStereotype():
        if stereotype.getName() in ['IDP Port', 'PartProperty']:
            return get_attributes(role, stereotype.getName(), role.getName())

def find_key(nested_dict, value):
    for key, val in nested_dict.items():
        if isinstance(val, dict):
            result = find_key(val, value)
            if result:
                return key
        elif val == value:
            return key

##___________________________________PARTS CATALOG_______________________________
def find_part_catalog_usage():
    parts_catalog = f.byQualifiedName().find(project, "Product Catalog")
    if parts_catalog is not None and isinstance(parts_catalog, Package):
        return parts_catalog
    return None

def extract_part_catalog_info(part_catalog):
    catalog_info = []
    for element in part_catalog.getOwnedElement():
        if isinstance(element, Class) and sh.hasStereotype(element, "Block"):
            block_info = {
                "name": element.getName(),
                "attributes": {},
                "material_type": None,
                "port" : {}
            }

            # Process ports
            for port in element.getOwnedPort():
                port_tags = {}
                # Extract all tags from the port
                for tag in port.getTaggedValue():
                    tag_name = tag.tagDefinition.name
                    tag_value = tag.getValue()[0]
                    port_tags[tag_name] = tag_value

                # Add port with its tags to the block info
                block_info["port"][port.getName()] = port_tags

            block_info["attributes"] = get_block_owned_attribute(element, {})

            for gen in element.get_relationshipOfRelatedElement():
                if gen.getHumanType() == "Generalization":
                    for classifier in gen.getTarget():
                        if classifier.getOwner().getName() == "IDP Taxonomy":
                            block_info["material_type"] = classifier.getName()
                            break
                    if block_info["material_type"]:
                        break

            catalog_info.append(block_info)
    return catalog_info

#___________________________________ASSETS_______________________________
def create_export_dictionary_asset(package):
    assets = []
    for item in package.getOwnedMember():
        for st in item.getAppliedStereotype():
            if st.getName() == "Block":
                part_asset = {}
                asset_name = {}
                asset_attributes = get_block_owned_attribute(item, part_asset)
                item_name = item.getName()
                asset_name['name'] = item_name.split('_')[0] if '_' in item_name else item_name
                for k, v in asset_attributes.items():
                    if is_number(v):
                        asset_attributes[k] = int(v) if is_number(v) else v
                    check_sublist(asset_attributes, k, v)
                id_value = asset_attributes.get("id")
                rels = item.get_relationshipOfRelatedElement()
                part = {}
                parent = {}
                location = {}
                for rel in rels:
                    if rel.getHumanType() == "Generalization":
                        part_material = {}
                        classifier_name = {'material_type': {'name':{}}}
                        for classifier in rel.getTarget():
                            c_lassifier = classifier.get_relationshipOfRelatedElement()
                            for rel_gen in c_lassifier:
                                target_end = rel_gen.getTarget()
                                owner_id = target_end[0]
                                if owner_id.getOwner().getName() == "IDP Taxonomy":
                                    classifier_name['material_type']['name'] = owner_id.getName()
                            store_list = get_block_owned_attribute(classifier, part_material)
                            part['material'] = store_list
                            part['material'].update(classifier_name)
                            for k, v in store_list.items():
                                check_sublist(store_list, k, v)
                    if rel.getHumanType() == "Association":
                        rel_elements = rel.getMemberEnd()
                        part_e = None
                        whole_e = None
                        for m_e in rel_elements:
                            if str(m_e.getAggregation()) == "composite":
                                part_e = m_e.getType().getName()
                            if str(m_e.getAggregation()) == "none":
                                whole_e = m_e.getType().getName()
                        if part_e:
                            if item.getName() == part_e:
                                if whole_e:
                                    related_es = rel.getRelatedElement()
                                    for re_s in related_es:
                                        if re_s.getName() == whole_e:
                                            rel_elem = f.byQualifiedName().find(project, re_s.getQualifiedName())
                                            if rel_elem.getOwningPackage().getName() == "Parts View":
                                                for atts in rel_elem.getOwnedAttribute():
                                                    if atts.getName() == "find_no":
                                                        part_parent = str(atts.getDefaultValue().getValue())
                                                        part['find_no'] = part_parent
                                            packages_to_check = [package.getName(), "Unknown"]
                                            for pkg_name in packages_to_check:
                                                if rel_elem.getOwningPackage().getName() == pkg_name:
                                                    for a_atts in rel_elem.getOwnedAttribute():
                                                        if a_atts.getName() == "id":
                                                            asset_parent = str(a_atts.getDefaultValue().getValue())
                                                            parent['parent'] = asset_parent
                                                            break
                                                    if 'parent' in parent:
                                                        break
                                            if rel_elem.getOwningPackage().getName() == "Location View":
                                                room = {}
                                                visitedBlocks = {}
                                                location = {'room': {}}
                                                traverseGeneralizationHierarchy(rel_elem, visitedBlocks, location)
                                                room['name'] = rel_elem.getName()
                                                location['room'].update(room)
                asset = {'asset': {'part': part, 'location': location}}
                asset['asset'].update(parent)
                asset['asset'].update(asset_name)
                asset['asset'].setdefault('parent', None)
                if not asset['asset'].get('location'):
                    asset['asset']['location'] = None
                asset['asset'].update(asset_attributes)
                assets.append(asset)
    main_part = {'name': package.getName(), 'assets': assets}
    return main_part

#___________________________________MACRO SETUP_______________________________
try:
    SessionManager.getInstance().closeSession()
except:
    pass

project = Application.getInstance().getProject()
project_models = project.getModels()

sm.getInstance().createSession("Export Asset Data")

modelElementsManager = mem.getInstance()

#_________________________________DEFINING PACKAGES_________________________
baseModel = Application.getInstance().getProject().getPrimaryModel()

asset_package = f.byQualifiedName().find(project, "Assets View")

connection_package = f.byQualifiedName().find(project, "Connection View")

asset_package_packages=[]
asset_list = []
for item in asset_package.getOwnedMember():
    if isinstance(item,Package):asset_package_packages.append(item.getName())
for package_name in asset_package_packages:
    systems = f.byQualifiedName().find(project, "Assets View::" + package_name)
    asset_list.append(create_export_dictionary_asset(systems))

##_______________________________________________PARTS CATALOG_____________________________________________________
# Check if we should skip parts catalog export
skip_catalog = False
try:
    skip_catalog = skip_parts_catalog
except NameError:
    skip_catalog = False

part_catalog = find_part_catalog_usage()
if part_catalog is not None and not skip_catalog and part_catalog_path and part_catalog_path.strip():
    catalog_info = extract_part_catalog_info(part_catalog)
    write_to_json(catalog_info, part_catalog_path)
elif not skip_catalog:
    printer("Product Catalog package not found")

##_______________________________________________CONNECTORS______________________________________________________
ef = project.getElementsFactory()
connector = ef.createConnectorInstance()

all_assets = [asset for system in asset_list for asset in system['assets']]

connector_dict = {asset['asset']['name']: asset['asset']['part']['material']['sbom']
                  for asset in all_assets
                  if 'material' in asset['asset']['part'] and 'sbom' in asset['asset']['part']['material']}

sbom_to_material_type = {asset['asset']['part']['material']['sbom']: asset['asset']['part']['material']['material_type']['name']
                         for asset in all_assets
                         if 'material' in asset['asset']['part'] and 'sbom' in asset['asset']['part']['material']
                         and 'material_type' in asset['asset']['part']['material']}

def normalize_package_name(name):
    return name.replace('_Singular Connections', '').strip().upper()

def create_export_dictionary_connection(package):
    connections = []
    processed_items = set()
    for i_tem in package.getOwnedMember():
        for st in i_tem.getAppliedStereotype():
            if st.getName() == "Block":
                connection = {}
                cable_found = False
                for st_conns in i_tem.getOwnedConnector():
                    ends = st_conns.getEnd()
                    if len(ends) >= 2:
                        roles = [end.getRole() for end in ends]
                        for item_r in roles:
                            type_r, name_r, owner_r, owner_id, port_att, at_gen = find_name(item_r)
                            if type_r == "port":
                                if not cable_found:
                                    if "source_port" not in connection:
                                        connection["source_port"] = port_att
                                        new_name = name_r.split('_')[0] if '_' in name_r else name_r
                                        connection["source_port"]["name"]=new_name
                                        connection["source"] = {"id": owner_id,"name": owner_r.split('_')[0]}
                                        connection["source_port"]["port_group"]["material"] = {"sbom" : at_gen}
                                else:
                                    connection["target_port"] = port_att
                                    new_name = name_r.split('_')[0] if '_' in name_r else name_r
                                    connection["target_port"]["name"]=new_name
                                    connection["target"] = {"id": owner_id, "name": owner_r.split('_')[0]}
                                    connection["target_port"]["port_group"]["material"] = {"sbom" : at_gen}
                            if type_r == "cable":
                                connection["cable"] = {"id": owner_id,"name": name_r.split('_')[0]}
                                connection["name"] = name_r.split('_')[0] if '_' in name_r else name_r
                                cable_found = True
                            if type_r == "connector":
                                if not cable_found:
                                    if "source_connectors" not in connection:
                                        connection["source_connectors"] = {"id": owner_id,"name": name_r.split('_')[0]}
                                else:
                                    connection["target_connectors"] = {"id": owner_id,"name": name_r.split('_')[0]}
                    if "cable" not in connection:
                        connection["cable"] = None
                        connection["name"] = None
                    if "connection" not in connection:
                        if "target_connectors" not in connection:
                            connection["target_connectors"] = []
                        if "source_connectors" not in connection:
                            connection["source_connectors"] = []
                if not cable_found:
                    connection["target_port"] = port_att
                    new_name = name_r.split('_')[0] if '_' in name_r else name_r
                    connection["target_port"]["name"]=new_name
                    connection["target"] = {"id": owner_id, "name":  owner_r.split('_')[0]}
                    connection["target_port"]["port_group"]["material"] = {"sbom" : at_gen}
                connections.append({"connection": connection})
    return {"connections": connections}

connection_package_packages=[]
connection_list=[]
for item in connection_package.getOwnedMember():
    if isinstance(item,Package):connection_package_packages.append(item.getName())
for package_name in connection_package_packages:
    connection_view = f.byQualifiedName().find(project, "Connection View::" + package_name)
    connection_list.append(create_export_dictionary_connection(connection_view))

connection_dict = {}
for package_name, conn_data in zip(connection_package_packages, connection_list):
    normalized_name = normalize_package_name(package_name)
    connection_dict[normalized_name] = conn_data.get('connections', [])

for asset_package in asset_list:
    package_name = normalize_package_name(asset_package['name'])
    if package_name in connection_dict:
        asset_package["connections"] = connection_dict[package_name]
    else:
        asset_package["connections"] = []

write_to_json(asset_list, export_file_path)

sm.getInstance().closeSession()