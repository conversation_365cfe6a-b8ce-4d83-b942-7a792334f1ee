package com.pmw790.jython;

import javax.swing.*;
import java.awt.*;
import java.util.List;

/**
 * Base class for progress dialogs with consistent styling and behavior
 */
public class BaseProgressDialog {
    private final JDialog progressDialog;
    private final JLabel statusLabel;
    private final JProgressBar progressBar;
    private final JButton cancelButton;
    private volatile boolean cancelled = false;
    private SwingWorker<?, ?> currentWorker;
    private Runnable cancellationCallback;

    public BaseProgressDialog(String title) {
        progressDialog = new JDialog();
        progressDialog.setTitle(title);
        progressDialog.setModal(true);
        progressDialog.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
        progressDialog.setSize(new Dimension(JythonConstants.PROGRESS_DIALOG_WIDTH, JythonConstants.PROGRESS_DIALOG_HEIGHT));
        progressDialog.setLocationRelativeTo(null);
        progressDialog.setLayout(new BorderLayout());

        statusLabel = new JLabel("Initializing...", JLabel.CENTER);
        progressBar = new JProgressBar(0, 100);
        progressBar.setValue(0);
        progressBar.setStringPainted(true);
        progressBar.setString("0%");

        cancelButton = new JButton("Cancel");
        cancelButton.setPreferredSize(new Dimension(JythonConstants.CANCEL_BUTTON_WIDTH, JythonConstants.CANCEL_BUTTON_HEIGHT));
        cancelButton.addActionListener(e -> {
            cancelled = true;
            if (currentWorker != null && !currentWorker.isDone()) {
                currentWorker.cancel(true);
            }
            // Call the cancellation callback if set
            if (cancellationCallback != null) {
                cancellationCallback.run();
            }
            progressDialog.dispose();
        });

        setupLayout();
    }

    private void setupLayout() {
        // Create a bottom panel to contain both progress bar and cancel button
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.add(progressBar, BorderLayout.CENTER);
        
        // Create a panel for the cancel button with some padding
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.add(cancelButton);
        bottomPanel.add(buttonPanel, BorderLayout.EAST);

        progressDialog.add(statusLabel, BorderLayout.CENTER);
        progressDialog.add(bottomPanel, BorderLayout.SOUTH);
    }

    /**
     * Updates the status text directly (deprecated - use setProgressWithStatus instead)
     * @param status The status message to display
     */
    @Deprecated
    public void updateStatus(String status) {
        if (!cancelled) {
            SwingUtilities.invokeLater(() -> statusLabel.setText(status));
        }
    }

    /**
     * Executes the given SwingWorker and shows the progress dialog
     * @param worker The SwingWorker to execute
     */
    public void executeTask(SwingWorker<?, ?> worker) {
        this.currentWorker = worker;
        worker.execute();
        progressDialog.setVisible(true);
    }

    /**
     * Closes and disposes the progress dialog
     */
    public void dispose() {
        SwingUtilities.invokeLater(() -> progressDialog.dispose());
    }

    /**
     * Returns whether the operation was cancelled by the user
     * @return true if cancelled, false otherwise
     */
    public boolean isCancelled() {
        return cancelled;
    }

    /**
     * Sets a callback to be executed when the operation is cancelled
     * @param callback The callback to execute on cancellation
     */
    public void setCancellationCallback(Runnable callback) {
        this.cancellationCallback = callback;
    }

    /**
     * Sets the progress bar with a specific percentage value
     * @param value Progress value (0-100)
     */
    public void setProgress(int value) {
        SwingUtilities.invokeLater(() -> {
            progressBar.setValue(Math.max(0, Math.min(100, value)));
            progressBar.setString(value + "%");
        });
    }

    /**
     * Sets the progress bar with a specific percentage value and custom status message
     * @param value Progress value (0-100)
     * @param status Custom status message to display
     */
    public void setProgressWithStatus(int value, String status) {
        SwingUtilities.invokeLater(() -> {
            progressBar.setValue(Math.max(0, Math.min(100, value)));
            progressBar.setString(value + "%");
            statusLabel.setText(status);
        });
    }

    /**
     * Gets the parent dialog for positioning child dialogs
     * @return The progress dialog
     */
    public JDialog getDialog() {
        return progressDialog;
    }
}